import React, { useEffect, useState } from 'react';
import { Image as AntdImage } from 'antd';

type Props = {
  src: string;
  size?: number;
};

/**
 * 对 Image 的简单封装，如果 src 加载失败会显示默认图片
 * @param props
 * @constructor
 */
const FallbackImage: React.FC<Props> = (props) => {
  const { src, size = 24 } = props;
  const [url, setUrl] = useState(
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAy5JREFUaEPNmm1OwzAMhruTwY7VX7BfORbjZCBHcXFcO/5ICkxCGlub+rFfO06y27boVUp53bYN/l7akPAeX8/25hM/2Pf9fcWjbzODNKPfmuGZoR5w0wxMCqCUAt4Dw1e+HhmQEMBFhnMnhEBcAAmpgOZpDoBMbs0BYDDkSfc9o3ju+373hNcEaMZ/OAYDo8F7NWFLKXAPGil6lQBpcrzjeNrzhwABydQHASwBAOMBwvSmATKUlAoQMB6cQyVzGEyBHBGEqGnFQYUQAYLGTyUhv3mQb6KcNIAvj8faNThJYU3H/wNDnC+VnAiFgF95+iDo/VDJixKxQlClyqtTB/CfjEdYAaKTEgfwSmeZ54nmwWbeP0HvBJKkZbyLwgHwF94PPpMq8HAgBeDeh6SkEwyWSrOue7Q+YXwdHhO6AmgZT/SHExXUaQCYrjSlFK9cNX9UmxCATvtwQw1RAwBaV1/i8TxJzlmAqgQE6AbD8JA+aFnStohjmxFhPl0LdmKH2GmdehxDLU0i2acHGkTrEXcJoPM2yQ+QEYLWpWFmAbJQQlXqACDqn6IbHkvJS3iu5W3p+6cEIDZNjqoRBnGMaUFVADGBAxHgDwmBTM4HlwAgUAiE5AZUKPdOxxUR4BExl4WSTrzy+g2AY9q3BM1ky4vL5Ums2peZQ5xzRagKZTezZnLB2g2p8wA3TO02kxXjcgDelwzb5eAElDJe65AFjT7EZm7btmHlcFaIqXWDJ9q1mWu0ZjvBKoQnH1LlM9Ar1egiwKm9HVUOR4VISyfQbv8AtJtOS8pRtznIhSnjvfrvlpSDm1QZaBrN1HyenCzCuHylOxbnRb0ShWhFmkpcpaU45Rt1kmdjKwJxBcBQ2tLWotSDDHVN82GFhEgl4raMtxaNCuCFmCqfA+PFplDbndZ2DSyI95l1suFA//a6o5Qt8bJQfdYccJAwjmbc6XrvXIXljpicEHBZ+rDacfppRto8pSS69KxTK0x7HXuozVD8HH+O8DvHrIkmTl2ZOb8wvU7HcUUgkGxOG8XLUjmVAmD5YZ26j6C6w/EM/RSAIC/4aPRzG/i+HhutOGOAwb4BxcsvRAav5fwAAAAASUVORK5CYII=',
  );

  useEffect(() => {
    const img = new Image();
    img.onload = () => {
      setUrl(src);
    };
    img.src = src;
  }, [src]);

  return <AntdImage width={size} height={size} preview={false} src={url} />;
};

export default FallbackImage;
