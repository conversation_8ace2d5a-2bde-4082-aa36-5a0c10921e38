import DMModal from '@/components/Common/Modal/DMModal';
import { useMemo, useRef, useState } from 'react';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import { useLockFn } from 'ahooks';
import { GhostModalCaller } from '@/mixins/modal';
import styles from './style.less';
import { Button, Checkbox, Col, Form, Image, Input, Row, Space, Tooltip, Typography } from 'antd';
import CopyableText from '@/components/Common/CopyableText';
import IconFontIcon from '@/components/Common/IconFontIcon';
import modalStyles from '@/style/modal.less';
import DMFormItem from '@/components/Common/DMFormItem';
import { trimValues } from '@/utils/utils';
import InfoTip from '@/components/Tips/InfoTip';
import I18N from '@/i18n/I18N';
import buttonStyles from '@/style/button.less';
import { tkProductByIdRemarkPut, tkProductPageGet } from '@/services/api-TKAPI/TkProductController';
import DMConfirm from '@/components/Common/DMConfirm';
import { rpaTaskManualTriggerTaskPost } from '@/services/api-RPAAPI/RpaTaskController';
import moment from 'moment';
import { showRunTaskSuccessAlert } from '@/pages/Rpa/components/RunRpaTaskModal';
import querystring from 'querystring';
import { rpaGetRpaFlowByBizCodeGet } from '@/services/api-RPAAPI/RpaController';
import TkFlowBizCode from '@/pages/RunFlow/utils/TkFlowBizCode';
import { useRequest } from '@@/plugin-request/request';
import { sendAsync } from '@/utils/ElectronUtils';

function useProductSync() {
  return useRequest(
    ({ shopId, deviceId }) => {
      DMConfirm({
        width: 460,
        title: I18N.t('是否立即同步当前店铺的商品列表'),
        content: I18N.t(
          '系统会触发一个自动化流程，帮您执行同步操作，您可在同步完成后，重新选择商品',
        ),
        okText: I18N.t('立即同步'),
        onOk() {
          const bizCode = TkFlowBizCode.同步店铺商品;
          rpaGetRpaFlowByBizCodeGet({
            bizCode,
          }).then((_res) => {
            const flow = _res.data!;
            rpaTaskManualTriggerTaskPost({
              formId: 'from-browser',
              deviceId,
              runOnCloud: false,
              concurrent: 1,
              concurrentDelay: 10,
              caseBrowserOpened: 'reuse',
              closeBrowserOnEnd: 'keep',
              showMouseTrack: true,
              description: `${I18N.t('执行任务：')}${flow!.name}`,
              forceRecord: false,
              name: `${flow!.name}_${moment().format('YYYYMMDDHHmmSS')}`,
              rpaFlowId: flow!.id,
              shopIds: [shopId],
              snapshot: 'OnFail',
              sscToken: querystring.parse(location?.href)?.sscToken || '',
              params: {},
            }).then((res) => {
              const rpaTaskVo = res.data ?? {};
              showRunTaskSuccessAlert({
                teamId: rpaTaskVo.teamId,
                taskId: rpaTaskVo.id,
                runOnCloud: rpaTaskVo.runOnCloud,
                isConsole: rpaTaskVo.console,
                headless: rpaTaskVo.headless,
                hideDetailBtn: true,
              });
            });
          });
        },
      });
    },
    {
      manual: true,
    },
  );
}
const FallbackImage =
  'data:image/png;base64,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';

const Description = (props: {
  data: API.TkProductDto;
  onUpdate: (remark?: string) => void;
  shopId: number;
}) => {
  const { data, onUpdate, shopId } = props;
  const [visible, changeVisible] = useState(true);
  const [form] = Form.useForm();
  return (
    <DMModal
      visible={visible}
      onCancel={() => {
        changeVisible(false);
      }}
      className={modalStyles.headlessModal}
      onOk={form.submit}
      bodyStyle={{
        paddingBottom: 0,
      }}
    >
      <Form
        requiredMark={false}
        form={form}
        onFinish={(vals) => {
          const values = trimValues(vals);
          const { remark = '' } = values;
          tkProductByIdRemarkPut({
            remark,
            id: data.id!,
            shopId,
          }).then(() => {
            onUpdate(remark);
            changeVisible(false);
          });
        }}
      >
        <DMFormItem
          disableLabelMuted
          label={I18N.t('请输入备注')}
          name={'remark'}
          initialValue={data.tocRemark || ''}
          rules={[
            {
              validator(rule, value) {
                const val = (value || '').trim();
                if (!val) {
                  return Promise.reject(new Error(I18N.t('请输入备注')));
                }
                if (val.length > 256) {
                  return Promise.reject(new Error(I18N.t('备注长度不能超过256')));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input.TextArea autoFocus style={{ resize: 'none', height: 100 }} />
        </DMFormItem>
      </Form>
    </DMModal>
  );
};

const ProductList = (props: {
  shopId: number;
  onSubmit: (ids: string[]) => void;
  selectedIds?: string[];
  deviceId?: string;
}) => {
  const { shopId, selectedIds, onSubmit, deviceId } = props;
  const [visible, changeVisible] = useState(true);
  const actionRef = useRef<ActionType>();
  const [ids, changeIds] = useState(selectedIds || []);
  const { run: syncProduct, loading } = useProductSync();
  const footer = useMemo(() => {
    return (
      <Row className={modalStyles.footer}>
        <Col>
          <InfoTip message={I18N.t('单次计划最多添加100个商品，建议不超过50个')} />
        </Col>
        <Col>
          <Space>
            <Button
              className={buttonStyles.successBtn}
              loading={loading}
              onClick={() => {
                syncProduct({
                  shopId,
                  deviceId,
                });
                changeVisible(false);
              }}
            >
              {I18N.t('同步店铺商品')}
            </Button>
            <Button
              type={'primary'}
              onClick={() => {
                onSubmit(ids);
                changeVisible(false);
              }}
            >
              {I18N.t('确定')}
            </Button>
            <Button
              onClick={() => {
                changeVisible(false);
              }}
            >
              {I18N.t('取消')}
            </Button>
          </Space>
        </Col>
      </Row>
    );
  }, [ids, onSubmit]);
  return (
    <DMModal
      width={720}
      footer={footer}
      bodyStyle={{
        paddingTop: 0,
        paddingBottom: 0,
      }}
      title={I18N.t('选择商品')}
      visible={visible}
      onCancel={() => {
        changeVisible(false);
      }}
    >
      <div className={styles.tkProductTable}>
        <ProTable<API.TkProductDto>
          size={'small'}
          actionRef={actionRef}
          {...scrollProTableOptionFn({
            pageId: 'tkProductTable',
            pagination: {
              hideOnSinglePage: false,
              size: 'small',
            },
          })}
          rowKey={'productNo'}
          columns={[
            {
              title: I18N.t('商品图'),
              dataIndex: 'thumbUrl',
              width: '90px',
              render(_, record) {
                const { thumbUrl } = record;
                return (
                  <Image
                    src={thumbUrl || FallbackImage}
                    preview={!!thumbUrl}
                    width={60}
                    height={60}
                  />
                );
              },
            },
            {
              title: I18N.t('商品名称'),
              width: '300px',
              dataIndex: 'name',
              render(_dom, record) {
                const { name, productNo } = record;
                return (
                  <div
                    style={{
                      overflow: 'hidden',
                      width: '100%',
                      height: 60,
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Typography.Paragraph
                      style={{ marginBottom: 0, lineHeight: 1.2 }}
                      ellipsis={{ rows: 2, expandable: false, tooltip: name }}
                    >
                      {name}
                    </Typography.Paragraph>
                    <div style={{ fontSize: 12, color: '#999' }}>
                      {I18N.t('商品ID：')}

                      <CopyableText text={productNo} type={I18N.t('商品ID')}>
                        {productNo}
                      </CopyableText>
                    </div>
                  </div>
                );
              },
            },
            {
              title: I18N.t('备注'),
              dataIndex: 'tocRemark',
              ellipsis: true,
            },
            {
              title: false,
              dataIndex: 'option',
              valueType: 'option',
              width: '45px',
              render(_dom, record) {
                return (
                  <Typography.Link
                    onClick={() => {
                      GhostModalCaller(
                        <Description
                          data={record}
                          shopId={shopId}
                          onUpdate={() => {
                            actionRef.current?.reload();
                          }}
                        />,
                      );
                    }}
                  >
                    <IconFontIcon iconName="edit_24" />
                  </Typography.Link>
                );
              },
            },
          ]}
          rowSelection={{
            selectedRowKeys: ids,
            onChange(ks) {
              changeIds(ks);
            },
            renderCell(value, record, index, node) {
              if (node?.props?.disabled) {
                return (
                  <Tooltip title={I18N.t('您已选择了该商品')}>
                    <span>
                      <Checkbox checked disabled />
                    </span>
                  </Tooltip>
                );
              }
              return node;
            },
            getCheckboxProps(record) {
              return { disabled: selectedIds?.includes(record.productNo) };
            },
          }}
          request={(params) => {
            const { current, pageSize } = params;
            return tkProductPageGet({
              shopId,
              pageSize,
              pageNum: current,
            }).then((res) => {
              return {
                data: res?.data?.list || [],
                total: res.data?.total,
              };
            });
          }}
        />
      </div>
    </DMModal>
  );
};
export default ProductList;

export function useTkProductList() {
  const { run: syncProduct } = useProductSync();
  return useLockFn(
    (options: {
      shop: API.ShopDetailVo;
      selected?: string[];
      onSubmit: (ids: string[]) => void;
    }) => {
      return new Promise<void>(async (resolve) => {
        const { shop, selected, onSubmit } = options;
        const shopId = shop.id!;
        // 获取商品列表的数量,有就显示,没有就提示
        const count = await tkProductPageGet({ shopId, pageSize: 1, pageNum: 1 }).then((res) => {
          return res.data?.total || 0;
        });
        const deviceId = await sendAsync('get-device-id');

        if (!count) {
          syncProduct({
            shopId: shopId!,
            deviceId,
          });
          resolve();
          return;
        }
        GhostModalCaller(
          <ProductList
            deviceId={deviceId}
            shopId={shopId}
            onSubmit={onSubmit}
            selectedIds={selected}
          />,
        );
        resolve();
      });
    },
  );
}
