import DMModal from '@/components/Common/Modal/DMModal';
import type { CSSProperties } from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import type { ActionType } from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import {
  Button,
  Checkbox,
  Col,
  ConfigProvider,
  Empty,
  Form,
  Input,
  message,
  Row,
  Space,
  Tooltip,
  Typography,
  Image as AntImage,
  Alert,
} from 'antd';
import CopyableText from '@/components/Common/CopyableText';
import modalStyles from '@/style/modal.less';
import DMFormItem from '@/components/Common/DMFormItem';
import { dateFormat, trimValues } from '@/utils/utils';
import InfoTip from '@/components/Tips/InfoTip';
import I18N from '@/i18n/I18N';
import {
  rpaTaskGetLatestTaskItemGet,
  rpaTaskManualTriggerTaskPost,
} from '@/services/api-RPAAPI/RpaTaskController';
import { rpaGetRpaFlowByBizCodeGet } from '@/services/api-RPAAPI/RpaController';
import moment from 'moment';
import _ from 'lodash';
import ProTable from '@ant-design/pro-table';
import {
  tkshopProductPageGet,
  tkshopProductByIdRemarkPut,
} from '@/services/api-TKShopAPI/TkshopProductController';
import styled from 'styled-components';
import { getDeviceId } from '@/utils/ElectronUtils';
import DMConfirm from '@/components/Common/DMConfirm';
import SortDropdown, { useOrder } from '@/components/Sort/SortDropdown';
import { useLocalStorageState } from '@umijs/hooks';
import ColumnDropdown from '@/components/ColumnDropdown';
import Placeholder, { OptionPlaceholder } from '@/components/Common/Placeholder';
import type { SelectorProps } from '@/components/Common/Selector';
import Selector from '@/components/Common/Selector';
import useResizableColumns from '@/hooks/useResizableColumns';
import { GhostModalCaller } from '@/mixins/modal';
import IconFontIcon from '@/components/Common/IconFontIcon';

const ProductStatus = ['Deactivated', 'Deleted', 'Draft', 'Live', 'Revieweing', 'Suspended'];
export const ProductStatusSelector = (props: SelectorProps) => {
  return (
    <Selector
      placeholder={<OptionPlaceholder type={'status'} />}
      options={ProductStatus.map((item) => {
        return {
          label: item,
          value: item,
        };
      })}
      {...props}
    />
  );
};
export function formatNumber(
  num: number | undefined,
  options?: {
    fractionDigits?: number;
    unit?: any;
    unitPosition?: 'prefix' | 'suffix';
    threshold?: number;
  },
) {
  if (!_.isNumber(num)) {
    return 0;
  }
  const {
    fractionDigits = 2,
    unit = '',
    unitPosition = 'prefix',
    threshold = 1e10,
  } = options || {};
  let count;
  if (num >= threshold && unit !== '%') {
    if (num >= 1e9) {
      count = `${(num / 1e9).toFixed(fractionDigits)}B`; // 十亿
    } else if (num >= 1e6) {
      count = `${(num / 1e6).toFixed(fractionDigits)}M`; // 百万
    } else if (num >= 1e3) {
      count = `${(num / 1e3).toFixed(fractionDigits)}K`; // 千
    } else {
      count = num?.toFixed(fractionDigits);
      count = _.toNumber(count).toLocaleString();
    }
  } else {
    count = num?.toFixed(fractionDigits);
    count = _.toNumber(count).toLocaleString();
  }
  if (!_.isNil(unit) && num !== 0) {
    return unitPosition === 'prefix' ? `${unit}${count}` : `${count}${unit}`;
  }
  return count || 0;
}
export const getNumberDom = (value?: number, _unit?: string) => {
  if (_.isNil(value)) {
    return <Placeholder />;
  }
  const inner = formatNumber(value!, {
    unit: _unit,
  });
  return (
    <Typography.Text
      style={{ color: 'inherit', cursor: 'inherit' }}
      ellipsis={{
        tooltip: inner,
      }}
    >
      {inner}
    </Typography.Text>
  );
};

const StyleImage = styled.div<{ loaded: boolean }>`
  display: inline-flex;
  vertical-align: top;
  align-items: center;
  justify-content: center;
  border: 1px solid #dddddd;
  box-sizing: content-box;
  overflow: hidden;
  .ant-image {
    display: block;
    width: 100%;
    height: 100%;
  }
  img {
    display: block;
    width: 100%;
    height: 100%;
    ${(props) => {
      return `transform:scale(${props.loaded ? '1' : '0.66'})`;
    }}
  }
`;
export const FallbackImage = (props: {
  src?: string;
  size?: [number, number];
  style?: CSSProperties;
  boxShadow?: boolean;
}) => {
  const { src, size = [32, 32], boxShadow, style, ...rest } = props;
  const [loaded, setLoaded] = useState(false);
  const showPreviewIcon = useMemo(() => {
    return loaded;
  }, [loaded]);
  const _style: CSSProperties = useMemo(() => {
    return {
      width: size[0],
      height: size[1],
      boxShadow: boxShadow && loaded ? '0 0 5px 0 rgba(0,0,0.05)' : 'none',
      ...(style || {}),
    };
  }, [boxShadow, loaded, size, style]);
  useEffect(() => {
    let img: HTMLImageElement | null = null;
    img = new Image();
    img.onload = () => {
      setLoaded(true);
    };
    img.onerror = () => {
      setLoaded(false);
    };
    img.src = src || '';
    return () => {
      if (img) {
        img = null;
      }
    };
  }, [src]);
  return (
    <StyleImage
      loaded={loaded}
      className={'fallback-image'}
      style={_style}
      onClick={(e) => {
        if (showPreviewIcon) {
          e.stopPropagation();
        }
      }}
      {...rest}
    >
      <AntImage preview={showPreviewIcon} src={loaded ? src : '/404.svg'} />
    </StyleImage>
  );
};

const Description = (props: {
  data: API.TkshopProductDto;
  onUpdate: (remark?: string) => void;
}) => {
  const { data, onUpdate } = props;
  const [visible, changeVisible] = useState(true);
  const [form] = Form.useForm();
  return (
    <DMModal
      visible={visible}
      onCancel={() => {
        changeVisible(false);
      }}
      className={modalStyles.headlessModal}
      onOk={form.submit}
      bodyStyle={{
        paddingBottom: 0,
      }}
    >
      <Form
        requiredMark={false}
        form={form}
        onFinish={(vals) => {
          const values = trimValues(vals);
          const { remark = '' } = values;
          tkshopProductByIdRemarkPut({
            remark,
            id: data.id!,
          }).then(() => {
            onUpdate(remark);
            changeVisible(false);
          });
        }}
      >
        <DMFormItem
          disableLabelMuted
          label={'请输入备注'}
          name={'remark'}
          initialValue={data.remark || ''}
          rules={[
            {
              validator(rule, value) {
                const val = (value || '').trim();
                if (val.length > 1000) {
                  return Promise.reject(new Error('备注长度不能超过1000'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input.TextArea autoFocus style={{ resize: 'none', height: 100 }} />
        </DMFormItem>
      </Form>
    </DMModal>
  );
};

const BIZ_CODE = 'tkshop.TS_SyncProducts';
export function useProductSyncRequest() {
  const time = useRef<number>();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<API.RpaTaskItemVo & { productCount: number }>();

  const submit = (shopId: number, deviceId = getDeviceId()) => {
    return new Promise<API.RpaTaskItemVo & { productCount: number }>(async (resolve, reject) => {
      const fetchTask = () => {
        rpaTaskGetLatestTaskItemGet({
          bizCode: BIZ_CODE,
          shopId: shopId!,
        })
          .then((res) => {
            const task_item = res.data!;
            if (!task_item) {
              setTimeout(() => {
                fetchTask();
              }, 5000);
              return;
            }
            if (
              task_item.status === 'Running' ||
              task_item.status === 'Scheduling' ||
              task_item.status === 'Scheduled' ||
              task_item.status === 'NotStart' ||
              new Date(task_item?.executeEndTime || Date.now()).getTime() < time.current!
            ) {
              setTimeout(() => {
                fetchTask();
              }, 5000);
            } else {
              tkshopProductPageGet({
                shopIds: [shopId],
                pageNum: 1,
                pageSize: 1,
              })
                .then((_res) => {
                  const productCount = _res.data?.total || 0;
                  const _result = {
                    ...task_item,
                    productCount,
                  };
                  message.success(`为您同步了 ${productCount} 个商品`);
                  setResult(_result);
                  resolve(_result);
                  setLoading(false);
                })
                .catch(() => {
                  setLoading(false);
                  reject();
                });
            }
          })
          .catch((e) => {
            reject(e);
          });
      };
      setLoading(true);
      const flow = await rpaGetRpaFlowByBizCodeGet({
        bizCode: BIZ_CODE,
      }).then((res) => {
        return res.data!;
      });
      if (!flow) {
        DMConfirm({
          type: 'info',
          title: I18N.t('未找到可用的自动化流程'),
        });
        reject();
        return;
      }
      time.current = Date.now();
      await rpaTaskManualTriggerTaskPost({
        rpaFlowId: flow.id!,
        deviceId: deviceId || getDeviceId(),
        concurrent: 5,
        shopIds: [shopId!],
        name: `${flow.name}_${moment().format('YYYYMMDDHHmm')}`,
        params: {},
        forceRecord: false,
        snapshot: 'OnFail',
        caseBrowserOpened: 'reuse',
        closeBrowserOnEnd: 'closeRpaBrowser',
      })
        .catch(() => {
          setLoading(false);
        })
        .then(() => {
          fetchTask();
          message.success(I18N.t('任务调度已传递给您指定的设备'));
        });
    });
  };

  return {
    loading,
    run: submit,
    data: result,
  };
}
const StyledDiv = styled.div`
  height: 560px;
  overflow: hidden;
  border: 1px solid #ddd;
  border-bottom: none;
  > div {
    height: 100%;
    overflow: hidden;
  }
  .ant-table-row {
    td {
      height: 77px;
      overflow: hidden;
    }
  }
`;
type ProductColumn = keyof API.TkshopProductDto | 'option';
const TkShopProductListModal = (props: {
  shopId: number;
  onSubmit: (ids: string[]) => void;
  selectedIds?: string[];
  deviceId?: string;
}) => {
  const { shopId, selectedIds, onSubmit, deviceId } = props;
  const [visible, changeVisible] = useState(true);
  const [total, setTotal] = useState(1);
  const [selected, setSelected] = useState<string[]>(() => {
    return _.uniq(selectedIds || []).filter(Boolean);
  });
  const actionRef = useRef<ActionType>();
  const { run, loading } = useProductSyncRequest();
  const [data, setData] = useState<API.TkshopProductDto[]>([]);
  const ids = useMemo(() => {
    return data
      .filter((item) => {
        return selected?.includes(item.productNo!);
      })
      .map((item) => {
        return item.productNo!;
      });
  }, [data, selected]);
  const { order, changeOrder } = useOrder(
    {
      key: 'updateTime',
      ascend: false,
    },
    'tkshop-product-list-order_v0425',
  );
  const ProductMetaColumns: any[] = useMemo(() => {
    return [
      {
        title: '商品图',
        dataIndex: 'productAvatar',
        disabled: true,
        width: 80,
        render(_text, record) {
          const { productAvatar } = record;
          return <FallbackImage src={productAvatar} size={[60, 60]} />;
        },
      },
      {
        title: '商品名称/ID',
        resizable: {
          minWidth: 220,
        },
        disabled: true,
        sortable: {},
        dataIndex: 'productName',
        render(_dom, record) {
          const { productName, productNo } = record;
          return (
            <div
              style={{
                overflow: 'hidden',
                width: '100%',
                height: 60,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
              }}
            >
              <Typography.Paragraph
                style={{ marginBottom: 0, lineHeight: 1.2 }}
                ellipsis={{ rows: 2, expandable: false, tooltip: productName }}
              >
                {productName}
              </Typography.Paragraph>
              <div style={{ fontSize: 12, color: '#999' }}>
                商品ID：
                <CopyableText text={productNo} type={'商品ID'}>
                  {productNo}
                </CopyableText>
              </div>
            </div>
          );
        },
      },
      {
        title: I18N.t('库存'),
        dataIndex: 'quantity',
        sortable: {},
        width: 70,
        renderText(_dom, record) {
          const { quantity } = record;
          return getNumberDom(quantity);
        },
      },
      {
        title: I18N.t('销量'),
        dataIndex: 'itemsSold',
        sortable: {},
        width: 70,
        renderText(_dom, record) {
          const { itemsSold } = record;
          return getNumberDom(itemsSold);
        },
      },
      {
        title: I18N.t('价格'),
        dataIndex: 'price',
        sortable: {},
        width: 95,
        renderText(_dom, record) {
          const { price, priceUnit } = record;
          return getNumberDom(price, priceUnit);
        },
      },
      {
        title: I18N.t('更新日期'),
        width: 95,
        dataIndex: 'updateTime',
        render(_dom, record) {
          const { updateTime } = record;
          return updateTime ? dateFormat(new Date(updateTime), 'MM-DD HH:mm') : <Placeholder />;
        },
        sortable: {},
      },
      {
        // Suspended 是最长的,100就是为了避免被裁剪掉
        title: I18N.t('状态'),
        dataIndex: 'status',
        sortable: {},
        width: 100,
      },
      {
        title: '备注',
        dataIndex: 'remark',
        resizable: {
          minWidth: 120,
        },
        render(_dom, record) {
          const { remark } = record;
          if (remark) {
            return <Typography.Text ellipsis={{ tooltip: remark }}>{remark}</Typography.Text>;
          }
          return <Placeholder />;
        },
      },
      {
        title: false,
        dataIndex: 'option',
        valueType: 'option',
        width: 45,
        onCell() {
          return {
            onClick(e) {
              e.stopPropagation();
            },
          };
        },
        render(_dom, record) {
          return (
            <Typography.Link
              onClick={() => {
                GhostModalCaller(
                  <Description
                    data={record}
                    onUpdate={() => {
                      actionRef.current?.reload();
                    }}
                  />,
                );
              }}
            >
              <IconFontIcon iconName="edit_24" />
            </Typography.Link>
          );
        },
      },
    ];
  }, []);

  const [form] = Form.useForm();
  const [columns, setColumns] = useLocalStorageState<ProductColumn[]>(
    'tkshop-product-list-columns_v0618',
    ['productAvatar', 'productName', 'status', 'remark', 'option'],
  );
  const footer = useMemo(() => {
    return (
      <Row className={modalStyles.footer}>
        <Col>
          <InfoTip message={I18N.t('单次计划最多添加100个商品，建议不超过50个')} />
        </Col>
        <Col>
          <Space>
            {total !== 0 && !loading && (
              <Button
                disabled={selected.length > 100}
                type={'primary'}
                onClick={() => {
                  onSubmit(selected);
                  changeVisible(false);
                }}
              >
                {I18N.t('确定')}
              </Button>
            )}
            <Button
              onClick={() => {
                changeVisible(false);
              }}
            >
              {I18N.t('取消')}
            </Button>
          </Space>
        </Col>
      </Row>
    );
  }, [loading, onSubmit, selected, total]);
  const columnsForShow: any[] = useMemo(() => {
    return (
      ProductMetaColumns.filter(({ dataIndex }) => {
        return columns.includes(dataIndex as unknown as ProductColumn);
      }) || []
    );
  }, [ProductMetaColumns, columns]);
  const {
    columns: tableColumns,
    header,
    tableWidth,
    isInitialized,
  } = useResizableColumns({
    fixWidth: 32,
    scope: 'tk-product-list-modal',
    columns: columnsForShow,
    order,
    changeOrder,
  });
  useEffect(() => {
    actionRef.current?.reloadAndRest?.();
  }, [order]);
  return (
    <DMModal
      width={860}
      footer={footer}
      bodyStyle={{
        paddingTop: 8,
        paddingBottom: 0,
        display: 'flex',
        flexDirection: 'column',
        gap: 8,
      }}
      title={'选择商品'}
      visible={visible}
      onCancel={() => {
        changeVisible(false);
      }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          flexWrap: 'nowrap',
          gap: 16,
        }}
      >
        <div style={{ flex: 1 }}>
          <Alert
            showIcon
            style={{
              padding: '4px 8px',
            }}
            message={
              <div>
                <span>{I18N.t('商品展示不全？')}</span>
                <Typography.Link
                  style={{ marginLeft: 24 }}
                  onClick={() => {
                    run(shopId, deviceId).then(() => {
                      actionRef.current?.reloadAndRest?.();
                    });
                  }}
                >
                  {I18N.t('立即同步')}
                </Typography.Link>
              </div>
            }
          />
        </div>
        <Form style={{ display: 'flex', overflow: 'hidden', gap: 8 }} form={form}>
          <Form.Item name={'query'} noStyle>
            <Input.Search
              allowClear
              style={{ width: 240 }}
              onSearch={(v) => {
                form.setFieldsValue({ query: v });
                actionRef.current?.reloadAndRest?.();
              }}
              placeholder={I18N.t('根据商品名称或ID检索')}
            />
          </Form.Item>
          <Form.Item noStyle name={'status'} initialValue={'Live'}>
            <ProductStatusSelector
              onChange={() => {
                actionRef.current?.reload(true);
              }}
              style={{ width: 130 }}
            />
          </Form.Item>
          <Form.Item noStyle>
            <ColumnDropdown
              columns={ProductMetaColumns.filter((item) => {
                return item.valueType !== 'option';
              })}
              selected={columns}
              onChange={(keys) => {
                setColumns(keys);
              }}
            />
          </Form.Item>
          <Form.Item noStyle>
            <SortDropdown
              onChange={changeOrder}
              order={order}
              list={ProductMetaColumns.filter((item) => !!item.sortable).map((item) => {
                return {
                  label: item.title,
                  key: item.dataIndex,
                };
              })}
            />
          </Form.Item>
        </Form>
      </div>
      <StyledDiv>
        <ConfigProvider
          renderEmpty={() => {
            if (total <= 0) {
              return (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={I18N.t('系统中尚未保存有该店铺已上架的商品')}
                />
              );
            }
            return (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={I18N.t('未找到相关商品')} />
            );
          }}
        >
          <ProTable<API.TkshopProductDto>
            size={'small'}
            components={{
              header,
            }}
            scroll={{
              x: tableWidth,
            }}
            loading={
              loading
                ? {
                    spinning: loading,
                    tip: I18N.t('正在为您同步商品'),
                  }
                : undefined
            }
            actionRef={actionRef}
            {...scrollProTableOptionFn({
              pageId: 'tkShopProductTable',
              pagination: {
                hideOnSinglePage: false,
                size: 'small',
              },
              footer() {
                return (
                  <Typography.Text type={selected.length > 100 ? 'danger' : 'secondary'}>
                    {I18N.t('已选：{{count}} 个', {
                      count: selected?.length?.toLocaleString(),
                    })}
                  </Typography.Text>
                );
              },
            })}
            rowKey={'productNo'}
            onRow={(record) => {
              const { productNo } = record;
              const disabled =  !record.planEnabled ||
                  record.status !== 'Live';
              return {
                onClick() {
                  if (disabled) {
                    return;
                  }
                  setSelected((prev) => {
                    const list = [...prev];
                    const index = list.indexOf(productNo!);
                    if (index !== -1) {
                      list.splice(index, 1);
                    } else {
                      list.push(productNo!);
                    }
                    return list;
                  });
                },
                style: {
                  cursor: disabled ? 'not-allowed' : 'pointer',
                },
              };
            }}
            columns={tableColumns}
            rowSelection={{
              selectedRowKeys: ids || [],
              onChange(ks) {
                setSelected((prev) => {
                  const list = [...prev];
                  data.forEach((item) => {
                    const index = list.indexOf(item.productNo!);
                    if (!ks.includes(item.productNo!)) {
                      if (index !== -1) {
                        list.splice(index, 1);
                      }
                    } else if (ks.includes(item.productNo!)) {
                      if (index === -1) {
                        list.push(item.productNo!);
                      }
                    }
                  });
                  return list;
                });
              },
              renderCell(value, record, index, node) {
                if (node?.props?.disabled) {
                  if (!record.planEnabled) {
                    return (
                      <Tooltip title={record.planRemark || '该商品无法用于定向邀约'}>
                        <span>
                          <Checkbox disabled />
                        </span>
                      </Tooltip>
                    );
                  }
                  if (record.status !== 'Live') {
                    return (
                      <Tooltip title={'该商品无法用于定向邀约'}>
                        <span>
                          <Checkbox disabled />
                        </span>
                      </Tooltip>
                    );
                  }

                }
                return node;
              },
              getCheckboxProps(record) {
                return {
                  disabled:
                    !record.planEnabled ||
                    record.status !== 'Live',
                };
              },
            }}
            request={(params) => {
              const { current, pageSize } = params;
              const searchParams = trimValues(form.getFieldsValue());
              return tkshopProductPageGet({
                shopIds: [shopId],
                pageSize,
                pageNum: current,
                sortField: order.key,
                sortOrder: order.ascend ? 'asc' : 'desc',
                ...searchParams,
              }).then((res) => {
                const _list = res?.data?.list || [];
                const _total = res?.data?.total || 0;
                if (!total) {
                  setTotal(_total);
                }
                setData(_list);
                return {
                  data: _list,
                  total: _total,
                };
              });
            }}
            style={
              !isInitialized
                ? {
                    opacity: 0,
                    pointerEvents: 'none',
                  }
                : {}
            }
          />
        </ConfigProvider>
      </StyledDiv>
    </DMModal>
  );
};
export default TkShopProductListModal;
