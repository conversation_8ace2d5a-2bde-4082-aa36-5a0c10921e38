import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { trimValues } from '@/utils/utils';
import { Form } from 'antd';
import { useState } from 'react';
import { useRequest } from 'umi';
import DMModal from "@/components/Common/Modal/DMModal";

const CommonFormModal = (
  props: GhostModalWrapperComponentProps & {
    children: any;
    onFinish: (values: any) => Promise<void>;
    title: string;
  },
) => {
  const { children, modalProps, onFinish, title } = props;
  const [open, setOpen] = useState(true);
  const [form] = Form.useForm();
  const { run: submit, loading } = useRequest(
    async () => {
      const vals = await form.validateFields();
      const values = trimValues(vals);
      await onFinish(values);
      setOpen(false);
    },
    {
      manual: true,
    },
  );
  return (
    <DMModal
      title={title}
      visible={open}
      onCancel={() => {
        setOpen(false);
      }}
      confirmLoading={loading}
      onOk={submit}
      {...modalProps}
    >
      <Form requiredMark={false} form={form}>
        {children}
      </Form>
    </DMModal>
  );
};

export default CommonFormModal;
