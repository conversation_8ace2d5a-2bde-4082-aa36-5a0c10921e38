import type { CSSProperties, Key } from 'react';
import { useCallback, useRef, useMemo, useEffect,useState } from 'react';
import _ from 'lodash';
import { Button, ConfigProvider, Form, Space, Tooltip, Typography } from 'antd';
import DmSwitch from '@/components/Switch';
import ProTable from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { StyledFlexFormItem, StyledTableWrapper } from '@/style/styled';
import I18N from '@/i18n';
import { GhostModalCaller } from '@/mixins/modal';
import TkShopProductListModal,{FallbackImage} from './TkShopProductListModal';
import { InputNumber } from 'antd5';
import { useRequest } from 'umi';
import ResponsiveText from './ResponsiveText';
import LabelRow, { LabelRowContext } from '@/components/Common/LabelRow';
import CommonFormModal from "@/pages/RunFlow/components/forms/tkshop/FormModal";
import DMFormItem from "@/components/Common/DMFormItem";
import Placeholder from "@/components/Common/Placeholder";
import {tkshopProductByNoGet} from "@/services/api-TKShopAPI/TkshopProductController";

// 常量定义
const INPUT_NUMBER_STYLE = { width: 100 };
const FORM_ITEM_STYLE = { marginBottom: 0 };
const SWITCH_STYLE = { width: 60 };

export type ProductItem = {
  productId: string;
  commission: number;
  enableAgencyCommission: boolean;
  agencyCommission: number;
};
function safeToNumber(value: any) {
  const num = _.toNumber(value);
  return Number.isNaN(num) ? 1 : num;
}
export function transformData(value: ProductItem[] | string): ProductItem[] {
  if (value?.length) {
    try {
      if (value instanceof Array) {
        // 新格式数据,允许更多可能性
        return value
          .map((row: ProductItem) => {
            const { productId, commission, enableAgencyCommission, agencyCommission } = row;
            return {
              productId,
              commission,
              enableAgencyCommission,
              agencyCommission,
            };
          })
          .filter((item) => !!item.productId);
      } else {
        // 兼容老的表单id,
        return value
          .split('\n')
          .map((row: string) => {
            const [productId, commission] = row.split(',');
            return {
              productId,
              commission: safeToNumber(commission),
              enableAgencyCommission: false,
              agencyCommission: 1,
            };
          })
          .filter((item) => !!item.productId);
      }
    } catch (e) {
      console.log(e);
      return [];
    }
  } else {
    return [];
  }
}
export function stringifyData(data: ProductItem[]) {
  return _.uniqBy(data, (item) => {
    return item.productId;
  }).filter((item) => !!item.productId);
}
const ProductNoCell = (props: { productNo: string; openModal: () => void; shopId: number }) => {
  const { productNo, openModal, shopId } = props;
  const [openTooltip, setOpenTooltip] = useState(false);
  const {
    data: detail,
    run,
    loading,
  } = useRequest(() =>
    tkshopProductByNoGet({
      shopId,
      productNo,
    }),
  );
  const title = useMemo(() => {
    if (loading && !detail) {
      return null;
    }
    if (!detail) {
      return `商品【${productNo}】未查询到详情`;
    }
    return (
      <div>
        <LabelRowContext.Provider value={{ labelWidth: 90, labelMuted: false }}>
          <LabelRow label="商品名称" valueWrapStyle={{ width: '100%', overflow: 'hidden' }}>
            <ResponsiveText ellipsis={false}>{detail?.productName}</ResponsiveText>
          </LabelRow>
          <LabelRow label="商品ID">{detail?.productNo}</LabelRow>
          <LabelRow label="公开佣金">
            {!_.isNil(detail?.cosRatio) ? `${detail?.cosRatio}%` : <Placeholder />}
          </LabelRow>
          <LabelRow label="公开广告佣金">
            {!_.isNil(detail?.adsCosRatio) ? `${detail?.adsCosRatio}%` : <Placeholder />}
          </LabelRow>
        </LabelRowContext.Provider>

      </div>
    );
  }, [detail, loading, productNo]);
  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: 4, overflow: 'hidden' }}>

      <FallbackImage src={detail?.productAvatar} />
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <Tooltip
          overlayInnerStyle={{ width: 300 }}
          visible={openTooltip}
          placement="right"
          onVisibleChange={setOpenTooltip}
          title={title}
        >
          <Typography.Link ellipsis onClick={openModal}>
            <ResponsiveText>{productNo}</ResponsiveText>
          </Typography.Link>
        </Tooltip>
      </div>
    </div>
  );
};
const ProductListField = (props: {
  shopId: number;
  deviceId?: string;
  value?: ProductItem[];
  onChange?: (value: ProductItem[]) => void;
  defaultCommission?: number;
  agencyCommission?: number;
  enableAgencyCommission?: boolean;
  height?: number;
}) => {
  const {
    shopId,
    deviceId,
    value: __value,
    onChange,
    defaultCommission = 1,
    agencyCommission = 1,
    enableAgencyCommission = false,
    height,
  } = props;
  const ref = useRef<HTMLDivElement>();
  const value = useMemo(() => {
    if (!__value) {
      return [];
    }
    return __value;
  }, [__value]);
  const hacked = useRef(false);
  useEffect(() => {
    hacked.current = false;
  }, [shopId]);
  useEffect(() => {
    // 处理老数据格式兼容
    if (value?.length && !hacked.current) {
      hacked.current = true;
      _onChange(transformData(value));
    }
  }, [value]);

  const _onChange = useCallback(
    (val: ProductItem[]) => {
      onChange?.(stringifyData(val));
    },
    [onChange],
  );

  // 统一的更新函数
  const _onUpdateProduct = useCallback(
    (productId: string, updates: Partial<ProductItem>) => {
      if (!productId || !value?.length) return;
      const newData = [...value];
      const index = newData.findIndex((item) => item.productId === productId);
      if (index === -1) return;
      Object.assign(newData[index], updates);
      _onChange(newData);
    },
    [value, _onChange],
  );

  const _onCommissionChange = useCallback(
    (productId: string, value: number | null) => {
      if (value !== null && value >= 1 && value <= 100) {
        _onUpdateProduct(productId, { commission: safeToNumber(value) });
      }
    },
    [_onUpdateProduct],
  );

  const _onAgencyCommissionChange = useCallback(
    (productId: string, value: number | null) => {
      if (value !== null && value >= 1 && value <= 100) {
        _onUpdateProduct(productId, { agencyCommission: safeToNumber(value) });
      }
    },
    [_onUpdateProduct],
  );

  const _onAgencyCommissionEnabledChange = useCallback(
    (productId: string, enabled: boolean) => {
      _onUpdateProduct(productId, { enableAgencyCommission: enabled });
    },
    [_onUpdateProduct],
  );
  const _onSelect = useCallback(
    (ids: string[]) => {
      const newData = ids.map((productId) => {
        // 先找是否存在，存在直接返回
        const _exist_item = value.find((item) => productId && item.productId === productId);
        if (_exist_item) {
          return _exist_item;
        }
        return {
          productId,
          commission: defaultCommission,
          enableAgencyCommission,
          agencyCommission,
        };
      });
      const _value = _.uniqBy(newData, (item) => item.productId).filter(
        (item) => !!item?.productId?.toString().trim(),
      );
      _onChange(_value);
    },
    [_onChange, defaultCommission, enableAgencyCommission, agencyCommission, value],
  );

  const openModal = useCallback(() => {
    GhostModalCaller(
      <TkShopProductListModal
        deviceId={deviceId}
        shopId={shopId}
        selectedIds={value?.map?.((item) => item.productId!) || []}
        onSubmit={_onSelect}
      />,
    );
  }, [shopId, value, _onSelect, deviceId]);

  const _onRemove = useCallback(
    (productId: Key) => {
      const newData = [...value];
      const index = newData.findIndex((item) => item.productId === productId);
      if (index === -1) return;
      newData.splice(index, 1);
      _onChange(newData);
    },
    [value, _onChange],
  );

  // 缓存表格列配置
  const columns = useMemo(
    () => [
      {
        title: (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <span>商品ID</span>
            {value?.length > 0 && (
              <Typography.Link onClick={openModal}>（{value?.length}个）</Typography.Link>
            )}
          </div>
        ),
        dataIndex: 'productId',
        render: (dom: string, record: ProductItem) => {
          const { productId } = record;
          return <ProductNoCell productNo={productId} openModal={openModal} shopId={shopId} />;
        },
      },
      {
        title: (
          <Space>
            标准佣金
            {value?.length > 0 && (
              <Typography.Link
                onClick={() => {
                  GhostModalCaller(
                    <CommonFormModal
                      title={'批量修改标准佣金'}
                      onFinish={async (values) => {
                        const newData = [...value];
                        newData.forEach((item) => {
                          item.commission = values.commission;
                        });
                        _onChange(newData);
                      }}
                    >
                      <DMFormItem
                        name={'commission'}
                        initialValue={defaultCommission}
                        label={'标准佣金'}
                        rules={[{ required: true, message: '请输入' }]}
                      >
                        <InputNumber addonAfter={'%'} min={1} max={100} precision={0} />
                      </DMFormItem>
                    </CommonFormModal>,
                  );
                }}
              >
                <IconFontIcon iconName={'edit_24'} />
              </Typography.Link>
            )}
          </Space>
        ),
        dataIndex: 'commission',
        width: '120px',
        render: (_: any, record: ProductItem) => {
          const { commission, productId } = record;
          return (
            <Form.Item style={FORM_ITEM_STYLE}>
              <InputNumber
                style={INPUT_NUMBER_STYLE}
                value={commission}
                min={1}
                max={100}
                precision={0}
                addonAfter={'%'}
                onChange={(v) => _onCommissionChange(productId, v)}
              />
            </Form.Item>
          );
        },
      },
      {
        title: (
          <Space>
            广告佣金
            {value?.length > 0 && (
              <Typography.Link
                onClick={() => {
                  GhostModalCaller(
                    <CommonFormModal
                      title={'批量修改广告佣金'}
                      onFinish={async (values) => {
                        const newData = [...value];
                        newData.forEach((item) => {
                          item.agencyCommission = values.agencyCommission;
                          item.enableAgencyCommission = values.enableAgencyCommission;
                        });
                        _onChange(newData);
                      }}
                    >
                      <Form.Item shouldUpdate noStyle>
                        {(f) => {
                          const _enableAgencyCommission = f.getFieldValue('enableAgencyCommission');
                          const _agencyCommission = f.getFieldValue('agencyCommission');
                          return (
                            <DMFormItem
                              name={'_VALIDATOR'}
                              label={'广告佣金'}
                              rules={[
                                {
                                  validator(rule, value) {
                                    if (_enableAgencyCommission) {
                                      if (!_agencyCommission) {
                                        return Promise.reject(new Error('请输入广告佣金'));
                                      }
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
                                <Form.Item
                                  noStyle
                                  name={'enableAgencyCommission'}
                                  initialValue={enableAgencyCommission}
                                >
                                  <DmSwitch
                                    style={SWITCH_STYLE}
                                    onChange={() => {
                                      f.validateFields(['_VALIDATOR']);
                                    }}
                                  />
                                </Form.Item>
                                <Form.Item
                                  noStyle
                                  name={'agencyCommission'}
                                  initialValue={agencyCommission}
                                >
                                  <InputNumber
                                    addonAfter={'%'}
                                    onChange={() => {
                                      f.validateFields(['_VALIDATOR']);
                                    }}
                                    controls
                                    min={1}
                                    max={100}
                                    precision={0}
                                    disabled={!_enableAgencyCommission}
                                  />
                                </Form.Item>
                              </div>
                            </DMFormItem>
                          );
                        }}
                      </Form.Item>
                    </CommonFormModal>,
                  );
                }}
              >
                <IconFontIcon iconName={'edit_24'} />
              </Typography.Link>
            )}
          </Space>
        ),
        dataIndex: 'agencyCommission',
        width: '200px',
        render: (_: any, record: ProductItem) => {
          const { agencyCommission, enableAgencyCommission, productId } = record;
          return (
            <Space>
              <DmSwitch
                style={SWITCH_STYLE}
                checked={enableAgencyCommission}
                onChange={(val) => _onAgencyCommissionEnabledChange(productId, val)}
              />
              <Form.Item style={FORM_ITEM_STYLE}>
                <InputNumber
                  style={INPUT_NUMBER_STYLE}
                  disabled={!enableAgencyCommission}
                  value={agencyCommission}
                  min={1}
                  max={100}
                  precision={0}
                  addonAfter={'%'}
                  onChange={(v) => _onAgencyCommissionChange(productId, v)}
                />
              </Form.Item>
            </Space>
          );
        },
      },
      {
        title: '操作',
        width: '55px',
        key: 'opt',
        render: (_: any, record: ProductItem) => {
          return (
            <Typography.Link onClick={() => _onRemove(record.productId)}>移除</Typography.Link>
          );
        },
      },
    ],
    [value, openModal, shopId, defaultCommission, _onChange, _onCommissionChange, enableAgencyCommission, agencyCommission, _onAgencyCommissionEnabledChange, _onAgencyCommissionChange, _onRemove],
  );

  return (
    <Form.Item shouldUpdate noStyle>
      {(f) => {
        return (
          <StyledTableWrapper
            style={{ height: '100%', overflow: 'hidden', border: '1px solid #ddd' }}
            ref={ref as React.RefObject<HTMLDivElement>}
          >
            <ConfigProvider
              renderEmpty={() => {
                return (
                  <Button
                    type="link"
                    icon={<IconFontIcon iconName="tianjia_24" />}
                    onClick={openModal}
                  >
                    添加商品
                  </Button>
                );
              }}
            >
              <ProTable<ProductItem>
                rowKey={'productId'}
                {...scrollProTableOptionFn({
                  scroll: {
                    y: height ? height : '100%',
                    x: value?.length > 0 ? '600px' : undefined,
                  },
                  pagination: false,
                })}
                dataSource={value}
                // @ts-ignore
                columns={columns}
              />
            </ConfigProvider>
          </StyledTableWrapper>
        );
      }}
    </Form.Item>
  );
};
export default ProductListField;
export const ProductsFormItem = (props: {
  shopId: number;
  name?: string;
  style?: CSSProperties;
  deviceId?: string;
  height?: number;
}) => {
  const { shopId, name = 'productIds', deviceId, style = {}, height } = props;
  return (
    <Form.Item shouldUpdate noStyle>
      {(f) => {
        const defaultCommission = f.getFieldValue('defaultCommission') || 1;
        const agencyCommission = f.getFieldValue('defaultAgencyCommission') || 1;
        const enableAgencyCommission = f.getFieldValue('defaultEnableAgencyCommission') || false;
        return (
          <StyledFlexFormItem
            style={style}
            label={'商品ID列表'}
            name={name}
            initialValue={[]}
            rules={[
              {
                validator: (rule, val: ProductItem[]) => {
                  const valid_products = _.filter(val || [], (item) => {
                    return !!_.trim(item.productId);
                  });
                  if (!valid_products.length) {
                    return Promise.reject(new Error(I18N.t('至少添加一个商品')));
                  }
                  if (valid_products.length > 100) {
                    return Promise.reject(new Error(I18N.t('单次计划最多添加100个商品')));
                  }
                  const findIndex = val.findIndex((item) => {
                    return (
                      !!_.trim(item.productId) &&
                      !!item.enableAgencyCommission &&
                      item.agencyCommission <= 0
                    );
                  });
                  if (findIndex !== -1) {
                    return Promise.reject(
                      new Error(I18N.t(`第${findIndex + 1}行的商品，未填入广告佣金`)),
                    );
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <ProductListField
              shopId={shopId}
              deviceId={deviceId}
              defaultCommission={defaultCommission}
              agencyCommission={agencyCommission}
              enableAgencyCommission={enableAgencyCommission}
              height={height}
            />
          </StyledFlexFormItem>
        );
      }}
    </Form.Item>
  );
};
