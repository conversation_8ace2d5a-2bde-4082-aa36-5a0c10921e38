import { Tooltip } from 'antd';
import _ from 'lodash';
import { useState, useRef, useMemo, useEffect, ReactNode } from 'react';
import Placeholder from "@/components/Common/Placeholder";

// 响应式文本组件，解决容器宽度变化时溢出文字不显示的问题
const ResponsiveText = (props: {
  children: ReactNode;
  style?: React.CSSProperties;
  ellipsis?: boolean;
}) => {
  const { children, style, ellipsis = true } = props;
  const [isOverflowing, setIsOverflowing] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const inner = useMemo(() => {
    if (_.isNil(children)) return null;
    return children;
  }, [children]);

  // 使用 ResizeObserver 监听容器宽度变化
  useEffect(() => {
    if (!textRef.current || !containerRef.current || !inner) return;

    const checkOverflow = () => {
      if (textRef.current && containerRef.current) {
        const textWidth = textRef.current.scrollWidth;
        const containerWidth = containerRef.current.clientWidth;
        setIsOverflowing(textWidth > containerWidth);
      }
    };

    const resizeObserver = new ResizeObserver(() => {
      // 使用 requestAnimationFrame 避免 ResizeObserver loop limit exceeded 错误
      requestAnimationFrame(checkOverflow);
    });

    resizeObserver.observe(containerRef.current);

    // 初始检查
    checkOverflow();

    return () => {
      resizeObserver.disconnect();
    };
  }, [inner]);

  if (_.isNil(children)) {
    return <Placeholder />;
  }

  return (
    <div
      ref={containerRef}
      style={{
        width: '100%',
        overflow: 'hidden',
        color: 'inherit',
        cursor: 'inherit',
      }}
    >
      <Tooltip
        title={isOverflowing && ellipsis ? inner : null}
        placement="top"
        destroyTooltipOnHide
      >
        <div
          ref={textRef}
          style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            width: '100%',
            ...style,
          }}
        >
          {inner}
        </div>
      </Tooltip>
    </div>
  );
};
export default ResponsiveText;
