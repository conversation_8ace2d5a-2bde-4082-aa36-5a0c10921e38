import {AdbClient, downloadAdb, getAdbExecutePath} from '@e/mobile/android/adb/AdbClient';
import {AndroidDevice} from '@e/mobile/android/AndroidDevice';
import Tracker from '@dead50f7/adbkit/lib/adb/tracker';
import {TrackerChangeSet} from '@dead50f7/adbkit/lib/TrackerChangeSet';
import {DeviceState} from '@e/mobile/DeviceState';

import logger from '@e/services/logger';
import {ConnectType, MobileDto} from '@e/mobile/cons/common';
import request from '@e/services/request';
import {isPortOpen, isWin7Platform} from '@e/utils/utils';
import {AppiumService} from '@e/mobile/android/adb/Appium';
import {waitSeconds} from '@e/rpa/utils';
import {DirectAndroidDevice} from "@e/mobile/android/DirectAndroidDevice";
import {CloudAndroidDevice} from "@e/mobile/android/CloudAndroidDevice";
import {Server} from "net";
import {AndroidStreamService} from "@e/mobile/android/service/stream";
import {portalRpcClient} from "@e/components/backendTask";
import {QCloudAndroidDevice} from "@e/mobile/android/QCloudAndroidDevice";
import Timeout = NodeJS.Timeout;

export class AndroidDeviceManager {
  private static readonly defaultWaitAfterError = 1000;
  private static instance?: AndroidDeviceManager;

  socks5Server?: Server;

  initialized: boolean = false;
  private waitAfterError = 1000;
  private restartTimeoutId?: Timeout;

  androidStreamService = new AndroidStreamService((udid)=> this.devices.get(udid));

  client!: AdbClient;
  /**
   * key : udid ，如果是 CloudAndroidDevice 则为 padCode
   * value : DirectAndroidDevice | CloudAndroidDevice
   */
  devices: Map<string, AndroidDevice> = new Map<string, AndroidDevice>();
  private tracker?: Tracker;
  // private requestAgent: RequestAgent;

  heartbeatTimer: any = 0;
  onUpdateProxyEventTimer: any = 0;

  protected constructor() {
    // this.requestAgent = new RequestAgent();
  }

  public static getInstance(): AndroidDeviceManager {
    if (!AndroidDeviceManager.instance) {
      AndroidDeviceManager.instance = new AndroidDeviceManager();
    }
    return AndroidDeviceManager.instance;
  }

  public async start() {
    if (isWin7Platform()) return;
    try {
      getAdbExecutePath();
    } catch (e) {
      await downloadAdb();
    }
    this.client = AdbClient.createClient();
    // await AppiumService.getInstance().startServer();
    await this.init();
  }

  private async init() {
    if (this.initialized) {
      return;
    }
    this.initialized = true;
    this.devices.clear();
    this.tracker = await this.startTracker();
    await this.refreshDevices();
    this.heartbeatTimer = setInterval(() => this.heartbeat(), 60 * 1000);
    this.androidStreamService.start().catch(()=>{});
    portalRpcClient.onAppEmit('q.mobile.update.proxy', (data: any) => {
      const udid = data.udid;
      let device = this.devices.get(udid);
      if(device && device.connectType == 'QCloud') {
        clearTimeout(this.onUpdateProxyEventTimer);
        this.onUpdateProxyEventTimer = setTimeout(() => {
          (device as QCloudAndroidDevice).onUpdateProxyEvent(data);
        }, 1000);
      }
      return 1;
    });
  }

  stop() {
    if (isWin7Platform()) return;
    this.initialized = false;
    this.devices.forEach((device) => {
      this.onDeviceConnected(device.udid, DeviceState.DISCONNECTED);
    });
    this.devices.clear();
    this.client?.kill?.();
    clearInterval(this.heartbeatTimer);
    this.socks5Server?.close();
    AppiumService.getInstance()?.stopServer();
    this.androidStreamService?.stop().catch(()=>{});
    portalRpcClient.removeAllListeners('q.mobile.update.proxy');
  }

  //客户端的心跳似乎有问题，会导致服务器端认为客户端已经下线而将所有的手机标记为offline
  private async heartbeat() {
    await this.refreshDevices();
    this.devices.forEach((device, udid) => {
      if (device.isDirect() && device.isConnected()) {
        device.reportState('ONLINE');
      }
    });
  }

  async refreshDevices() {
    let devices = await this.client.listDevices();
    devices.forEach((device) => {
      const { id, type } = device;
      this.onDeviceConnected(id, type);
    });
    request(`/api/shop/app_mobile/findWifiUdidsByDevice`, {
      method: 'GET',
    }).then(async (wifiUdids) => {
      for (let adbAddress of wifiUdids) {
        await this.client.runAdbCommand(undefined, 'connect', adbAddress);
      }
    });
  }

  /**
   * 获取设备，如果是arm cloud，传入的udid其实是padCode
   * @param udid
   * @param connectType
   */
  async prepareDevice(udid: string, connectType: ConnectType): Promise<AndroidDevice|undefined> {
    let device = this.devices.get(udid);
    if('ARMCLOUD' === connectType || 'Baidu' === connectType || 'QCloud' === connectType) {
      if(!device) {
        let padCode = udid;
        const cloudMobileDevice = 'QCloud' === connectType ? new QCloudAndroidDevice(padCode, connectType, 'offline') :new CloudAndroidDevice(padCode, connectType, 'offline');
        this.devices.set(padCode, cloudMobileDevice);
        device = cloudMobileDevice;
      }
      await device.prepareAdb();
    }
    return device;
  }

  async restartAdbServer() {
    logger.info('开始重启adb服务');
    this.stopTracker();
    await AppiumService.getInstance().stopServer();
    await AdbClient.createClient().kill();
    this.client = AdbClient.createClient();
    this.devices.forEach((device) => {
      device.killServer(0);
      device.setState('offline');
      device.disconnectAdb();
    });
    await waitSeconds(5);
    this.tracker = await this.startTracker();
    await this.refreshDevices();
  }

  /**
   * 将某个设备的scrcpy服务端口暴露出来
   * @param udid
   */
  public async forwardScrcpy(udid: string): Promise<string> {
    let device = this.devices.get(udid);
    if (device) {
      await device.prepareAdb();
      if (device.isConnected()) {
        const port = await device.forwardScrcpy();
        return `ws://127.0.0.1:${port}`;
      } // else 说明已经下线
      device.reportState('OFFLINE').catch(() => {});
    }
    throw new Error(`设备未连接（${udid}）`);
  }

  public async forwardMjpeg(udid: string, smallPreview = false): Promise<string> {
    let device = this.devices.get(udid);
    if (device) {
      await device.prepareAdb();
      if (device.isConnected()) {
        return this.androidStreamService.getWsUrl() + '?udid=' + udid + '&smallPreview=' + (!!smallPreview);
      } // else 说明已经下线
      device.reportState('OFFLINE').catch(() => {});
    }
    throw new Error(`设备未连接（${udid}）`);
  }


  public kickInUse(udid: string) {
    let device = this.devices.get(udid);
    device?.kickInUse();
  }

  public async listDevices(teamId: number, excludeUdids: string[]): Promise<MobileDto[]> {
    excludeUdids = excludeUdids || [];
    let result: MobileDto[] = [];
    let allowPadCodes = undefined;
    for(let [udid, device] of this.devices) {
      //todo 根据teamId排队掉已经添加到该设备却不属于当前团队的设备
      if(!device.isDirect()) {
        if(!allowPadCodes) {
          allowPadCodes = await request(`/api/shop/app_mobile/findBoundCloudCodes`, {
            teamId,
          });
        }
        if(!allowPadCodes.includes(udid)) {
          device.disconnectAdb();
          this.devices.delete(udid);
        }
      }
      if (device.isConnected() && !excludeUdids.includes(udid)) {
        result.push(await device.toMobileDto());
      }
    }
    //查找已经分配到当前团队，但是尚未导入的arm cloud手机
    let armCloudIns = await request(`/api/shop/app_mobile/findNotBindArmCloudIns`, {
      teamId,
    });
    for(let ins of (armCloudIns || [])) {
      if(!this.devices.has(ins.padCode) && !excludeUdids.includes(ins.padCode)) {
        result.push({
          code: ins.padCode,
          platform: 'Android',
          connectType: ins.connectType,
          name: ins.name,
          mode: ins.mode,
          androidVersion: ins.androidVersion,
          screenWidth: ins.screenWidth,
          screenHeight: ins.screenHeight,
        });
      }
    }
    return result;
  }

  /**
   * 通过wifi连接一台手机
   * @param host
   * @param port
   * @param pairPort 配对端口
   * @param pairCode 配对码
   */
  public async connectWifiDevice(
    host: string,
    port: number,
    pairPort?: number,
    pairCode?: string,
  ): Promise<MobileDto> {
    const udid = `${host}:${port}`;
    if (!(await isPortOpen(host, port))) {
      throw new Error('指定的端口无法连接');
    }
    if (pairPort && pairCode) {
      try {
        await this.client.runAdbCommand(undefined, 'pair', [`${host}:${pairPort}`, pairCode]);
      } catch (e) {
        throw e;
      }
    }
    await this.client.runAdbCommand(undefined, 'connect', udid);
    const device = this.devices.get(udid);
    if (!device) {
      throw new Error('无法通过指定端口建立adb连接');
    } else {
      for (let i = 0; i < 10; i++) {
        await waitSeconds(1);
        if (device.isConnected()) {
          return (device as DirectAndroidDevice).toMobileDto();
        }
      }
      throw new Error('无法通过指定端口建立adb连接');
    }
  }

  private async onDeviceConnected(udid: string, state: string) {
    if (udid?.indexOf('_adb-tls-connect._tcp') != -1) {
      return;
    }
    let device = this.devices.get(udid);
    if(!device) {
      for(let value of this.devices.values()) {
        if(udid === value.udid) {
          device = value;
          break;
        }
      }
    }
    if (device) {
      device.setState(state);
    } else {
      let isWifi = /:\d+$/.test(udid);
      if(!isWifi || state === 'device') {
        device = new DirectAndroidDevice(udid, state);
        this.devices.set(udid, device);
        logger.info(`Android设备已连接，udid=${udid}`);
      }
    }
  }

  private onChangeSet = (changes: TrackerChangeSet): void => {
    this.waitAfterError = AndroidDeviceManager.defaultWaitAfterError;
    if (changes.added.length) {
      for (const item of changes.added) {
        const { id, type } = item;
        this.onDeviceConnected(id, type);
      }
    }
    if (changes.removed.length) {
      for (const item of changes.removed) {
        const { id } = item;
        this.onDeviceConnected(id, DeviceState.DISCONNECTED);
      }
    }
    if (changes.changed.length) {
      for (const item of changes.changed) {
        const { id, type } = item;
        this.onDeviceConnected(id, type);
      }
    }
  };

  private async startTracker(): Promise<Tracker> {
    if (this.tracker) {
      return this.tracker;
    }
    const tracker = await this.client.trackDevices();
    tracker.on('changeSet', this.onChangeSet);
    tracker.on('end', this.restartTracker);
    tracker.on('error', this.restartTracker);
    return tracker;
  }

  private stopTracker(): void {
    if (this.tracker) {
      this.tracker.off('changeSet', this.onChangeSet);
      this.tracker.off('end', this.restartTracker);
      this.tracker.off('error', this.restartTracker);
      this.tracker.end();
      this.tracker = undefined;
    }
    this.tracker = undefined;
    this.initialized = false;
  }

  private restartTracker = (): void => {
    if (this.restartTimeoutId) {
      return;
    }
    logger.log(`Device tracker is down. Will try to restart in ${this.waitAfterError}ms`);
    this.restartTimeoutId = setTimeout(() => {
      this.stopTracker();
      this.waitAfterError *= 1.2;
      this.init();
    }, this.waitAfterError);
  };
}
