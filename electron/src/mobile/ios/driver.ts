import {WDAClient} from "@e/mobile/ios/service/wda";
import {getAFreePort} from "@e/utils/utils";
import {spawn} from "child_process";
import logger, {wdaLogger} from "@e/services/logger";
import {waitMilliseconds, waitPromise, waitSeconds} from "@e/rpa/utils";
import {executeIOS, goIosPath, parseElementId} from "@e/mobile/ios/utils";
import {TouchActions} from "@e/mobile/ios/touch";
import path from "path";
import {Heartbeat} from "@e/rpa/heartbeat";
import EventEmitter from "events";
import {XCUIElementTypes} from "@e/mobile/ios/consts";
import {readFile} from "fs-extra";
import Downloader from "nodejs-file-downloader";
import fs from "fs";
import {app} from "electron";

const min_alert_layout_depth = 15;

export class Element {
  elementId?: string;
  private wda: WDAClient;
  private driver: IOSDriver;

  constructor(driver: IOSDriver, elementId?: string) {
    this.wda = driver.wda;
    this.driver = driver;
    this.elementId = elementId;
  }

  __xpath!: string;
  __predicate!: string;

  exists(): boolean {
    return !!this.elementId;
  }

  async label(): Promise<string> {
    return await this.prop('/attribute/label');
  }

  async type(): Promise<string> {
    return await this.prop('/attribute/type');
  }

  async text(): Promise<string> {
    return await this.prop('/text');
  }

  async name(): Promise<string> {
    return await this.prop('/attribute/name');
  }

  async value(): Promise<string> {
    return await this.prop('/attribute/value');
  }

  async selected() {
    return await this.prop('/selected');
  }

  async displayed(): Promise<string> {
    return await this.prop('/displayed');
  }

  async enabled(): Promise<boolean> {
    return await this.prop('/enabled');
  }

  async accessible(): Promise<boolean> {
    return await this.wdaProp('/accessible');
  }

  async visible(): Promise<boolean> {
    return await this.prop('/attribute/visible');
  }

  async accessibilityContainer(): Promise<boolean> {
    return await this.wdaProp('/accessibilityContainer');
  }

  async bounds(): Promise<{x: number, y: number, width: number, height: number}> {
    return await this.prop('/rect');
  }

  async setText(text: string, frequency = 0.5) {
    await this.req('post', '/value', {value: text, frequency})
  }

  async clearText() {
    await this.req('post', '/clear');
  }

  async tap() {
    await this.req('post', '/click');
  }

  async click() {
    let bounds = await this.bounds();
    let center = {
      x: bounds.x + bounds.width / 2,
      y: bounds.y + bounds.height / 2
    };
    await this.wda.tap(center.x, center.y);
  }

  async doubleTab() {
    let bounds = await this.bounds();
    let center = {
      x: bounds.x + bounds.width / 2,
      y: bounds.y + bounds.height / 2
    };
    await this.wda.doubleTap(center.x, center.y);
  }

  /**
   * 滚动当前元素
   * @param direction visible 表示将元素滑动到可见，其它值如字面意思
   * @param distance distance=1.0 means, element (width or height) multiply 1.0
   */
  async scroll(direction : 'visible' | 'up' | 'down' |  'left' | 'right' = 'visible', distance=1.0) {
    if('visible' === direction) {
      return this.wdaReq('post', '/scroll', {'toVisible': true});
    } else if(['up', 'down', 'left', 'right'].includes(direction)) {
      let winSize = await this.wda.getWindowSize();
      let bounds = await this.bounds();
      let x0 = Math.max(0, Math.min(bounds.x + bounds.width / 2, winSize.width));
      let y0 = Math.max(0, Math.min(bounds.y + bounds.height / 2, winSize.height));
      let x1 = x0, y1 = y0;
      let x2 = x0, y2 = y0;
      switch (direction) {
        case 'left':
          x2 = Math.max(0, x0 - distance * bounds.width);
          x1 = x2 + 2;
          break;
        case 'right':
          x2 = Math.min(winSize.width, x0 + distance * bounds.width);
          x1 = x2 - 2;
          break;
        case 'up':
          y2 = Math.max(0, y0 - distance * bounds.height);
          y1 = y2 + 2;
          break;
        case 'down':
          y2 = Math.min(winSize.height, y0 + distance * bounds.height);
          y1 = y2 - 2;
          break;
      }
      if(x2 == x0 && y2 == y0) {
        return;
      }
      let touch = new TouchActions();
      touch.finger('0').press(x0, y0).wait(200).move(x1, y1).wait(100).move(x2, y2).wait(100).release();
      await this.wda.performTouchActions(touch);
    }
  }

  /**
   * 获取父节点
   */
  async parent() {
    let xpath = this.__xpath + '/..';
    return this.driver.findElement(xpath);
  }

  /**
   * 查找当前节点前面的兄弟节点
   * @param type * 表示所有类型节点都行，否则只找具体类型的节点
   * @param index 第几个 1-based
   */
  async preceding(type: string, index: number): Promise<Element> {
    let xpath = this.__xpath + `/preceding-sibling::${type}[${index}]`;
    return this.driver.findElement(xpath);
  }

  /**
   * 获取当前节点前面的兄弟节点
   * @param type * 表示所有类型节点都行，否则只找具体类型的节点
   */
  async precedings(type: string): Promise<Element[]> {
    let xpath = this.__xpath + `/preceding-sibling::${type}`;
    return this.driver.findElements(xpath);
  }

  /**
   * 获取当前节点后面的兄弟节点
   * @param type * 表示所有类型节点都行，否则只找具体类型的节点
   * @param index 第几个 1-based
   */
  async following(type: string, index: number): Promise<Element> {
    let xpath = this.__xpath + `/following-sibling::${type}[${index}]`;
    return this.driver.findElement(xpath);
  }

  /**
   * 获取当前节点后面的兄弟节点
   * @param type * 表示所有类型节点都行，否则只找具体类型的节点
   */
  async followings(type: string): Promise<Element[]> {
    let xpath = this.__xpath + `/following-sibling::${type}`;
    return this.driver.findElements(xpath);
  }

  /**
   * 查找子元素
   * @param xpath
   */
  async children(xpath: string): Promise<Element[]> {
    xpath = this.__xpath + xpath;
    return this.driver.findElements(xpath);
  }

  private async prop(path: string) {
    path = `/element/${this.elementId}${path}`;
    let ret = await this.wda.sessionGet(path);
    return ret.value;
  }

  private async wdaProp(path: string) {
    path = `/wda/element/${this.elementId}${path}`;
    let ret = await this.wda.sessionGet(path);
    return ret.value;
  }

  private async req(method: 'get'|'post', path: string, data?: any) {
    path = `/element/${this.elementId}${path}`;
    if('get' === method.toLowerCase()) {
      return await this.wda.sessionGet(path);
    } else {
      return await this.wda.sessionPost(path, data);
    }
  }

  private async wdaReq(method: 'get'|'post', path: string, data?: any) {
    path = `/wda/element/${this.elementId}${path}`;
    if('get' === method.toLowerCase()) {
      return await this.wda.sessionGet(path);
    } else {
      return await this.wda.sessionPost(path, data);
    }
  }

}

export class Alert extends EventEmitter {
  driver: IOSDriver;
  heartbeat?: Heartbeat;

  __the_original_depth__ = 0;

  private watchCallback?: (text: string, buttons: string[]) => 'accept'|'dismiss' | Promise<'accept'|'dismiss'>;

  constructor(driver: IOSDriver) {
    super();
    this.driver = driver;
  }

  async exists(): Promise<boolean> {
    try {
      let text = await this.text();
      return !!text;
    } catch (e) {
      return false;
    }
  }

  /**
   * 获取系统弹窗
   */
  async text(): Promise<string> {
    try {
      let ret = await this.driver.wda.sessionGet('/alert/text');
      return ret.value;
    } catch (e) {
      return undefined as any as string;
    }
  }

  async accept(button?: string) {
    try {
      let data: any = {};
      if(button) {
        data.name = button;
      }
      await this.driver.wda.sessionPost('/alert/accept', data);
    } catch (ignore) {}
  }

  async dismiss() {
    try {
      await this.driver.wda.sessionPost('/alert/dismiss', {});
    } catch (ignore) {}
  }

  async buttons() {
    try {
      let ret = await this.driver.wda.sessionGet('/wda/alert/buttons');
      return ret.value;
    } catch (e) {
      return undefined;
    }

  }

  /**
   * 监听系统弹窗
   * @param callback: (text: string, buttons: string[])=> 'accept'|'dismiss' | Promise<'accept'|'dismiss'>
   */
  watch(callback: (text: string, buttons: string[]) => 'accept'|'dismiss' | Promise<'accept'|'dismiss'>) {
    this.watchCallback = callback;
    if(!this.heartbeat) {
      this.heartbeat = new Heartbeat(async () => {
        if(await this.exists()) {
          let text = await this.text();
          let buttons = await this.buttons();
          let ret = this.watchCallback?.(text, buttons);
          if(ret instanceof Promise) {
            ret = await ret;
          }
          if(ret == 'accept') {
            if(await this.exists()) {
              await this.accept();
            }
          } else {
            if(await this.exists()) {
              await this.dismiss();
            }
          }
        }
      }, 2000);
      this.heartbeat.start();
    }
  }

  stopWatch() {
    this.watchCallback = undefined;
    this.heartbeat?.stop();
    this.heartbeat = undefined;
  }

  destroy() {
    this.stopWatch();
    this.driver.alert_cache = undefined;
    this.driver.setLayoutDepth(this.__the_original_depth__);
  }

}

abstract class IOSDriverApi extends EventEmitter {
  udid!: string;
  wda!: WDAClient;

  getSessionId(): string {
    let sessionId = this.wda.getSessionId();
    if(!sessionId) {
      throw 'wda not ready';
    }
    return sessionId;
  }

  async getRotation() {
    return this.wda.getRotation();
  }

  async setRotation(deg: number) {
    return this.wda.setRotation(deg);
  }

  sizeCache?: {width: number, height: number, scale: number};
  async getWindowSize(): Promise<{width: number, height: number, scale: number}> {
    if(!this.sizeCache) {
      this.sizeCache = await this.wda.getWindowSize();
    }
    return this.sizeCache;
  }

  async isLocked(): Promise<boolean> {
    return this.wda.isLocked();
  }

  async lock() {
    return this.wda.lock();
  }

  async unlock() {
    return this.wda.unlock();
  }

  /**
   * 按下按钮
   * @param button: "home" | "volumeUp" | "volumeDown"
   */
  async pressButton(button: string) {
    return this.wda.pressButton(button);
  }

  /**
   * 创建一个TouchActions对象，可以作为 performTouchActions 的参数
   */
  createTouch() {
    return new TouchActions();
  }

  /**
   * 触发touch手势
   * @param action
   *
   * 单手指：
   * let touch = driver.createTouch();
   * touch.finger('0').press(100, 256).wait(50).move(50, 256).wait(10).release();
   * await driver.performTouchActions(touch);
   *
   * 多手指(放大操作):
   * let zoomIn = driver.createTouch();
   * zoomIn.finger("0").press(100, 256).wait(50).move(150, 256).wait(10).release();
   * zoomIn.finger("1").press(100, 256).wait(50).move(50, 256).wait(10).release();
   * await driver.performTouchActions(zoomIn);
   *
   * 多手指(缩小操作):
   * let zoomOut = driver.createTouch();
   * zoomOut.finger("0").press(50, 256).wait(50).move(100, 256).wait(10).release();
   * zoomOut.finger("1").press(150, 256).wait(50).move(100, 256).wait(10).release();
   * await driver.performTouchActions(zoomOut);
   */
  async performTouchActions(action: TouchActions) {
    return this.wda.performTouchActions(action);
  }

  async tap(x: number, y: number) {
    return this.wda.tap(x, y);
  }

  async doubleTap(x: number, y: number) {
    return this.wda.doubleTap(x, y);
  }

  /**
   * 发送文本
   * @param text
   * @param frequency
   */
  async sendKeys(text: string, frequency: number = 0.5) {
    return this.wda.sendKeys(text, frequency);
  }

  /**
   * 模拟io hid事件
   * @param page 0x05 实体按键 | 0x07 键盘
   * @param usage
   * @param duration
   */
  async performIoHidEvent(page: number, usage: number, duration = 0.05) {
    return this.wda.performIoHidEvent(page, usage, duration);
  }

  /**
   * 设置剪切板
   * @param content
   * 在17手机上测试似乎不生效
   */
  async setClipboard(content: string) {
    return this.wda.setClipboard(content);
  }

  /**
   * 获取剪切板
   * 在17手机上测试似乎不生效
   */
  async getClipboard() {
    return this.wda.getClipboard();
  }

  /**
   * 获取当前界面的节点树
   */
  async pageSource(): Promise<string> {
    return this.wda.pageSource();
  }

  async accessibleSource(): Promise<string> {
    return this.wda.accessibleSource();
  }

  /**
   * 获取屏幕截图，返回的是png格式的Buffer
   */
  async screenshot(scalingFactor = 100) {
    return this.wda.screenshot(scalingFactor);
  }

  /**
   * 在屏幕上滑动
   * @param fromX 起始位置的x
   * @param fromY 起始位置的y
   * @param toX 结束位置的x
   * @param toY 结束位置的y
   * @param duration 滑动总时间，单位秒，允许小数
   */
  async swipe(fromX: number, fromY: number, toX: number, toY: number, duration= 1) {
    return this.wda.swipe(fromX, fromY, toX, toY, duration);
  }

  /**
   * 与 swipe 类型，只是这里的坐标是百分比，屏幕中间是 0.5，而swipe是逻辑像素坐标
   * @param fromX 取值范围 (0, 1) 的开区间
   * @param fromY 取值范围 (0, 1) 的开区间
   * @param toX 取值范围 (0, 1) 的开区间
   * @param toY 取值范围 (0, 1) 的开区间
   * @param duration
   */
  async swipePercent(fromX: number, fromY: number, toX: number, toY: number, duration=1) {
    return this.wda.swipePercent(fromX, fromY, toX, toY, duration);
  }

  async appList(userOnly: boolean = true) {
    let apps = await executeIOS(this.udid, ['apps', '--all']);
    if (typeof apps === 'string') {
      return [];
    }
    if(userOnly) {
      apps = apps.filter((app: any) => app.ApplicationType === 'User');
    } else {
      apps = apps.filter((app: any) => app.ApplicationType !== 'Hidden');
    }
    //转成可读性高的格式
    apps = apps.map((app: any) => {
      return {
        bundleId: app.CFBundleIdentifier,
        name: app.CFBundleName,
        version: app.CFBundleShortVersionString
      };
    });
    return apps;
  }

  /**
   * 启动一个应用
   * @param bundleId
   * @param args
   * @param env
   */
  async appLaunch(bundleId: string, args: string[] =[], env: any ={}) {
    await this.wda.appLaunch(bundleId, args, env);
    return this.currentApp();
  }

  /**
   * 结束一个应用
   * @param bundleId
   */
  async appTerminate(bundleId: string) {
    await waitPromise(this.wda.appTerminate(bundleId), 5).catch((e: any)=>{
      //手机wda卡死无法杀进程的情况下，使用 ios kill {bundleId} 直接杀死应用
      return executeIOS(this.udid, ['kill', bundleId]);
    });
  }

  /**
   * 获取当前运行的应用的信息
   */
  async currentApp() {
    let current = await this.wda.currentApp(); //{"processArguments":{"env":{},"args":[]},"name":"","pid":1680,"bundleId":"com.gemd.iting"}
    if(current?.bundleId) {
      let appList = await this.appList(false);
      for(let app of appList) {
        if(current.bundleId == app.bundleId) {
          current.name = app.name;
          current.version = app.version;
          break;
        }
      }
    }
    return current;
  }

  /**
   * 等等某个元素出现
   * @param xpath
   * @param timeout
   */
  async waitElement(xpath: string, timeout: number = 5*60*1000): Promise<Element> {
    if(!timeout || (typeof timeout != 'number') ||timeout < 0) {
      timeout = 5*60*1000;
    }
    let startTime = Date.now();
    while(true) {
      let element = await this.findElement(xpath);
      if(element.elementId) {
        return element;
      }
      await waitMilliseconds(200, false);
      if(Date.now() -  startTime > timeout) {
        break;
      }
    }
    throw '等待元素超时';
  }

  /**
   * 查找单个元素
   * @param xpath
   */
  async findElement(xpath: string, retries = 3): Promise<Element> {
    let tries = 0;
    while(true) {
      try {
        let ret = await this.wda.sessionPost(`/element`, {
          using: 'xpath',
          value: xpath
        });
        let elementId = parseElementId(ret.value);
        let element = new Element((this as any) as IOSDriver, elementId);
        element.__xpath = xpath;
        return element;
      } catch (e) {
        if(++tries >= retries) {
          break;
        }
      }
    }
    return new Element((this as any) as IOSDriver, undefined);
  }

  /**
   * 查找多个元素，以数组返回
   * @param xpath
   */
  async findElements(xpath: string) {
    let tries = 0;
    while(true) {
      try {
        let ret = await this.wda.sessionPost(`/elements`, {
          using: 'xpath',
          value: xpath
        });
        let elements = [];
        for(let i = 0; i < ret.value.length; i++) {
          let elementId = parseElementId(ret.value[i]);
          let element = new Element((this as any) as IOSDriver, elementId);
          element.__xpath = xpath + (ret.value.length > 1 ? `[${i+1}]` : '');
          elements.push(element);
        }
        return elements;
      } catch (e) {
        if(++tries >= 3) {
          break;
        }
      }
    }
    return [] as Element[];
  }

  async waitElementByPredicate(predicate: string, timeout: number = 5*60*1000): Promise<Element> {
    if(!timeout || (typeof timeout != 'number') ||timeout < 0) {
      timeout = 5*60*1000;
    }
    let startTime = Date.now();
    while(true) {
      let element = await this.findElementByPredicate(predicate);
      if(element.elementId) {
        return element;
      }
      await waitMilliseconds(200, false);
      if(Date.now() -  startTime > timeout) {
        break;
      }
    }
    throw '等待元素超时';
  }


  async findElementByPredicate(predicate: string, retries = 3) {
    let tries = 0;
    while(true) {
      try {
        let ret = await this.wda.sessionPost(`/element`, {
          using: 'predicate string',
          value: predicate
        });
        let elementId = parseElementId(ret.value);
        let element = new Element((this as any) as IOSDriver, elementId);
        element.__predicate = predicate;
        return element;
      } catch (e) {
        if(++tries >= retries) {
          break;
        }
      }
    }
    return new Element((this as any) as IOSDriver, undefined);
  }

  async findElementsByPredicate(predicate: string) {
    let tries = 0;
    while(true) {
      try {
        let ret = await this.wda.sessionPost(`/elements`, {
          using: 'predicate string',
          value: predicate
        });
        let elements = [];
        for(let i = 0; i < ret.value.length; i++) {
          let elementId = parseElementId(ret.value[i]);
          let element = new Element((this as any) as IOSDriver, elementId);
          element.__predicate = predicate;//fixme 没办法指定第几个
          elements.push(element);
        }
        return elements;
      } catch (e) {
        if(++tries >= 3) {
          break;
        }
      }
    }
    return [] as Element[];
  }

  snapshotMaxDepth = 0;
  async setLayoutDepth(snapshotMaxDepth = 15) {
    if(this.alert_cache) {
      this.alert_cache.__the_original_depth__ = snapshotMaxDepth;
      //如果用户关注弹窗，要求的最小深度为 min_alert_layout_depth
      snapshotMaxDepth = Math.max(snapshotMaxDepth, min_alert_layout_depth);
    }
    this.snapshotMaxDepth = snapshotMaxDepth;
    await this.wda.appiumSettings({
      snapshotMaxDepth: snapshotMaxDepth
    });
  }

  async setExcludeElementTypes(types: string[] = []) {
    let intTypes = [];
    for(let type of types) {
      let intType = XCUIElementTypes[type];
      if(typeof intType === 'number') {
        intTypes.push(intType);
      }
    }
    try {
      await this.wda.appiumSettings({
        excludedElementTypes: intTypes.join(',')
      });
    } catch (e) {
      wdaLogger.error('setExcludeElementTypes error', e);
    }
  }

  async openUrl(url: string, bundleId?: string) {
    await this.wda.sessionPost('/url', {
      url, bundleId
    });
  }

  /**
   * 回到主界面
   */
  async home() {
    await this.wda.sendPost('/wda/homescreen', {});
  }

  async setSimulatedLocation(latitude: string, longitude: string, altitude?: string) {
    let data: any  = {
      latitude, longitude
    };
    if(altitude) {
      data.altitude = altitude;
    }
    await this.wda.sendPost('/wda/simulatedLocation', data);
  }

  async getSimulatedLocation(lat: string, lon: string) {
    let ret = await this.wda.sendGet('/wda/simulatedLocation');
    return ret.value as {
      latitude: string, longitude: string, altitude: string
    };
  }

  async clearSimulatedLocation() {
    await this.wda.send('DELETE', '/wda/simulatedLocation');
  }

  alert_cache?: Alert;
  async alert() {
    if(!this.alert_cache) {
      let __the_original_depth__ = (this as any as IOSDriver).snapshotMaxDepth;
      if((this as any as IOSDriver).snapshotMaxDepth < min_alert_layout_depth) {
        await this.setLayoutDepth(min_alert_layout_depth);
      }
      this.alert_cache = new Alert(this as any as IOSDriver);
      this.alert_cache.__the_original_depth__ = __the_original_depth__;
    }
    return this.alert_cache;
  }

  /**
   * 向ios系统相册里上传一张图片
   * @param image 本地文件的路径或者是Buffer
   */
  async uploadPhoto(image: string | Buffer) {
    let isHttp = typeof image === 'string' && /^https?:\/\//.test(image);
    if(isHttp) {
      const fileUrl = image as string;
      const tempDir = fs.mkdtempSync(path.join(app.getPath('temp'), 'hy-'));
      image = path.join(tempDir, 'push_iphone_image.temp');
      try {
        await new Downloader({
          url: fileUrl,
          fileName: 'push_iphone_image.temp',
          cloneFiles: false,
          directory: tempDir,
          timeout: 5 * 60 * 1000,
          maxAttempts: 3,
        }).download();
      } catch (e: any) {
        logger.error(e.message);
      }
    }
    let buf = image instanceof Buffer ? image : await readFile(image);
    if(isHttp) {
      fs.unlink(image as string, () => {});
      fs.rmdir(path.dirname(image as string), () => {});
    }
    let base64Image = buf.toString('base64');
    await this.wda.sendPost('/photos/upload', {
      image: base64Image
    });
    await this.allowWriteAlbums();
  }

  /**
   * 向ios系统相册里上传一个视频
   * @param video 一定要是一个mp4文件或者是mov文件
   */
  async uploadVideo(video: string | Buffer) {
    let isHttp = typeof video === 'string' && /^https?:\/\//.test(video);
    if(isHttp) {
      const fileUrl = video as string;
      const tempDir = fs.mkdtempSync(path.join(app.getPath('temp'), 'hy-'));
      video = path.join(tempDir, 'push_iphone_video.temp');
      try {
        await new Downloader({
          url: fileUrl,
          fileName: 'push_iphone_video.temp',
          cloneFiles: false,
          directory: tempDir,
          timeout: 5 * 60 * 1000,
          maxAttempts: 3,
        }).download();
      } catch (e: any) {
        logger.error(e.message);
      }
    }
    let buf = video instanceof Buffer ? video : await readFile(video);
    if(isHttp) {
      fs.unlink(video as string, () => {});
      fs.rmdir(path.dirname(video as string), () => {});
    }
    let base64Video = buf.toString('base64');
    await this.wda.sendPost('/videos/upload', {
      video: base64Video
    });
    await this.allowWriteAlbums();
  }

  private async allowWriteAlbums() {
    //检测授权弹窗并自动点击接受
    let layoutDepth = (this as any as IOSDriver).snapshotMaxDepth;
    await this.setLayoutDepth(min_alert_layout_depth);
    try {
      let alert = new Alert(this as any as IOSDriver);
      await waitSeconds(1);
      let text = await alert.text();
      if(text && text.includes('WebDriverAgentRunner-Runner')) {
        let buttons = await alert.buttons();
        let longest = '';
        for(let button of buttons) {
          if(button.length > longest.length) {
            longest = button;
          }
        }
        await alert.accept(longest);
      }
    } finally {
      await this.setLayoutDepth(layoutDepth);
    }
  }

}

export class IOSDriver extends IOSDriverApi {

  wdaBoundId: string = ''; // com.huayoung.WebDriverAgentRunner'; //.xctrunner';
  wdaBoundVersion: string = '';

  wdaProcess?: any;
  wdaPort!: number;
  mjpegPort!: number;

  healthChecker: Heartbeat;

  constructor(udid: string) {
    super();
    this.udid = udid;
    this.healthChecker = new Heartbeat(async ()=>{
      await this.checkHealth();
    }, 30000);
  }

  async create() {
    if(this.wda) {
      return;
    }
    let wdaBound = await this.getInstalledWDA();
    if(!wdaBound || !wdaBound.boundId) {
      //参考 https://blog.csdn.net/m0_37268414/article/details/133904154 真机部分进行安装
      throw '当前设备未安装 WebDriverAgent，无法进行下一步操作';
    }
    this.wdaBoundId = wdaBound.boundId.substring(0, wdaBound.boundId.indexOf('.xctrunner'));
    this.wdaBoundVersion = wdaBound.version;
    await this.mountDeveloperDiskImage();

    this.wda = await this.startWDA();
    await this.wda.createSession();
    await this.wda.appiumSettings({
      snapshotMaxDepth: 0,
      waitForIdleTimeout: 0,
      animationCoolOffTimeout: 0
    });
    this.healthChecker.start();
    // logger.log('默认将snapshotMaxDepth设置成1');
  }

  async checkHealth() {
    let health = false;
    try {
      let status = await this.wda.status();
      if(status?.value?.ready == true) {
        health = true;
      }
    } catch (e) {
    }
    if(!health) {
      if(this.wdaProcess && !this.wdaProcess.killed) {
        this.wdaProcess.kill();
      }
      //@ts-ignore
      this.wda = undefined;
    }
  }

  async close() {
    this.healthChecker.stop();
    if(this.wdaProcess && !this.wdaProcess.killed) {
      this.wdaProcess.kill();
    }
    //@ts-ignore
    this.wda = undefined;
    this.wdaPort = 0;
    this.mjpegPort = 0;
    this.wdaProcess = undefined;
    this.wdaBoundId = '';
  }

  wdaUrl() {
    return `http://127.0.0.1:${this.wdaPort}`;
  }

  mjpegUrl() {
    return `http://127.0.0.1:${this.mjpegPort}`;
  }

  async updateMjpegSettings(scalingFactor = 50, mjpegFps = 10) {
    if(mjpegFps <= 0) {
      mjpegFps = 10;
    }
    mjpegFps = Math.min(mjpegFps, 10);
    await this.wda.appiumSettings({
      mjpegServerFramerate: mjpegFps, mjpegScalingFactor: scalingFactor
    });
  }

  async deviceInfo() {
    let info = await executeIOS(this.udid, ['info']);
    return info;
  }

  getWDA() {
    return this.wda;
  }

  public async getInstalledWDA() {
    let apps = await this.appList(true);
    for(let app of apps) {
      if(app.name === "WebDriverAgentRunner-Runner") {
        return {
          boundId: app.bundleId, version:app.version
        };
      }
    }
    return undefined;
  }

  startWDAPromise?: Promise<WDAClient>;
  private async startWDA() {
    if(this.startWDAPromise) {
      return this.startWDAPromise;
    }
    this.startWDAPromise = new Promise(async (resolve, reject)=>{
      const go_ios = goIosPath()
      this.wdaPort = await getAFreePort();
      this.mjpegPort = await getAFreePort();
      let startWDACommand = `--udid=${this.udid} runwda --serverlocalport=${this.wdaPort} --mjpeglocalport=${this.mjpegPort} --bundleid=${this.wdaBoundId}.xctrunner --testrunnerbundleid=${this.wdaBoundId}.xctrunner --xctestconfig=WebDriverAgentRunner.xctest --nojson`;
      // startWDACommand += ` --env=MJPEG_SCALING_FACTOR=${MJPEG_SCALING_FACTOR}`;
      // startWDACommand += ` --env=MJPEG_FRAMERATE=${MJPEG_FRAMERATE}`;
      this.wdaProcess = spawn(go_ios, startWDACommand.split(' '));
      wdaLogger.info(this.logTag(), `wdaurl=${this.wdaUrl()}:, mjpegurl=${this.mjpegUrl()}`);
      this.wdaProcess.stdout.on('data', (data: any)=>{
        wdaLogger.info(this.logTag(), data?.toString());
      });
      this.wdaProcess.stderr.on('data', (data: any)=>{
        wdaLogger.error(this.logTag(), data?.toString());
      });
      this.wdaProcess.on('close', () => {
        this.wdaProcess?.stdout.removeAllListeners('data');
        this.wdaProcess?.stderr.removeAllListeners('data');
      });
      let wda = new WDAClient(this.wdaUrl());
      for(let i = 0; i < 10; i++) {
        try {
          let status = await wda.status();
          if(status?.value?.ready == true) {
            resolve(wda);
            return wda;
          } else {
            await waitSeconds(1, false);
          }
        } catch (e) {
          await waitSeconds(1, false);
        }
      }
      reject('启动wda失败');
    });
    this.startWDAPromise.then(()=>{
      setTimeout(()=>{
        this.emit('wda-ready');
      }, 1000);
    }).finally(()=>{
      this.startWDAPromise = undefined;
    });
    return this.startWDAPromise;
  }

  mountPromise?: Promise<any>;
  private async mountDeveloperDiskImage() {
    if(this.mountPromise) {
      return this.mountPromise;
    }
    this.mountPromise = executeIOS(this.udid, ['image', 'auto', `--basedir=${path.resolve(__dirname, '../extra/devimages')}`]);
    this.mountPromise.then((ret) => {
      wdaLogger.log(ret);
    }).catch(error => {
      wdaLogger.error(error)
    }).finally(()=>{
      this.mountPromise = undefined;
    });
  }

  logTag(): string {
    return `[ios:${this.udid}]`;
  }

}
