import {BrowserWindow, ipcMain} from 'electron';
import path from 'path';
import {androidDeviceManager, iosDeviceManager, portalRpcClient} from '@e/components/backendTask';
import appConfig from '@e/configs/app';
import {runAiAgent, stopPreview} from '@e/rpa/task_schedule';
import {AndroidDevice} from "@e/mobile/android/AndroidDevice";
import {QCloudAndroidDevice} from "@e/mobile/android/QCloudAndroidDevice";

// 需要监听网络变化的房间ID列表
export const watchNetworkChangeRoomIds: any[] = [];

export class MobileHelperWindow {
  helperWindow: BrowserWindow;

  constructor() {
    this.helperWindow = new BrowserWindow({
      title: 'Mobile Helper',
      show: false && !!appConfig.DEBUG,
      width: 400,
      height: 300,
      maximizable: false,
      fullscreenable: false,
      webPreferences: {
        // devTools: false,
        devTools: true,
        preload: path.join(__dirname, 'helper/mobile_helper.js'),
        nodeIntegration: true,
        // enableRemoteModule: true,
        webSecurity: false,
        backgroundThrottling: false,
      },
    });
    this.init().then(() => {});
    //可访问 http://localhost:47326/openDevtools/mobileHelperWindow 打开
    // if (process.env.NODE_ENV === 'development') {
    //   this.helperWindow.webContents.openDevTools();
    // }
  }

  async init() {
    await this.helperWindow.loadFile(path.join(__dirname, './helper/index.html'));

    this.registerIpc();

    portalRpcClient.onAppEmit('mobile-initiate-video-room', (data) => {
      this.helperWindow.webContents.send('mobile-initiate-video-room', data);
    });
    portalRpcClient.onAppEmit('restart-adb-server', async (data) => {
      androidDeviceManager.restartAdbServer();
      iosDeviceManager.restartIosServer();
    });
  }

  public beforeExecute(item: any) {
    this.helperWindow.webContents.send('before-rpa-item-execute', item);
  }
  public afterExecute(item: any) {
    this.helperWindow.webContents.send('after-rpa-item-execute', item);
  }

  private ipcRegistered = false;
  registerIpc() {
    //获取控制android设备的ws连接
    ipcMain.handle('mobile-forward-scrcpy', async (evt, data) => {
      const { udid, connectType, platform, smallPreview } = data;
      if('Android' === platform) {
        await androidDeviceManager.prepareDevice(udid, connectType);
        if(connectType === 'QCloud') {
          let wsUrl = await androidDeviceManager.forwardMjpeg(udid, smallPreview);
          return wsUrl;
        } else {
          let wsUrl = await androidDeviceManager.forwardScrcpy(udid);
          return wsUrl;
        }
      } else {
        await iosDeviceManager.prepareDevice(udid);
        let wsUrl = await iosDeviceManager.forwardMjpeg(udid, smallPreview);
        return wsUrl;
      }
    });
    ipcMain.handle('mobile-join-room', async (evt, data) => {
      const { roomId } = data;
      let room = portalRpcClient.joinChatRoom(roomId);
      ((_roomId, _evt) => {
        room.addListener('users-change', (event) => {
          event.userCount = event.data;
          this.helperWindow.webContents.send('mobile-room-users-change', roomId, event);
        });
        room.addListener('message', (event: any) => {
          const message = event.data;
          this.helperWindow.webContents.send('mobile-room-message', roomId, message);
        });
      })(roomId, evt);
    });
    ipcMain.handle('mobile-send-to-chat-room', async (evt, payload: any) => {
      const { roomId, data } = payload;
      portalRpcClient.sendToChatRoom(roomId, data);
    });
    ipcMain.handle('mobile-leave-room', async (evt, data) => {
      const { roomId } = data;
      portalRpcClient.leaveChatRoom(roomId);
    });
    ipcMain.handle('mobile-kickInUse', async (evt, data: any) => {
      const { udids } = data;
      udids?.forEach((udid: string) => {
        //无法直接通过udid区分是ios还是android，直接各调一次
        androidDeviceManager.kickInUse(udid);
        iosDeviceManager.kickInUse(udid);
      });
    });
    ipcMain.handle('hy.scrcpy.command', async (evt, data) => {
      let ret: any = false;
      if (!!data.command) {
        const { teamId, udid, connectType, force, platform, shell, script } = data;
        const device = platform === 'Android' ? (await androidDeviceManager.prepareDevice(udid, connectType)) : (await iosDeviceManager.prepareDevice(udid));
        if (!device) {
          ret = 'Device Not Found';
        } else {
          try {
            switch (data.command) {
              case 'textExtraction':
                /**
                 * 文本提取
                 * translate 是否需要翻译，默认true
                 * lang 中文 | 英文 翻译目标语言，默认中文。因为lang是直接传递给大模型的，所以lang使用自然语言就可以了
                 * mode 使用的模型，目前没用到
                 */
                const { translate, lang, mode } = data;
                ret = await device.textExtraction(teamId, translate, lang, mode);
                break;
            }
            if (!['textExtraction'].includes(data.command)) {
              if(device instanceof AndroidDevice) {
                switch (data.command) {
                  case 'adbShell':
                    ret = await device.runShellCommandAdbKit(script);
                    break;
                  case 'adbCommand':
                    ret = await device.runCommandAdbKit(shell, script);
                    break;
                  case 'installHyKeyboard': //安装并启用花漾键盘
                    ret = await device.installHyKeyboard(force);
                    break;
                  case 'installAppium':
                    await device.installAppium();
                    break;
                  case 'installVpick': //安装vpick (腾讯云手机一键新机要用到的组件)
                    if(connectType === 'QCloud') {
                      await (device as QCloudAndroidDevice).installVpick();
                    } else {
                      throw '只有腾讯云手机支持该功能';
                    }
                    break;
                  case 'qMobile/changeModel':
                    if(connectType === 'QCloud') {
                      const { model } = data;
                      await (device as QCloudAndroidDevice).changeModel(model);
                    } else {
                      throw '只有腾讯云手机支持该功能';
                    }
                    break;
                  case 'aiAgent/prompt':
                    const { rpaType, prompt = '', pluginJsUrl = '' } = data.data;
                    ret = await new Promise((resolve) => {
                      runAiAgent(
                        data.data?.teamId,
                        rpaType,
                        data.data?.shopOrMobileId,
                        prompt,
                        pluginJsUrl,
                        (msg) => {
                          portalRpcClient.sendToChatRoom(
                            data.roomId,
                            JSON.stringify({
                              action: 'aiAgent/step',
                              data: msg,
                            }),
                          );
                        },
                      ).then((res) => {
                        resolve(JSON.stringify(res));
                      });
                    });
                    break;
                  case 'aiAgent/stopTask':
                    ret = await new Promise((resolve) => {
                      stopPreview(data.data?.shopOrMobileId);
                      resolve(JSON.stringify({ success: true }));
                    });
                    break;
                  case 'pushFile':
                    let { source, dir } = data;
                    ret = await device.pushFileWithProgress(source, dir, (payload) => {
                      portalRpcClient.sendToChatRoom(
                        data.roomId,
                        JSON.stringify({
                          action: 'pushFile/progress',
                          data: {
                            mobileId: data.mobileId,
                            source,
                            ...payload,
                          },
                        }),
                      );
                    });
                    break;
                  case 'watchNetworkChange':
                    watchNetworkChangeRoomIds.push(data.roomId);
                    setTimeout(() => {
                      const index = watchNetworkChangeRoomIds.indexOf(data.roomId);
                      if (index > -1) {
                        watchNetworkChangeRoomIds.splice(index, 1);
                      }
                    }, 60 * 1000); //60秒后自动清除
                    break;
                  case 'getClipboard':
                    ret = await device.getClipboardByHYKeyboard();
                    break;
                  default:
                    ret = `Unsupported command ${data.command}`;
                }
              } else {
                //ios
                switch (data.command) {
                  case 'installWDA':
                    ret = await device.tryInstallWDA();
                    break;
                  case 'updateMjpegSettings':
                    const { scalingFactor = 50, mjpegFps = 10, smallPreviewDuration  = 5 } = data;
                    ret = await device.updateMjpegSettings(scalingFactor, mjpegFps, smallPreviewDuration);
                    break;
                  case 'iosShell':
                    //执行一个 ios 命令，并不是真正在ios上执行命令，能做的事情参考：https://github.com/danielpaulus/go-ios?tab=readme-ov-file#features
                    let { script } = data;
                    ret = await device.runIosCommand(script);
                    break;
                  case 'rotateScreen':
                    ret = await device.rotateScreen();
                    break;
                  case 'clearSimulatedLocation':
                    ret = await device.clearSimulatedLocation();
                    break;
                  case 'wdaSend':
                    ret = await device.wdaSend(data.method, data.path, data.data);
                    break;
                  case 'pushFile':
                    const { video, image } = data;
                    const driver = await device.connectDriver();
                    if(video) {
                      await driver.uploadVideo(video);
                    }
                    if(image) {
                      await driver.uploadPhoto(image);
                    }
                    break;
                }
              }
            }
          } catch (e: any) {
            e = e || { message: '未知错误调用失败' };
            ret = e.message || e.toString();
          }
        }
      }
      return ret;
    });
  }

  close() {
    ipcMain.removeHandler('mobile-forward-scrcpy');
    ipcMain.removeHandler('mobile-join-room');
    ipcMain.removeHandler('mobile-send-to-chat-room');
    ipcMain.removeHandler('mobile-leave-room');
    ipcMain.removeHandler('mobile-kickInUse');
    ipcMain.removeHandler('trigger-mobile-watchers-change-event');
    ipcMain.removeHandler('hy.scrcpy.command');
    portalRpcClient.removeAllListeners('mobile-initiate-video-room');
    portalRpcClient.removeAllListeners('restart-adb-server');
    this.helperWindow.close();
  }
}
