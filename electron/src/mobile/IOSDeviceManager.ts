import fs from 'fs-extra';
import {IOSDevice} from "@e/mobile/ios/IOSDevice";
import {executeIOS, goIosPath} from "@e/mobile/ios/utils";
import {IOSTunnelService} from "@e/mobile/ios/service/tunnel";
import {waitSeconds} from "@e/rpa/utils";
import {MobileDto} from "@e/mobile/cons/common";
import {IOSStreamService} from "@e/mobile/ios/service/stream";
import logger from "@e/services/logger";
import {AppiumService} from "@e/mobile/android/adb/Appium";
import {AdbClient} from "@e/mobile/android/adb/AdbClient";

export class IOSDeviceManager {

  private static instance?: IOSDeviceManager;
  scanInterval: any;
  iosTunnelService = new IOSTunnelService();
  iosStreamService = new IOSStreamService((udid)=> this.devices.get(udid));

  devices: Map<string, IOSDevice> = new Map<string, IOSDevice>();

  public static getInstance(): IOSDeviceManager {
    if (!IOSDeviceManager.instance) {
      IOSDeviceManager.instance = new IOSDeviceManager();
    }
    return IOSDeviceManager.instance;
  }

  async start() {
    const go_ios = goIosPath();
    const hasGoIOS = await fs.pathExists(go_ios);
    if (hasGoIOS) {
      this.scanInterval = setTimeout(()=> this.scan(), 3000);
      this.iosStreamService.start().catch(()=>{});
    }
  }

  stop() {
    clearTimeout(this.scanInterval);
    this.iosTunnelService.stop().catch(()=>{});
    this.iosStreamService.stop().catch(()=>{});
  }

  public async forwardMjpeg(udid: string, smallPreview = false): Promise<string> {
    let device = this.devices.get(udid);
    if (device) {
      if (device.isConnected()) {
        return this.iosStreamService.getWsUrl() + '?udid=' + udid + '&smallPreview=' + (!!smallPreview);
      } // else 说明已经下线
      device.reportState('OFFLINE').catch(() => {});
    } else {
      new IOSDevice(udid, 'USB').reportState('OFFLINE').catch(() => {});
    }
    throw new Error(`设备未连接（${udid}）`);
  }

  public kickInUse(udid: string) {
    let device = this.devices.get(udid);
    device?.kickInUse();
  }

  /**
   * 获取设备，如果是arm cloud，传入的udid其实是padCode
   * @param udid
   */
  async prepareDevice(udid: string): Promise<IOSDevice|undefined> {
    let device = this.devices.get(udid);
    return device;
  }

  async restartIosServer() {
    logger.info('开始重启ios服务');
    this.stop();
    this.devices.forEach((device) => {
      device.disconnectIOS();
      device.setState('offline');
    });
    this.devices.clear();
    await waitSeconds(5);
    this.start();
  }

  public async listDevices(teamId: number, excludeUdids: string[]): Promise<MobileDto[]> {
    excludeUdids = excludeUdids || [];
    let result: MobileDto[] = [];
    for(let [udid, device] of this.devices) {
      if (device.isConnected() && !excludeUdids.includes(udid)) {
        result.push(await device.toMobileDto());
      }
    }
    return result;
  }

  private async scan() {
    try {
      clearTimeout(this.scanInterval);
      let ret = await executeIOS('', 'list --detail'.split(' '));
      let deviceList = ret?.deviceList || [];
      let connectedDevices: string[] = deviceList.map((d: any) => d.Udid);
      this.devices.forEach((device, udid)=> {
        if(!connectedDevices.includes(udid)) {
          device.setState('offline');
        }
      });
      for(let d of deviceList) {
        let udid = d.Udid;
        let device = this.devices.get(udid);
        if(!device) {
          let productVersion = parseInt(d.ProductVersion);
          if(productVersion > 16) {
            await this.startIOSTunnel();
          }

          device = new IOSDevice(udid, 'USB');
          device.name = d.ProductName;
          device.version = d.ProductVersion;
          this.devices.set(udid, device);
        }
        device.setState('device');//和android保持一致，用device代替online
      }
    } finally {
      let interval = this.devices.size > 0 ? 3000 : 30000;
      this.scanInterval = setTimeout(()=> this.scan(), interval);
    }
  }

  private async startIOSTunnel() {
    if(await this.iosTunnelService.running()) {
      return;
    }
    await this.iosTunnelService.stop();
    await this.iosTunnelService.start();
    for(let i = 0; i < 10; i++) {
      if(await this.iosTunnelService.running()) {
        break;
      }
      await waitSeconds(1, false);
    }
  }

}
