import { app, BrowserWindow, screen, shell } from 'electron';
import path from 'path';
import yargs from 'yargs';
import logger from '@e/services/logger';
import db from '@e/components/db';
import { DEFAULT_WIN_OPTS, loadURL, resolveUrl } from '@e/utils/utils';
import * as windowUtil from '@e/utils/window';
import {
  bringWindowFront,
  closeLoginWindow,
  closeMainWindow,
  getCurrentWindow,
  getMainWindow,
  openLoginWindow,
  waitAllChromiumClosed,
  openMainWindow,
} from '@e/utils/window';
import request from '@e/services/request';
import { backendTaskIns } from '@e/components/backendTask';
import { openShopByToken } from '@e/utils/runtime';
import { showInitWindow } from '@e/main';
import appConfig from '@e/configs/app';

let isInit = false;
let appReadied = false;
// APP 自定义协议
export const PROTOCOL_CODE = process.env.PROTOCOL_CODE || 'huayoung';
logger.info(`[APP] PROTOCOL_CODE：${PROTOCOL_CODE}`);
// 验证是否为自定义协议的链接
export const PROTOCOL_REGEXP = new RegExp(`^${PROTOCOL_CODE}://`);
export const SHORTCUT_REGEXP = new RegExp(`^${PROTOCOL_CODE}://shortcut/([a-zA-Z0-9-]+)`);

// 注册自定义协议
function setDefaultProtocol() {
  const agreement = PROTOCOL_CODE;

  if (!app.isDefaultProtocolClient(agreement)) {
    app.removeAsDefaultProtocolClient(agreement); // 删除自定义协议 然后再重新注册
    // 开发模式下在window运行需要做兼容
    if (process.env.NODE_ENV === 'development' && process.platform === 'win32') {
      // 设置electron.exe 和 app的路径
      app.setAsDefaultProtocolClient(agreement, process.execPath, [path.resolve(process.argv[1])]);
    } else {
      app.setAsDefaultProtocolClient(agreement);
    }
    logger.verbose(`[APP] 注册自定义协议：${PROTOCOL_CODE}`);
  }
}

/**
 * 是否未同一个用户的 jwt
 */
export function isSameUserJwt(oldJwt: string, newJwt: string) {
  const oldUserInfoStr = oldJwt?.split?.('.')[1] ?? '';
  const newUserInfoStr = newJwt?.split?.('.')[1] ?? '';

  try {
    const oldUserInfo = JSON.parse(Buffer.from(oldUserInfoStr, 'base64').toString());
    const newUserInfo = JSON.parse(Buffer.from(newUserInfoStr, 'base64').toString());
    return oldUserInfo.sub === newUserInfo.sub;
  } catch (e) {
    //
  }
  return false;
}

export async function handleOpenAppUrl(url: string) {
  const isProtocol = PROTOCOL_REGEXP.test(url);
  let _url = url;
  let isSameUserJwtFlag = false;
  if (SHORTCUT_REGEXP.test(url)) {
    handleArgs([`--token=${SHORTCUT_REGEXP.exec(url)![1]}`]);
    return;
  }
  if (db.isRuntimeMode()) {
    return;
  }
  if (isProtocol) {
    try {
      const urlObj = new URL(url);
      const jwt = urlObj.searchParams.get('jwt');
      if (jwt) {
        isSameUserJwtFlag = isSameUserJwt(db.getJwt()! || '', jwt);
        // 是否需要重新登录
        if (db.getJwt() && !isSameUserJwtFlag) {
          await waitAllChromiumClosed();
          // 注销当前登录的用户
          try {
            backendTaskIns.stop();
            await request('/api/account/logout');
          } catch (e) {}
          await openLoginWindow();
          if (getMainWindow()) {
            closeMainWindow();
          }
        }
        await db.getDb().set('account', { jwt }).write();
        urlObj.searchParams.delete('jwt');
        _url = urlObj.href;
      }
    } catch (e) {
      logger.error('[APP] parse url failed', e);
    }
    if (!db.getJwt()) {
      if (getCurrentWindow()) {
        bringWindowFront(getCurrentWindow());
      } else {
        openLoginWindow();
      }
      return;
    }
    const targetUrl = resolveUrl(db.getPortalUrl(), _url.substr(PROTOCOL_CODE.length + 3));
    // RPA流程定义编辑
    if (/\/rpa\/flow\/\d+/.test(_url)) {
      const screenSize = screen.getPrimaryDisplay().size;
      const win = new BrowserWindow({
        ...DEFAULT_WIN_OPTS,
        width: Math.min(screenSize.width, 900),
        height: screenSize.height - 60,
        minWidth: 800,
        minHeight: 600,
        x: 0,
        y: 0,
      });
      await loadURL(win, targetUrl);
    } else if (/\/rpaTaskMonitor\/\d+/.test(_url)) {
      // 云端执行实时查看
      logger.info(`[APP] 打开RPA任务实时查看窗口: ${targetUrl}`);
      const win = new BrowserWindow({
        ...DEFAULT_WIN_OPTS,
        parent: getCurrentWindow() || undefined,
      });
      // win.webContents.openDevTools();
      await loadURL(win, targetUrl);
      const portalUrl = db.getPortalUrl();
      win.webContents.setWindowOpenHandler(({ url }) => {
        let origin = '';
        try {
          origin = new URL(url).origin;
        } catch (e) {}
        // 不在同一个域下，用外部浏览器打开
        if (origin && !portalUrl.includes(origin)) {
          shell.openExternal(url);
          return { action: 'deny' };
        }
        return { action: 'allow' };
      });
    } else {
      const path = _url.substr(PROTOCOL_CODE.length + 3);
      if (!path || path === '/') {
        if (getCurrentWindow()) {
          bringWindowFront(getCurrentWindow());
          if (url !== _url && !isSameUserJwtFlag) {
            getCurrentWindow()?.reload();
          }
        } else {
          openLoginWindow();
        }
      } else {
        logger.info(`[APP] open url: ${targetUrl}`);
        openMainWindow(targetUrl);
        closeLoginWindow();
      }
    }
  } else {
    if (getCurrentWindow()) {
      bringWindowFront(getCurrentWindow());
    } else {
      openLoginWindow();
    }
  }
}

export async function handleArgs(_argv = process.argv.slice(1)) {
  const argv = yargs(_argv).argv;
  if (argv.token) {
    openShopByToken(argv.token as string, appConfig.isOEM);
  }
  logger.info('[APP] argv', argv);
}

// 监听自定义协议唤起
function watchProtocol() {
  app.on('second-instance', (evt, argv) => {
    logger.info('[APP] second-instance', JSON.stringify(argv));
    const _argv = yargs(argv).argv;
    const openByProtocolUrl = argv.find((v) => PROTOCOL_REGEXP.test(v));
    if (openByProtocolUrl) {
      handleOpenAppUrl(openByProtocolUrl);
    } else if (_argv.token) {
      handleArgs(argv.slice(1));
    } else {
      const currentWindow = windowUtil.getCurrentWindow();
      if (currentWindow) {
        try {
          if (currentWindow.isMinimized()) currentWindow.restore();
          currentWindow.show();
        } catch (e) {}
      } else {
        showInitWindow();
      }
    }
  });
}

/**
 * 注册、监听APP自定义协议
 */
export default {
  // 初始化
  init() {
    if (isInit) {
      return;
    }
    isInit = true;
    let urlToOpen = '';
    // mac唤醒应用 会激活open-url事件 在open-url中判断是否为自定义协议打开事件
    app.on('open-url', (event, url) => {
      logger.info('[APP] open-url', url);
      if (!appReadied) {
        process.argv.push(url);
        urlToOpen = url;
      } else {
        handleOpenAppUrl(url);
      }
    });
    app.whenReady().then(() => {
      appReadied = true;
      setDefaultProtocol();
      if (!db.isRpaExecutor()) {
        watchProtocol();
      }
      if (urlToOpen) {
        handleOpenAppUrl(urlToOpen);
      }
    });
  },
};
