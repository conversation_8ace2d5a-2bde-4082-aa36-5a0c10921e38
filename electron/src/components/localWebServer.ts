import { app } from 'electron';
import Koa from 'koa';
import bodyParser from 'koa-bodyparser';
import Router from '@koa/router';
import cors from '@koa/cors';
import { AddressInfo } from 'net';
import path from 'path';
import { getHeapSpaceStatistics, getHeapStatistics, writeHeapSnapshot } from 'v8';
import moment from 'moment';
import logger from '@e/services/logger';
import { handleOpenAppUrl, isSameUserJwt, PROTOCOL_CODE } from '@e/components/protocol';
import db from '@e/components/db';
import {
  getChromium,
  getMainWindow,
  getCurrentWindow,
  waitAllChromiumClosed,
  openLoginWindow,
  closeMainWindow,
  openShopWindow,
} from '@e/utils/window';
import { isWinPlatform, resolveUrl } from '@e/utils/utils';
import { createShopShortcut } from '@e/components/shopShortcut';
import { openShopByToken } from '@e/utils/runtime';
import { popupMobile } from '@e/utils/ipc/mobile';
import { backendTaskIns, baseBackendTaskIns } from '@e/components/backendTask';
import { appUpdater } from '@e/components/updater';
import { killBrowserProcess } from '@e/utils/ipc';
import { checkEmailServer } from '@e/utils/email';
import appConfig from '@e/configs/app';
import request from '@e/services/request';
import puppeteer from 'donkey-puppeteer-core';
import KernelManage from '@e/components/kernelManage';
import fs from 'fs-extra';
import { spawn } from 'child_process';

const portQueue = appConfig.isOEM
  ? [57326, 57327, 57328, 57329, 57330]
  : [47326, 47327, 47328, 47329, 47330];

export class LocalWebServer {
  private app: Koa | undefined;
  private port: number | undefined;
  private rpc_events: any[] = [];

  constructor() {
    this.init();
  }

  async init() {
    this.app = new Koa();
    this.app.use(bodyParser());
    this.app.use(cors());
    const router = new Router();
    router.get('/ping', (ctx) => {
      ctx.response.body = { version: app.getVersion(), success: true };
    });
    router.get('/info', (ctx) => {
      ctx.response.body = {
        version: app.getVersion(),
        sysPres: db.getSysPres(),
        success: true,
      };
    });
    router.get('/reboot', (ctx) => {
      ctx.response.body = {
        version: app.getVersion(),
        success: true,
      };
      app.relaunch();
      app.exit(0);
    });
    router.get('/quitAndInstall', (ctx) => {
      ctx.response.body = {
        success: true,
      };
      appUpdater.quitAndInstall();
    });
    router.get('/getDeviceId', (ctx) => {
      ctx.response.body = {
        deviceId: db.getDeviceIdFromCookies(),
        success: true,
      };
    });
    router.get('/checkJwt', (ctx) => {
      const { query } = ctx.request;
      ctx.response.body = {
        hasJwt: !!db.getJwt(),
        isSame: !!db.getJwt() && isSameUserJwt((query.jwt ?? '') as string, db.getJwt()!),
        success: true,
      };
    });
    router.get('/setJwt', async (ctx) => {
      const { query } = ctx.request;
      if (!query.jwt) {
        ctx.throw(400, 'jwt is required');
      }
      // @ts-ignore
      const isSameUserJwtFlag = isSameUserJwt(db.getJwt()!, query.jwt);
      if (!isSameUserJwtFlag) {
        await waitAllChromiumClosed();
        // 注销当前登录的用户
        try {
          backendTaskIns.stop();
          await request('/api/account/logout');
        } catch (e) {}
        await openLoginWindow();
        if (getMainWindow()) {
          closeMainWindow();
        }
        await db.getDb().set('account', { jwt: query.jwt }).write();
        getCurrentWindow()?.reload();
      }
      ctx.response.body = {
        success: true,
      };
    });
    router.post('/rpc/event', async (ctx) => {
      const { body } = ctx.request;
      const { jwt, url, payload, eventName } = body as any;
      const urlObj = new URL(`${PROTOCOL_CODE}://${url}`);
      if (eventName) {
        this.rpc_events.push({
          eventName: eventName,
          timestamp: new Date().getTime(),
          payload: payload,
        });
        urlObj.searchParams.set('_rpcEvent', eventName);
      }
      if (jwt) {
        urlObj.searchParams.set('jwt', jwt);
      }
      // 需要跳到特定的
      await handleOpenAppUrl(urlObj.toString());
      ctx.response.body = {
        success: true,
      };
    });
    router.post('/rpc/checkEmailService', async (ctx) => {
      const { body } = ctx.request;
      const { host, port, secure, auth, ...rest } = body as any;
      const res = await checkEmailServer({ host, port, secure, auth, ...rest });
      ctx.response.body = res;
    });
    router.post('/rpc/visitShop', (ctx) => {
      const { body } = ctx.request;
      const { shopId, teamId, target, jwt = '' } = body as any;
      if (target === 'DETAIL' || !target) {
        // 店铺详情
        handleOpenAppUrl(`${PROTOCOL_CODE}://team/${teamId}/shopManage/all/${shopId}?jwt=${jwt}`);
        ctx.response.body = { success: true };
      } else if (target === 'BROWSER') {
        // 打开浏览器
        const chromium = getChromium({ shopId });
        if (chromium) {
          chromium.bringToFront();
          ctx.response.body = {
            success: true,
          };
        } else {
          handleOpenAppUrl(
            `${PROTOCOL_CODE}://team/${teamId}/shopManage/all/${shopId}?jwt=${jwt}&access=true`,
          );
          ctx.response.body = { success: true };
        }
      } else {
        // 打开目标网站
        const chromium = getChromium({ shopId });
        if (chromium) {
          chromium.handleCmd('open-url', { url: target });
          ctx.response.body = {
            success: true,
          };
        } else {
          handleOpenAppUrl(
            `${PROTOCOL_CODE}://team/${teamId}/shopManage/all/${shopId}?jwt=${jwt}&access=true&url=${target}`,
          );
          ctx.response.body = { success: true };
        }
      }
    });
    router.post('/rpc/openBrowsers', async (ctx) => {
      try {
        const { body } = ctx.request;
        const { shopIds, teamId, jwt } = body as any;
        if (!shopIds || !Array.isArray(shopIds)) {
          ctx.throw(400, 'shopIds array is required');
        }
        // 判断teamId 是否存在
        if (!teamId) {
          ctx.throw(400, 'teamId is required');
        }
        await handleOpenAppUrl(`${PROTOCOL_CODE}://team/${teamId}/shopManage/all?jwt=${jwt}`);

        const results = [];

        for (const shopId of shopIds) {
          // 这里需要判断一下,如果shopId的会话已经打开,则直接聚焦窗口
          const chromium = getChromium({ shopId });
          if (chromium) {
            chromium.bringToFront();
            results.push({
              shopId,
              success: true,
            });
            continue;
          }

          try {
            // 调用openShopWindow函数
            await openShopWindow({ shopId, teamId }, (shopId, uuid, vo) => {
              // 回调函数处理进度信息
              logger.info(`[RPC] openBrowsers progress for shop ${shopId}: ${JSON.stringify(vo)}`);
            });

            results.push({
              shopId,
              success: true,
            });
          } catch (error: any) {
            results.push({
              shopId,
              success: false,
              error: error.message,
            });
          }
        }

        ctx.response.body = {
          success: true,
          results,
          total: shopIds.length,
        };
      } catch (error: any) {
        ctx.response.body = {
          success: false,
          error: error.message,
        };
      }
    });
    router.get('/checkBrowserAlive', (ctx) => {
      const { query } = ctx.request;
      ctx.response.body = {
        alive: !!getChromium({ ...query }),
        success: true,
      };
    });
    router.post('/openUrl', (ctx) => {
      const { body } = ctx.request;
      const { url } = body as any;
      handleOpenAppUrl(`${PROTOCOL_CODE}://${url}`);
      ctx.response.body = { success: true };
    });
    router.get('/getCurrentTeamId', (ctx) => {
      const { query } = ctx.request;
      const mainWin = getMainWindow();
      let teamId: number | undefined = undefined;
      if (mainWin) {
        const url = mainWin.webContents.getURL();
        const regRes = url.match(/\/team\/(\d+)/);
        if (regRes) {
          teamId = Number(regRes[1]);
        }
      }
      ctx.response.body = {
        teamId,
        success: true,
      };
    });
    router.get('/debugCrash', (ctx) => {
      process.crash();
    });
    router.get('/debug/browser/launch', async (ctx) => {
      const { query } = ctx.request;
      let success = false;
      let is_path_exist = false;
      let message = '';
      try {
        const installedKernel = KernelManage.getInstance().getInstalledKernelInfo();
        let targetKernel = installedKernel[0];
        if (query.ua) {
          const _targetKernel = installedKernel.find((k) => k.version === Number(query.ua));
          if (_targetKernel) {
            targetKernel = _targetKernel;
          }
        }
        const executablePath = path.join(targetKernel.path, KernelManage.getPlatformPath());
        is_path_exist = fs.existsSync(executablePath);
        if (query.spawn) {
          spawn(
            path.join(targetKernel.path, KernelManage.getPlatformPath()),
            [
              '--no-default-browser-check',
              '--disable-features=TrackingProtection3pcd,PdfOopif',
              `--remote-debugging-port=${query.port || '0'}`,
            ],
            {
              env: {
                ...process.env,
                'donkey-shop-title': 'debug',
              },
              shell: query.shell ? true : undefined,
              detached: query.detached ? true : undefined,
            },
          );
        } else {
          await puppeteer.launch({
            executablePath: path.join(targetKernel.path, KernelManage.getPlatformPath()),
            headless: query.headless ? 'new' : false,
            defaultViewport: null,
            args: [
              '--no-default-browser-check',
              '--disable-features=TrackingProtection3pcd,PdfOopif',
              `--remote-debugging-port=${query.port || '0'}`,
            ],
            ignoreDefaultArgs: [
              '--disable-features=Translate,BackForwardCache,AcceptCHFrame,MediaRouter,OptimizationHints,DialMediaRouteProvider,ProcessPerSiteUpToMainFrameThreshold',
              '--password-store=basic',
              '--enable-automation',
              '--disable-component-extensions-with-background-pages',
            ],
            env: {
              ...process.env,
              'donkey-shop-title': 'debug',
            },
            userDataDir: path.join(app.getPath('temp'), (query.dir as string) || 'hy_debug'),
            pipe: !query.port,
            debuggingPort: query.port ? Number(query.port) : undefined,
            waitForInitialPage: false,
            timeout: 60 * 1000,
            protocolTimeout: 10 * 60 * 1000,
          });
        }
        success = true;
      } catch (e: any) {
        message = e.stack;
      }
      ctx.response.body = {
        success,
        is_path_exist,
        message,
      };
    });
    router.get('/debug/browser/kill', (ctx) => {
      killBrowserProcess();
      ctx.response.body = {
        success: true,
      };
    });
    router.get('/heapDump', (ctx) => {
      const { query } = ctx.request;
      const { dataDir } = db.getDb().get('sysPres').value();
      let filename;
      if (!query.light) {
        filename = writeHeapSnapshot(
          path.join(dataDir, `heapsnapshot_${moment().format('YYYYMMDD_HHmmss')}.heapsnapshot`),
        );
      }
      ctx.response.body = {
        success: true,
        heapStatistic: getHeapStatistics(),
        appMetrics: app.getAppMetrics(),
        heapSpaceStatistics: getHeapSpaceStatistics(),
        filename,
      };
    });
    router.get('/openDevtools', (ctx) => {
      const mainWin = getMainWindow();
      if (mainWin) {
        mainWin.webContents.openDevTools();
      }
      ctx.response.body = {
        success: true,
      };
    });
    // 刷新当前窗口
    router.get('/reload/current', (ctx) => {
      const win = getCurrentWindow();
      if (win) {
        win.reload();
      }
      ctx.response.body = {
        success: true,
      };
    });
    router.get('/sysPref/setDevtoolsConsole', (ctx) => {
      const { query } = ctx.request;
      const sysPres = db.getSysPres();
      db.getDb()
        .set('sysPres', { ...sysPres, devtoolsConsole: query.enable === 'true' })
        .write();
      ctx.response.body = {
        success: true,
        devtoolsConsole: db.getSysPres().devtoolsConsole,
      };
    });
    router.get('/openDevtools/mobileHelperWindow', (ctx) => {
      const helperWindow = baseBackendTaskIns.getMobileHelper()?.helperWindow;
      helperWindow?.show();
      helperWindow?.webContents.openDevTools();
      ctx.response.body = {
        helperWindow: !!helperWindow,
        success: true,
      };
    });
    router.get('/getReport', (ctx) => {
      ctx.response.body = {
        success: true,
        data: process.report?.getReport(),
      };
    });
    router.get('/couldCreateShopShortcut', (ctx) => {
      const { query } = ctx.request;
      if (!isWinPlatform()) {
        ctx.response.body = {
          success: false,
          message: '您的操作系统暂不支持创建快捷方式',
        };
      } else {
        ctx.response.body = {
          success: true,
        };
      }
    });
    router.get('/createShopShortcut', (ctx) => {
      const { query } = ctx.request;
      if (!isWinPlatform()) {
        ctx.response.body = {
          success: false,
          message: '您的操作系统暂不支持创建快捷方式',
        };
        return;
      }
      const created = createShopShortcut({
        name: query.name as string,
        args: query.args as string,
      });
      ctx.response.body = {
        success: created,
      };
    });
    router.get('/openShopShortcut', async (ctx) => {
      const { query } = ctx.request;
      try {
        await openShopByToken(query.token as string, true);
        ctx.response.body = {
          success: true,
          code: 0,
        };
      } catch (e: any) {
        ctx.response.body = {
          success: false,
          code: e.code,
          message: e.message,
        };
      }
    });
    router.get('/popupMobile', async (ctx) => {
      const { query } = ctx.request;
      const { teamId, mobileId, width, height, ...rest } = query;
      try {
        let url = resolveUrl(db.getPortalUrl(), `/team/${teamId}/phonePopup/${mobileId}`);
        if (Object.keys(rest).length) {
          url += `?${new URLSearchParams(rest as Record<any, any>).toString()}`;
        }
        await popupMobile(Number(mobileId), url, ~~(width ?? '0'), ~~(height ?? '0'));
        ctx.response.body = {
          success: true,
        };
      } catch (e: any) {
        ctx.response.body = {
          success: false,
          message: e.message,
        };
      }
    });
    router.post('/openUrlByShopBrowser', async (ctx) => {
      const { body } = ctx.request;
      const { shopId, url } = body as any;
      const chromium = getChromium({ shopId });
      if (chromium) {
        chromium.handleCmd('open-url', { url });
        ctx.response.body = {
          success: true,
        };
      } else {
        ctx.throw(404, 'shop browser not found');
      }
    });
    router.get('/api/browser/addPptrIgnoreArg', async (ctx) => {
      const { query } = ctx.request;
      const pptrIgnoreArgs = db.getDb().get('pptrIgnoreArgs').value();
      if (query.arg && !pptrIgnoreArgs.includes(query.arg)) {
        pptrIgnoreArgs.push(query.arg);
        db.getDb().set('pptrIgnoreArgs', pptrIgnoreArgs).write();
      }
      ctx.response.body = {
        success: true,
        data: pptrIgnoreArgs,
      };
    });
    router.get('/api/browser/removePptrIgnoreArg', async (ctx) => {
      const { query } = ctx.request;
      const pptrIgnoreArgs = db.getDb().get('pptrIgnoreArgs').value();
      if (query.arg) {
        const index = pptrIgnoreArgs.indexOf(query.arg);
        if (index !== -1) {
          pptrIgnoreArgs.splice(index, 1);
          db.getDb().set('pptrIgnoreArgs', pptrIgnoreArgs).write();
        }
      }
      ctx.response.body = {
        success: true,
        data: pptrIgnoreArgs,
      };
    });
    this.app.use(router.routes());
    for (const port of portQueue) {
      try {
        await this.listenPort(port);
        this.port = port;
        break;
      } catch (e: any) {
        //
      }
    }
  }

  async listenPort(port: number) {
    return new Promise((resolve, reject) => {
      const server = this.app?.listen(
        {
          port,
          host: '127.0.0.1',
        },
        () => {
          const address = server?.address() as AddressInfo;
          if (typeof address === 'object') {
            resolve(address.port);
          } else {
            resolve(address);
          }
        },
      );
      server?.on('error', (err) => {
        logger.error(`[APP] LocalWebServer listen port(${port}) failed: ${err.message}`);
        reject(err);
      });
    });
  }

  getPort() {
    return this.port;
  }
  getRpcEvent(eventName: string) {
    const _list: any[] = [];
    const list: any[] = [];
    this.rpc_events.forEach((item) => {
      if (item.eventName === eventName) {
        _list.push(item);
      } else {
        list.push(item);
      }
    });
    this.rpc_events = list;
    return _list;
  }
}

export const localWebServerIns = new LocalWebServer();
