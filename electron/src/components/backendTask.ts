import path from 'path';
import { app, powerMonitor } from 'electron';
import { AjaxEventClient } from '@e/utils/AjaxEventClient';
import { ScheduleJob } from '@e/rpa/task_schedule';
import { FileWatcher } from '@e/rpa/file_watcher';
import { OpenApiListener } from '@e/rpa/open_api_listener';
import request, { resolveApiUrl } from '@e/services/request';
import { appPowerSaveBlocker } from '@e/components/powerSaveBlocker';
import logger from '@e/services/logger';
import db from '@e/components/db';
import { getHostName, isWinPlatform } from '@e/utils/utils';
import os from 'os';
import fs from 'fs';
import { createGCHelper, GCHelper } from '@e/helper/index';
import { getDynamicPath } from '@e/configs/app';
import appConfig from '../configs/app';
import { RealtimePortalRpcClient } from '@e/ws';
import { NodeWsTransport } from '@e/ws/ws-transport';
import { AndroidDeviceManager } from '@e/mobile/AndroidDeviceManager';
import { MobileHelperWindow } from '@e/mobile/MobileHelperWindow';
import { EmailListener } from '@e/utils/ipc/email';
import { RecordControllerOwner } from '@e/recorder/controller';
import axios, { AxiosResponse } from 'axios';
import { IOSDeviceManager } from '@e/mobile/IOSDeviceManager';
import { listenerProbeTask } from '@e/utils/ipc/ping';

let ajaxEventClient: AjaxEventClient | undefined;
let recorderController: RecordControllerOwner | undefined; //负责录像的electron窗口
let mobileHelperWindow: MobileHelperWindow | undefined; //负责处理手机串流的electron窗口
export const nodeWsTransport = new NodeWsTransport();
export const portalRpcClient: RealtimePortalRpcClient = new RealtimePortalRpcClient(
  nodeWsTransport,
); //与门户通信的websocket通道

export const androidDeviceManager: AndroidDeviceManager = AndroidDeviceManager.getInstance();
export const iosDeviceManager: IOSDeviceManager = IOSDeviceManager.getInstance();

export function getAjaxEventClientIns() {
  return ajaxEventClient;
}

export function getRecorderController() {
  if (recorderController) {
    return recorderController;
  }
  return undefined;
}

class BaseBackendTask {
  state: 'started' | 'stopped';
  private monitorTimer: any = 0;
  private lastReportTime: number = 0;
  constructor() {
    this.state = 'stopped';
    this.reportClientIp = this.reportClientIp.bind(this);
  }

  startRecorderController() {
    if (!recorderController) {
      recorderController = new RecordControllerOwner();
    }
  }

  stopRecorderController() {
    recorderController?.close();
    recorderController = undefined;
  }

  getMobileHelper() {
    return mobileHelperWindow;
  }

  startMobileHelper() {
    if (!mobileHelperWindow) {
      mobileHelperWindow = new MobileHelperWindow();
    }
  }

  stopMobileHelper() {
    mobileHelperWindow?.close();
    mobileHelperWindow = undefined;
  }

  async start() {
    if (this.state === 'started') return;
    this.state = 'started';
    powerMonitor.on('suspend', () => {
      logger.info('[APP] suspend');
    });
    powerMonitor.on('resume', () => {
      logger.info('[APP] resume');
      setTimeout(() => {
        if (ajaxEventClient) {
          ajaxEventClient.reset();
        }
      }, 1000);
    });
    appPowerSaveBlocker.init();
    if (db.isRpaExecutor()) {
      await new Promise((resolve) => {
        setTimeout(resolve, 3000);
      });
      try {
        const hostName = await getHostName();
        await request('/api/rpa/cloud/executor/contact', {
          method: 'POST',
          forceHttp: true,
          params: {
            appId: db.getDb().get('uuid').value(),
            hostName,
            osName: os.type(),
            cpus: os.cpus().length,
            appVersion: app.getVersion(),
            mem: os.totalmem(),
          },
        });
      } catch (e) {
        logger.error('[APP] contact executor failed', e);
      }
    }
    this.monitorTimer = setInterval(() => {
      // 获取客户端内存使用情况，以MB单位输出
      const mem = process.memoryUsage();
      logger.info(`[APP] process memory usage: ${(mem.rss / 1024 / 1024).toFixed(0)}MB`);
      // app.getAppMetrics().forEach((metric) => {
      //   // metric 内存单位以MB输出
      //   logger.info(
      //     `[APP] ${metric.type} - pid: ${metric.pid} ${metric.name ? 'name:' + metric.name : ''} ${
      //       metric.serviceName ? 'serviceName:' + metric.serviceName : ''
      //     } memory: ${(metric.memory.workingSetSize / 1024).toFixed(0)}MB/${(
      //       metric.memory.peakWorkingSetSize / 1024
      //     ).toFixed(0)}MB`,
      //   );
      // });
    }, 10 * 60 * 1000);
    const { dataDir } = db.getDb().get('sysPres').value();
    if (process.report) {
      process.report.reportOnFatalError = true;
      process.report.directory = dataDir;
    }
    this.reportClientIp();
  }

  async doReportClientIp() {
    try {
      if (Date.now() - this.lastReportTime < 1000) {
        return;
      }
      this.lastReportTime = Date.now();
      await axios.get(resolveApiUrl('/api/device/reportClientIp'), {
        headers: {
          Cookie: db.getCookies().join(';') ?? '',
        },
      });
    } catch (e: any) {
      logger.error('[APP] report client ip failed', e.message);
    }
  }

  async reportClientIp() {
    await this.doReportClientIp();
    setTimeout(this.reportClientIp, 5 * 60 * 1000);
  }

  stop() {
    if (this.state === 'stopped') return;
    this.state = 'stopped';
    recorderController?.close();
    clearInterval(this.monitorTimer);
  }
}

export const baseBackendTaskIns = new BaseBackendTask();

export default class BackendTask {
  state: 'started' | 'stopped';
  private rpaScheduleJob: ScheduleJob | null = null;
  private fileWatcher: FileWatcher | null = null;
  private emailListener: EmailListener | null = null;
  private openApiListener: OpenApiListener | null = null;
  private _gcHelper?: GCHelper;
  constructor() {
    this.state = 'stopped';
  }

  async start() {
    await baseBackendTaskIns.start();
    //初始化websocket连接
    portalRpcClient.stop();
    portalRpcClient.start();
    if (this.state === 'started') return;
    this.state = 'started';
    ajaxEventClient = new AjaxEventClient();
    ajaxEventClient.listenUploadLogs();
    portalRpcClient.ajaxPushHandle = (events: any[]) => {
      ajaxEventClient?.handleAjaxPush(events);
    };
    portalRpcClient.onAppEmit('client-send-http', async (option: any) => {
      let { url, method, headers, body, jobId } = option;
      let response = await new Promise<AxiosResponse>((resolve, reject) => {
        axios
          .request({
            url: url,
            method: method || 'GET',
            headers: headers,
            data: body,
            responseType: 'text',
          })
          .then((res) => {
            resolve(res);
          })
          .catch((e) => {
            if (e.response) {
              reject(
                JSON.stringify({
                  status: resp.status,
                  statusText: resp.statusText,
                  message: resp.data?.message,
                }),
              );
            } else {
              reject('Error');
            }
          });
      });
      let resp = response.data;
      if(jobId) {
        await request('/api/clientHttpResult', {
          method: 'POST',
          data: {
            jobId,
            response: resp,
          },
        });
      }
    });
    this.rpaScheduleJob = new ScheduleJob({ ajaxEventClient }).start();
    if (!db.isRpaExecutor() && !db.isRuntimeMode()) {
      listenerProbeTask();
      this.fileWatcher = new FileWatcher({}).start();
      // 邮件监听服务.调用客户端发送TK邮件
      this.emailListener = new EmailListener({}).start();
      this.openApiListener = new OpenApiListener({}).start();
      if (!baseBackendTaskIns.getMobileHelper()) {
        baseBackendTaskIns.startMobileHelper();
      }
      await androidDeviceManager.start();
      await iosDeviceManager.start();
    }
  }

  stop() {
    if (this.state === 'stopped') return;
    this.state = 'stopped';
    baseBackendTaskIns.stop();
    baseBackendTaskIns.stopRecorderController();
    baseBackendTaskIns.stopMobileHelper();
    this.rpaScheduleJob?.stop();
    this.rpaScheduleJob = null;
    this.fileWatcher?.stop();
    this.fileWatcher = null;
    this.openApiListener?.stop();
    this.openApiListener = null;
    this.emailListener?.stop();
    this.emailListener = null;
    ajaxEventClient?.close();
    ajaxEventClient = undefined;
    androidDeviceManager.stop();
    iosDeviceManager.stop();
    //关闭websocket连接
    portalRpcClient.stop();
  }

  async getGCHelper(): Promise<GCHelper> {
    if (!isWinPlatform()) {
      throw '不支持当前操作系统';
    }
    if (!this._gcHelper) {
      const { DATA_DIR } = getDynamicPath();
      this._gcHelper = createGCHelper(path.join(DATA_DIR, 'GCHelper.exe'));
      await this._gcHelper.start();
      if (appConfig.DEBUG || process.env.NODE_ENV === 'development') {
        this._gcHelper.enableDebug();
      }
    }
    return this._gcHelper;
  }

  async stopGcHelper() {
    if (this._gcHelper) {
      await this._gcHelper.destroy();
      this._gcHelper = undefined;
    }
  }

  checkGCFile(): boolean {
    const { DATA_DIR } = getDynamicPath();
    return fs.existsSync(path.join(DATA_DIR, 'GCHelper.exe'));
  }
}

export const backendTaskIns = new BackendTask();
portalRpcClient.onAppEmit('debug-restart-client', () => {
  logger.info('[APP] 收到重启客户端的请求...');
  // 重启客户端
  app.relaunch();
  app.exit(0);
});
