//鼠标点击
import { ElementHandle, KeyInput } from 'donkey-puppeteer-core';
import { TaskContext } from '@e/rpa/context';
import { ElementParam } from '@e/rpa/params/element_param';
import os from 'os';
import {
  delayExec,
  getSprintTab,
  saveToParam,
  tryUseIframe,
  waitMilliseconds,
  waitSeconds,
} from '@e/rpa/utils';
import random from '@e/utils/random';
import fs, { FSWatcher } from 'fs-extra';
import path from 'path';
import { Node, PageNode } from '@e/rpa/nodes/base';

//点击元素
export class Click extends PageNode {
  type = 'rpa.event.Click';
  mkNode = true;
  props: {
    selector: string;
    button?: 'left' | 'right' | 'middle';
    modifiers?: number; // 1<<0 ctrl, 1<<1 shift, 1<<2 alt
    clickCount?: number; //1 表示单击
    center?: boolean; //是否点击正中间，如果发现有点击不生效的情况时勾选
    clickX?: number; //点击的x坐标，相对于元素左上角，clickX 和 clickY 同时指定才有意义
    clickY?: number; //点击的y坐标，相对于元素左上角 clickX 和 clickY 同时指定后，center 会被忽略
  } = { selector: '', button: 'left', modifiers: 0, clickCount: 1, center: false };

  async exec(ctx: TaskContext): Promise<void> {
    if (this.props.selector) {
      let selector = await ctx.evalParams(this.props.selector);
      if (!selector) {
        throw '点击的页面节点不可为空!';
      }
      let center = !!this.props.center;
      let pos = undefined;
      let clickX = await ctx.evalParams(this.props.clickX);
      let clickY = await ctx.evalParams(this.props.clickY);
      if (typeof clickX != 'undefined' && typeof clickY != 'undefined') {
        center = false;
        pos = {
          x: ~~clickX,
          y: ~~clickY,
        };
      }
      let el = await this.$(ctx, this.props.selector);
      await ctx.fakeAction.simClickElement(ctx.page, el!, {
        button: this.props.button || 'left',
        modifiers: this.props.modifiers || 0,
        clickCount: this.props.clickCount || 1,
        center: center,
        pos: pos,
        sim: this.isSim(ctx),
      });
    }
  }
}

//输入内容
export class Type extends PageNode {
  type = 'rpa.event.Type';
  mkNode = true;
  declare props: {
    selector: string;
    text: string;
    clear: boolean; //输入前是否清空内容
    usePaste: boolean; //是否用
    enterModifier?: 'Shift' | 'Control' | 'Alt' | 'Meta'; //遇到换行字符，使用 modifier + 换行，默认不按任何modifier键
    clickFocus?: boolean; //输入之前是否先点击一下元素，默认值true，为空表示true
  };
  released = false;

  async exec(ctx: TaskContext): Promise<void> {
    this.released = false;
    if (this.props.selector) {
      //先让其获取焦点
      let el = await this.$(ctx, this.props.selector);
      if (!el) {
        //未找到输入变量
        return;
      }
      let clickFocus = this.props.clickFocus;
      if (typeof clickFocus == 'undefined') {
        clickFocus = true;
      }
      if (clickFocus) {
        let click = new Click()._useProps({
          selector: this.props.selector, center: true
        }).setOwner(this);
        click.sim = this.isSim(ctx);
        await click.exec(ctx);
        // if (this.isSim(ctx)) {
        //   await ctx.fakeAction.simMouseMoveToElement(ctx.page, el!);
        // } else {
        //   await ctx.fakeAction.mouseMoveToElement(ctx.page, el!);
        // }
        // await ctx.fakeAction.fitMouseElClick(el!, 'left');
      }
      await el!.focus();
      let frame = await tryUseIframe(ctx, this.iframe);
      let control: 'Meta' | 'Control' = 'darwin' === os.platform() ? 'Meta' : 'Control';
      if (!!this.props.clear) {
        //清空现有内容
        await delayExec(() => ctx.page.keyboard.down(control), random.nextInt(20, 50));
        await delayExec(() => ctx.page.keyboard.press('a'), random.nextInt(20, 50));
        await delayExec(() => ctx.page.keyboard.up(control), random.nextInt(20, 50));

        // await frame.evaluate(() => document.execCommand('selectall', false));
        // await delayExec(() => ctx.page.keyboard.press('Delete'), random.nextInt(20, 50));
        //@ts-ignore
        await el?.evaluate((el) => (el.value = ''));
      }
      let text = (await ctx.evalParams(this.props.text)) || '';
      let needType = true;
      if (!!this.props.usePaste) {
        await el?.evaluate((el, value) => {
          document.execCommand('donkeyPaste', false, value);
        }, text);
        let val = await el!.evaluate((el) => {
          switch (el.tagName) {
            case 'INPUT':
            case 'TEXTAREA':
              //@ts-ignore
              return el.value;
            default:
              //@ts-ignore
              return el.innerText;
          }
        });
        if (val.length == 0) {
          needType = true;
        } else {
          needType = false;
        }
      }
      if (needType) {
        if (this.isSim(ctx)) {
          //使用模拟输入的方式
          await ctx.fakeAction.simKeyboardType(ctx.page, el!, String(text), () => !this.released, {
            pauseAfterLastKeyUp: true,
            enterModifier: this.props.enterModifier,
          });
        } else {
          await el?.type(String(text));
        }
      }
    }
  }

  release(ctx: TaskContext) {
    super.release(ctx);
    this.released = true;
  }
}

//自动填入账号保存的用户名密码
export class FillPassword extends PageNode {
  type = 'rpa.event.FillPassword';
  mkNode = true;
  declare props: {
    username?: string; //如果同一网站保存了多个密码，用户可以决定使用啊一个
    usernameSelector?: string; //如果用户需要自动填入用户名
    passwordSelector?: string; //密码输入框的selector
  };

  get premiseSelector(): any[] {
    const selectors = [];
    if (this.props.usernameSelector) {
      selectors.push(this.props.usernameSelector);
    }
    if (this.props.passwordSelector) {
      selectors.push(this.props.passwordSelector);
    }
    return selectors;
  }

  async exec(ctx: TaskContext): Promise<void> {
    if (!this.props.passwordSelector && !this.props.usernameSelector) {
      return;
    }
    const domain = await ctx.page.evaluate(() => document.location.host);
    let passwords = [];
    if (domain) {
      //@ts-ignore
      passwords = await ctx.requestAgent.request(
        `/api/rpa/task/item/${ctx.params.account.id}/passwords`,
        {
          teamId: ctx.item.teamId,
          method: 'get',
          params: {
            domain: domain,
          },
        },
      );
    }
    if (passwords.length > 0) {
      let username = await ctx.evalParams(this.props.username);
      if (username) {
        // @ts-ignore
        passwords = passwords.filter((p) => username == p.usernameValue);
      }
    }
    if (passwords.length == 0) {
      throw '找不到对应的用户名密码';
    }
    let password = passwords[0];
    if (this.props.usernameSelector && password.usernameValue) {
      await new Type()
        ._useProps({
          selector: this.props.usernameSelector,
          text: password.usernameValue,
          clear: true,
        })
        .setOwner(this)
        .exec(ctx);
    }
    if (this.props.passwordSelector && password.passwordValue) {
      await new Type()
        ._useProps({
          selector: this.props.passwordSelector,
          text: password.passwordValue,
          clear: true,
        })
        .setOwner(this)
        .exec(ctx);
    }
  }
}

//网页标准combobox, select标签，用来给其设置值
class Select extends PageNode {
  type = 'rpa.event.Select';
  mkNode = true;
  declare props: {
    selector: string;
    /*
     * text: 根据选项的文本或value来选择
     * index: 根据选项的位置来选择 (1-based)
     * 为向前兼容，如果不设置，默认为text
     */
    selectType: 'text' | 'index';
    value: string;
  };

  async exec(ctx: TaskContext): Promise<void> {
    if (this.props.selector) {
      let el = await this.$(ctx, this.props.selector);
      let value = await ctx.evalParams(this.props.value);
      if (this.isSim(ctx)) {
        await ctx.fakeAction.simMouseMoveToElement(ctx.page, el!);
      } else {
        await ctx.fakeAction.mouseMoveToElement(ctx.page, el!);
      }
      await el!.focus();
      // @ts-ignore
      let options = await el!.$$('option');
      for (let i = 0; i < options.length; i++) {
        let option = options[i];
        // @ts-ignore
        let [optionValue, optionText]: [string, string] = await option.evaluate((el: any) => [
          el.value,
          el.textContent,
        ]);
        if (this.props.selectType === 'index') {
          if (value == i + 1) {
            await el!.select(optionValue);
            break;
          }
        } else if (value == optionValue || value == optionText) {
          await el!.select(optionValue);
          break;
        }
      }
    }
  }
}

//复选框
class CheckBox extends Node {
  type = 'rpa.event.CheckBox';
  mkNode = true;
  declare props: {
    selector: string;
    action: string; //check, uncheck, toggle
  };

  async exec(ctx: TaskContext): Promise<void> {
    let el = await this.$(ctx, this.props.selector);
    //@ts-ignore
    let checked = await el.evaluate((e) => e.checked);
    let target = checked;
    if (!['check', 'uncheck', 'toggle'].includes(this.props.action)) {
      target = await ctx.evalParams(this.props.action);
      if (!(typeof target === 'boolean')) {
        target = 'true' == target;
      }
    } else {
      switch (this.props.action) {
        case 'check':
          target = true;
          break;
        case 'uncheck':
          target = false;
          break;
        case 'toggle':
          target = !checked;
          break;
      }
    }
    if (checked !== target) {
      await ctx.fakeAction.simClickElement(ctx.page, el!, {
        button: 'left',
        modifiers: 0,
        clickCount: 1,
        center: true,
        sim: this.isSim(ctx),
      });
    }
  }
}

//移动鼠标
class MoveMouse extends PageNode {
  type = 'rpa.event.MoveMouse';
  mkNode = true;
  declare props: {
    x: number;
    y: number;
  };

  async exec(ctx: TaskContext): Promise<void> {
    if (ctx.mobile) {
      throw '手机端不支持移动鼠标';
    }
    let x = (await ctx.evalParams(this.props.x)) || 0;
    let y = (await ctx.evalParams(this.props.y)) || 0;
    if (this.isSim(ctx)) {
      await ctx.fakeAction.simMouseMoveTo(ctx.page, { x, y });
    } else {
      await ctx.page.mouse.move(x, y);
    }
  }
}

//拖放
class DragDrop extends PageNode {
  type = 'rpa.event.DragDrop';
  mkNode = true;
  declare props: {
    selector: string; //要拖放的节点的选择器
    vector: number[]; //一个二维向量，分别代表x,y轴的移动多少像素
    endSelector: string; //目标位置的选择器  vector和endSelector同时存在的话，vector具有更高优先级
  };

  async exec(ctx: TaskContext): Promise<any> {
    let el = await this.$(ctx, this.props.selector);
    let vector = this.props.vector;
    let endSelector = this.props.endSelector;
    if (vector && vector.length == 2) {
      await ctx.fakeAction.simMouseMoveToElement(ctx.page, el!, false);
      await ctx.fakeAction.fitMouseDown(ctx.page, 'left');
      let currPos = ctx.fakeAction.currPos;
      let vectorX = ~~(await ctx.evalParams(vector[0])) || 0;
      let vectorY = ~~(await ctx.evalParams(vector[1])) || 0;
      await ctx.fakeAction.simMouseMoveTo(ctx.page, {
        x: currPos.x + vectorX,
        y: currPos.y + vectorY,
      });
      await ctx.fakeAction.fitMouseUp(ctx.page, 'left');
    } else if (endSelector) {
      let endEl = await this.$(ctx, endSelector);
      await ctx.fakeAction.simMouseMoveToElement(ctx.page, el!, false);
      await ctx.fakeAction.fitMouseDown(ctx.page, 'left');
      if (!(await ctx.fakeAction.simMouseMoveToElement(ctx.page, endEl!, true))) {
        await waitMilliseconds(400);
      }
      await ctx.fakeAction.fitMouseUp(ctx.page, 'left');
    }
  }
}

//发送鼠标滚轮事件
class Wheel extends PageNode {
  type = 'rpa.event.Wheel';
  mkNode = true;
  declare props: {
    selector?: string; //如果为空则使用document.body
    up: boolean; //是不是向上滚动
    count: number; //滚动事件发几次
  };

  async exec(ctx: TaskContext): Promise<any> {
    let el: ElementHandle | undefined;
    if (this.props.selector) {
      el = await this.$(ctx, this.props.selector);
    }
    let count = ~~(await ctx.evalParams(this.props.count));
    if (this.isSim(ctx)) {
      el && (await ctx.fakeAction.simMouseMoveToElement(ctx.page, el!));
      for (let i = 0; i < count; i++) {
        await ctx.page.mouse.wheel({ deltaY: (this.props.up ? -1 : 1) * random.nextInt(60, 90) });
        await waitMilliseconds(random.nextInt(10, 30));
        if (i != 0 && i % 4 == 0) {
          await waitMilliseconds(random.nextInt(200, 400));
        }
      }
    } else {
      el && (await ctx.fakeAction.mouseMoveToElement(ctx.page, el!));
      for (let i = 0; i < count; i++) {
        await ctx.page.mouse.wheel({ deltaY: (this.props.up ? -1 : 1) * random.nextInt(60, 90) });
      }
    }
  }
}

//键盘按键
class Keyboard extends PageNode {
  type = 'rpa.event.Keyboard';
  mkNode = true;
  declare props: {
    /*
     * 只允许单个键，允许值见 https://github.com/wix-incubator/unidriver/blob/master/core/src/puppeteer-us-keyboard-layout.ts
     */
    keys: KeyInput;
    /*
     * 辅助键，只允许单个值
     * 当keys == mask 时，mask会被忽略。
     */
    mask: 'Control' | 'Shift' | 'Alt' | 'Meta';
  };

  async exec(ctx: TaskContext): Promise<any> {
    if (this.props.keys) {
      let keyboard = ctx.page.keyboard;
      let arr = this.props.keys.split(',');
      if (arr.length > 0) {
        let key = arr[0];
        if (this.props.mask && this.props.mask != key) {
          await keyboard.down(this.props.mask);
        }
        // @ts-ignore
        await keyboard.press(key, { delay: random.nextInt(100, 300) });
        await waitMilliseconds(random.nextInt(100, 300));
        if (this.props.mask && this.props.mask != key) {
          await waitMilliseconds(random.nextInt(100, 300));
          await keyboard.up(this.props.mask);
        }
      }
    }
  }
}

//经过元素
class Hover extends PageNode {
  type = 'rpa.event.Hover';
  mkNode = true;
  declare props: {
    selector: string;
    center?: boolean; //是否移动到元素正中心位置

    hoverX?: number; //悬停位置的x坐标，相对于元素左上角，hoverX和hoverY同时指定才有意义
    hoverY?: number; //悬停位置的y坐标，相对于元素左上角，hoverX和hoverY同时指定后，center会被忽略
  };

  async exec(ctx: TaskContext): Promise<any> {
    if (ctx.mobile) {
      throw '手机端不支持经过元素';
    }
    let el = await this.$(ctx, this.props.selector);
    let center = !!this.props.center;
    let pos = undefined;
    let hoverX = await ctx.evalParams(this.props.hoverX);
    let hoverY = await ctx.evalParams(this.props.hoverY);
    if (typeof hoverX != 'undefined' && typeof hoverY != 'undefined') {
      center = false;
      pos = {
        x: ~~hoverX,
        y: ~~hoverY,
      };
    }
    if (this.isSim(ctx)) {
      await ctx.fakeAction.simMouseMoveToElement(ctx.page, el!, pos || center);
    } else {
      await ctx.fakeAction.mouseMoveToElement(ctx.page, el!, pos || center);
    }
    // await el.hover();
  }
}

//聚集元素
class Focus extends PageNode {
  type = 'rpa.event.Focus';
  mkNode = true;
  declare props: {
    selector: string;
  };

  async exec(ctx: TaskContext): Promise<any> {
    let el = await this.$(ctx, this.props.selector);
    if (this.isSim(ctx)) {
      await ctx.fakeAction.simMouseMoveToElement(ctx.page, el!);
    } else {
      await ctx.fakeAction.mouseMoveToElement(ctx.page, el!);
    }
    await el!.focus();
  }
}

/**
 * 下载文件
 */
class Download extends PageNode {
  type = 'rpa.event.Download';
  mkNode = true;
  declare props: {
    type: 'url' | 'selector';
    url?: string; //下载地址，必须是 https?:// 开头的地址
    selector?: string; //点击地址
    filename: string; //保存的文件名
    saveTo?: string; //下载成功后将文件的路径保存到一个变量
  };
  watcher?: FSWatcher;
  private releaseDownloadFun?: () => any;

  get premiseSelector(): any[] | undefined {
    return this.props.type === 'selector' ? [this.props.selector] : undefined;
  }

  async exec(ctx: TaskContext): Promise<any> {
    let tmpFile =
      this.props.type === 'selector' ? await this.clickDownload(ctx) : await this.downloadUrl(ctx);
    let targetFile: string = await ctx.evalParams(this.props.filename || 'download.html');
    await ctx.rpaFiles.upload(targetFile, tmpFile);
    targetFile = await ctx.rpaFiles.absolutePath(targetFile);
    if (this.props.saveTo) {
      saveToParam(ctx.params, this.props.saveTo, targetFile);
    }
    this.execRet = targetFile;
  }

  async downloadUrl(ctx: TaskContext): Promise<string> {
    let url = (await ctx.evalParams(this.props.url)).trim();
    let userAgent = await ctx.page.evaluate(() => window.navigator.userAgent);
    let current = getSprintTab(ctx.page);
    let referer = current.url;
    ctx.rpaFiles.httpFetcher.headers = { 'User-Agent': userAgent, Referer: referer };
    return await ctx.rpaFiles.fetch(url);
  }

  async clickDownload(ctx: TaskContext): Promise<string> {
    let tempDir = await ctx.rpaFiles.newTempDir();
    const client = await ctx.page.target().createCDPSession();
    await client.send('Page.setDownloadBehavior', {
      behavior: 'allow',
      downloadPath: tempDir,
    });
    this.releaseDownloadFun = () => {
      try {
        client.send('Page.setDownloadBehavior', {
          behavior: 'default',
        });
        client.detach();
      } catch (_) {}
    };
    let click = new Click().setOwner(this);
    click.props = { selector: this.props.selector! };
    let fileWatcher = new Promise((resolve) => {
      this.watcher = fs.watch(tempDir, function (eventType, filename) {
        // console.log(eventType, filename);
        if (!filename?.endsWith('crdownload')) {
          resolve(filename);
        }
      });
    });
    await click.exec(ctx);
    //@ts-ignore
    let filename: string = await fileWatcher;
    this.watcher?.close();
    this.watcher = undefined;
    await waitSeconds(1);
    return path.resolve(tempDir, filename);
  }

  release(ctx: TaskContext) {
    super.release(ctx);
    this.watcher?.close();
    this.watcher = undefined;
    if (this.releaseDownloadFun) {
      this.releaseDownloadFun();
    }
  }
}

//上传
class Upload extends PageNode {
  type = 'rpa.event.Upload';
  mkNode = true;
  declare props: {
    fileSelector: string; //文件输入框的selector
    filename: string; //要上传文件的文件名，如果使用该属性，对应文件必须事先放置到task的文件上下文下
    //后期应该可以指定其它位置的文件
  };

  get premiseSelector(): any[] {
    return [this.props.fileSelector];
  }

  async exec(ctx: TaskContext): Promise<any> {
    let originFiles = await ctx.evalParams(this.props.filename);
    let files = [];
    if (Array.isArray(originFiles)) {
      files = originFiles as [];
    } else {
      files = ('' + originFiles).split('\n');
    }
    files = files.filter((v) => v.length > 0);
    for (let i = 0; i < files.length; i++) {
      if(await ctx.rpaFiles.isLocalFile(files[i])) {
        let tempDir = await ctx.rpaFiles.newTempDir();
        let tempFile = path.resolve(tempDir, path.basename(files[i]));
        await ctx.rpaFiles.copy(files[i], tempFile);
        files[i] = tempFile;
      } else {
        files[i] = await ctx.rpaFiles.fetch(files[i]);
      }
    }
    let fileFiled = await this.$(ctx, this.props.fileSelector);
    if (!fileFiled) {
      throw '未找到对应的文件框';
    }
    let ep = new ElementParam(this.props.fileSelector, fileFiled, this.iframe);
    let nodeName = await ep.tagName(),
      type = await ep.getAttribute('type');
    if (nodeName && nodeName.toUpperCase() == 'INPUT' && type == 'file') {
      //直接选中的就是input文件上传tag
      //@ts-ignore
      await fileFiled.uploadFile(...files);
    } else {
      const [fileChooser] = await Promise.all([
        ctx.page.waitForFileChooser(),
        await ctx.fakeAction.simClickElement(ctx.page, fileFiled, {
          button: 'left',
          modifiers: 0,
          clickCount: 1,
          center: true,
          sim: this.isSim(ctx),
        }),
      ]);
      await waitSeconds(1);
      //@ts-ignore
      await fileChooser.accept(files);
    }
  }
}

export const NodeTypes = {
  'rpa.event.Click': Click,
  'rpa.event.Type': Type,
  'rpa.event.Select': Select,
  'rpa.event.CheckBox': CheckBox,
  'rpa.event.Wheel': Wheel,
  'rpa.event.Hover': Hover,
  'rpa.event.Focus': Focus,
  'rpa.event.MoveMouse': MoveMouse,
  'rpa.event.Keyboard': Keyboard,
  'rpa.event.Download': Download,
  'rpa.event.Upload': Upload,
  'rpa.event.FillPassword': FillPassword,
  'rpa.event.DragDrop': DragDrop,
};
