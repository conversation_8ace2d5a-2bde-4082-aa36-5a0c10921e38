import { TaskContext } from '@e/rpa/context';
import { Node } from '@e/rpa/nodes/base';
import { SendHttp } from '@e/rpa/nodes/index';
import path from 'path';
import axios, { AxiosProxyConfig } from 'axios';
import fsutil from '@e/rpa/fs.utils';
import { saveToParam } from '@e/rpa/utils';
import { getAppRawProxy, parseProxyUrl, readSystemProxy } from '@e/utils/proxy_util';
import * as socks from 'socks';
import {
  createSocksTunnel,
  FrontendSocksTunnel,
  getLocalFrontendProxyUrl,
} from '@e/utils/tunnels/direct/frontend';
import logger from '@e/services/logger';

const emlformat = require('eml-format');
const EmlParser = require('eml-parser');

const nodemailer = require('nodemailer');
const { ImapFlow } = require('imapflow');
const Pop3Command = require('node-pop3');
const cheerio = require('cheerio');

const FormData = require('form-data');

declare interface NotifyProps {
  subject: string; //主题，如果是短信和微信，该字段无意义
  content: string; //内容
  attachment: string; //附件，user_disk:// team_disk:// file:// attachment:// 等

  method: 'user' | 'text' | 'taskCreator' | 'openId'; //text表示用户输入字符串；taskCreator表示流程执行人
  userIds: number[]; //接收人员id列表
  userStr: string; // 接收人员字符串（手机号或邮箱），逗号分隔
  openIdStr: string; // 接收人员字符串（openId），逗号分隔
}

abstract class SendNotify extends Node {
  declare props: NotifyProps;

  async exec(ctx: TaskContext): Promise<void> {
    let subject = await ctx.evalParams(this.props.subject);
    let content = await ctx.evalParams(this.props.content);
    if (typeof content != 'undefined' && typeof content != 'string') {
      content = JSON.stringify(content);
    }
    const userStrs = String(await ctx.evalParams(this.props.userStr));
    const openIdStr = String(await ctx.evalParams(this.props.openIdStr));
    let targetMethod = this.props.method || 'user';
    let params: any = Object.assign(
      {
        type: this.type,
        method: targetMethod,
        userIds: this.props.method === 'taskCreator' ? [] : this.props.userIds || [],
        userStrs: userStrs.split(',') || [],
        rpaFlowId: await ctx.evalParams('{flow.id}'),
        shopId: await ctx.evalParams('{account.id}'),
        subject,
        content,
      },
      await this.extParams(ctx),
    );
    if (targetMethod === 'openId') {
      let scope = 'task';
      if (ctx.preview) {
        scope = 'preview';
      } else if (ctx.params.task?.planId) {
        scope = 'plan';
      }
      params['bizId'] = `rpa_${scope}-${ctx.flowId}`;
      params['openIds'] = openIdStr.split(/[\n,]/) || [];
    }
    if (!ctx.preview) {
      params['rpaTaskId'] = ctx.params.task.id;
      params['rpaTaskItemId'] = ctx.item.id;
    }
    let ret = await ctx.requestAgent.request(`/api/rpa/task/sendNotify`, {
      teamId: ctx.item.teamId,
      method: 'post',
      data: params,
    });
    if ('rpa.notify.SendEmail' == this.type) {
      this.log(
        ctx,
        `发送完成，预计发送给${ret.expectCount}人，实际发送给${ret.successCount}人（使用花漾邮件服务，忽略附件）`,
      );
    } else {
      this.log(ctx, `发送完成，预计发送给${ret.expectCount}人，实际发送给${ret.successCount}人`);
    }
  }

  async extParams(ctx: TaskContext) {
    return {};
  }
}

//发送电子邮件
class SendEmail extends SendNotify {
  type = 'rpa.notify.SendEmail';
  declare props: NotifyProps & {
    customServer: boolean; //是否使用自定义的邮件服务器

    host: string;
    port: number;
    secure: boolean;
    user: string;
    pass: string;

    html: boolean; //是否以html发送, undefined 等价 true

    attachment2: string;
    attachment3: string;
    attachment4: string;
    attachment5: string;

    proxy_mode: 'app' | 'localFrontendProxy' | 'fixed_servers' | 'system' | 'none'; // 使用客户端的代理配置（兼容旧的流程） | 使用固定的代理配置 | 使用系统代理配置 | 不使用代理 ；默认值 app
    proxyRules?: string;
  };

  async exec(ctx: TaskContext): Promise<void> {
    if (!this.props.customServer) {
      //使用花漾的邮件服务器
      return super.exec(ctx);
    }
    //使用用户自定义的邮件服务器
    const host = await ctx.evalParams(this.props.host);
    const port = await ctx.evalParams(this.props.port);
    const secure = await ctx.evalParams(this.props.secure);
    const user = await ctx.evalParams(this.props.user);
    const pass = await ctx.evalParams(this.props.pass);
    let subject = await ctx.evalParams(this.props.subject);
    let content = await ctx.evalParams(this.props.content);
    let html = await ctx.evalParams(this.props.html);
    if (typeof html === 'undefined') {
      html = true;
    }
    let proxyUrl: string | undefined = undefined;
    let proxyMode = this.props.proxy_mode || 'app';
    let sysProxy = undefined;
    let frontendProxy: FrontendSocksTunnel | undefined = undefined;
    switch (proxyMode) {
      case 'none':
        break;
      case 'app':
        sysProxy = await getAppRawProxy();
        break;
      case 'localFrontendProxy':
        try {
          const localFrontProxyUrl = await getLocalFrontendProxyUrl();
          frontendProxy = createSocksTunnel(localFrontProxyUrl)!;
          await frontendProxy.open();
          sysProxy = {
            proxyType: 'socks5',
            host: '127.0.0.1',
            port: frontendProxy.listenPort,
          };
        } catch (e) {
          logger.error(`创建前置代理失败：${e}`);
        }
        break;
      case 'fixed_servers':
        sysProxy = parseProxyUrl(this.props.proxyRules ?? '');
        break;
      case 'system':
        let ret = await readSystemProxy();
        if (ret && ret.success) {
          sysProxy = ret.proxy;
        }
        break;
    }
    if (sysProxy) {
      proxyUrl = `${sysProxy.proxyType}://${sysProxy.host}:${sysProxy.port}`;
    }
    let transporter = nodemailer.createTransport({
      host,
      port: Number(port),
      secure,
      auth: { user, pass },
      proxy: proxyUrl,
    });
    if (sysProxy?.proxyType === 'socks5') {
      transporter.set('proxy_socks_module', socks);
    }
    let mailOptions: any = {
      from: user,
      to: '',
      subject: subject,
    };
    mailOptions[!!html ? 'html' : 'text'] = content;
    let attachments = [];
    let totalSize = 0;
    for (let atc of [
      this.props.attachment,
      this.props.attachment2,
      this.props.attachment3,
      this.props.attachment4,
      this.props.attachment5,
    ]) {
      if (atc) {
        let attachment = await ctx.evalParams(atc);
        let tempFile = await ctx.rpaFiles.fetch(attachment);
        let stat = await fsutil.stat(tempFile);
        totalSize += stat.size;
        if (totalSize > 100 * 1024 * 1024) {
          throw 'file size exceed 100M';
        }
        attachments.push({
          filename: path.basename(tempFile),
          path: tempFile,
        });
      }
    }
    if (attachments.length > 0) {
      //@ts-ignore
      mailOptions.attachments = attachments;
    }
    const userStrs = String(await ctx.evalParams(this.props.userStr));
    let targetMethod = this.props.method || 'user';
    if (targetMethod === 'taskCreator') {
      targetMethod = 'user';
    }
    let params = {
      method: targetMethod,
      userIds:
        this.props.method === 'taskCreator'
          ? [ctx.params.task.creatorId]
          : this.props.userIds || [],
      userStrs: userStrs.split(',') || [],
    };
    try {
      let recipients = await ctx.requestAgent.request(`/api/rpa/task/notifyEmails`, {
        teamId: ctx.item.teamId,
        method: 'post',
        data: params,
      });
      for (let recipient of recipients) {
        mailOptions.to = recipient;
        await transporter.sendMail(mailOptions);
      }
    } catch (e) {
      throw e;
    } finally {
      if (frontendProxy) {
        await frontendProxy.close();
      }
    }
  }
}

//发送短信
class SendSMS extends SendNotify {
  type = 'rpa.notify.SendSMS';
}

//发送微信模板消息
export class SendWeChat extends SendNotify {
  type = 'rpa.notify.SendWeChat';
  declare props: NotifyProps & {
    messageType: 'simple' | 'detail';
    detail: string;
    attachments: string; //一次最多发送5个附件，以换行符分割，附件合计大小不超过100M
  };

  businessType?: string;
  msgUrl?: string; //不向用户暴露，仅当用作工具类的时候用来发消息

  private async loadAttachments(ctx: TaskContext) {
    let attachments: string[] = [];
    if (this.props.attachments) {
      let attachmentsStr = await ctx.evalParams(this.props.attachments);
      if (attachmentsStr) {
        let totalSize = 0;
        let ar = attachmentsStr.trim().split('\n');
        for (let atc of ar) {
          if (atc.trim().length > 0) {
            if (await ctx.rpaFiles.isDirectory(atc)) {
              throw '不允许发送文件夹';
            }
            let filePath = await ctx.rpaFiles.fetch(atc);
            let stats = await fsutil.stat(filePath);
            totalSize += stats.size;
            if (totalSize > 100 * 1024 * 1024) {
              throw '附件总大小超过100M';
            }
            attachments.push(filePath);
          }
        }
      }
    }
    return attachments;
  }

  async extParams(ctx: TaskContext) {
    let detail = undefined;
    if (this.props.messageType == 'detail') {
      detail = await ctx.evalParams(this.props.detail);
      if (typeof detail != 'undefined' && typeof detail != 'string') {
        detail = JSON.stringify(detail);
      }
    }
    let attachments = (await this.loadAttachments(ctx)) || [];
    if (attachments.length > 0) {
      for (let i = 0; i < attachments.length; i++) {
        let basename = path.basename(attachments[i]);
        let workName = 'work_dir://模板消息附件/' + basename;
        await ctx.rpaFiles.upload(workName, attachments[i]);
        attachments[i] = workName;
      }
    }
    return { url: this.msgUrl, businessType: this.businessType, detail, attachments };
  }
}

//发送企业微信机器人消息
const wechat_webhook_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=';
class WechatWebhook extends Node {
  type = 'rpa.webhook.WechatWebhook';
  declare props: {
    key: string; //详见 https://developer.work.weixin.qq.com/document/path/91770
    msgtype: 'text' | 'markdown' | 'image' | 'news' | 'file' | 'template_card';
    content: string; //如果msgtype=file，该值需要指向一个文件
  };

  async exec(ctx: TaskContext): Promise<void> {
    let url = await ctx.evalParams(this.props.key);
    if (!url.startsWith(wechat_webhook_url)) {
      url = `${wechat_webhook_url}${url}`;
    }
    let content = await ctx.evalParams(this.props.content);
    let body = null;
    if ('text' === (this.props.msgtype || 'text')) {
      body = { msgtype: 'text', text: { content: content } };
    } else if ('file' === this.props.msgtype) {
      //文件需要先上传
      let realKey = url.substr(wechat_webhook_url.length);
      let uploadUrl = `https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key=${realKey}&type=file`;
      let file = await ctx.rpaFiles.fetch(content); //路径
      if (!(await fsutil.exists(file))) {
        throw '文件读取失败，请确认文件是否存在';
      }
      let stats = await fsutil.stat(file);
      if (stats.size <= 5 || stats.size >= 20 * 1024 * 1024) {
        throw '企业微信要求文件大小在5B~20M之间';
      }
      let formdata = new FormData();
      let lock = await fsutil.lock(file);
      try {
        formdata.append('media', await fsutil.readFile(file), { filename: path.basename(file) });
      } finally {
        lock.release();
      }
      const response = await axios.request({
        url: uploadUrl,
        method: 'POST',
        headers: { 'Content-Type': `multipart/form-data; boundary=${formdata.getBoundary()}` },
        data: formdata.getBuffer(),
      });
      let result = response.data;
      if ('ok' != result.errmsg) {
        throw result.errmsg || '未知错误';
      }
      let media_id = result.media_id;
      body = {
        msgtype: 'file',
        file: {
          media_id: media_id,
        },
      };
    }
    if (body) {
      let sendHttp = new SendHttp().setOwner(this)._useProps({
        method: 'POST',
        url,
        headers: 'Content-Type: application/json',
        body: body,
      });
      await sendHttp.exec(ctx);
      let result = JSON.parse(sendHttp.execRet);
      if ('ok' != result.errmsg) {
        throw result.errmsg || '未知错误';
      }
    } else {
      throw `${this.props.msgtype} 类型消息暂未实现`;
    }
  }
}

//发送钉钉机器人消息
const ding_talk_webhook_url = 'https://oapi.dingtalk.com/robot/send?access_token=';
class DingTalkWebhook extends Node {
  type = 'rpa.webhook.DingTalkWebhook';
  declare props: {
    access_token: string; //详见 https://open.dingtalk.com/document/robots/custom-robot-access
    msgtype: 'text' | 'link' | 'markdown' | 'ActionCard' | 'FeedCard';
    content: string; //作为属性传递给 msgtype 属性，详见 https://open.dingtalk.com/document/robots/custom-robot-access
  };

  async exec(ctx: TaskContext): Promise<any> {
    let url = await ctx.evalParams(this.props.access_token);
    if (!url.startsWith(ding_talk_webhook_url)) {
      url = `${ding_talk_webhook_url}${url}`;
    }
    let content = await ctx.evalParams(this.props.content);
    let body = null;
    if ('text' === (this.props.msgtype || 'text')) {
      body = { msgtype: 'text', text: { content: content } };
    }
    if (body) {
      let sendHttp = new SendHttp().setOwner(this)._useProps({
        method: 'POST',
        url,
        headers: 'Content-Type: application/json',
        body: body,
      });
      await sendHttp.exec(ctx);
      let result = JSON.parse(sendHttp.execRet);
      if ('ok' != result.errmsg) {
        throw result.errmsg || '未知错误';
      }
    } else {
      throw `${this.props.msgtype} 类型消息暂未实现`;
    }
  }
}

//发送飞书机器人消息
const feishu_webhook_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/';
class FeiShuWebhook extends Node {
  type = 'rpa.webhook.FeiShuWebhook';
  declare props: {
    access_token: string; //详见 https://open.feishu.cn/document/ukTMukTMukTM/ucTM5YjL3ETO24yNxkjN
    msg_type: 'text' | 'post' | 'image' | 'interactive'; //详见 https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json
    content: string; //根据msg_type的不同，要求不同的格式
  };

  async exec(ctx: TaskContext): Promise<void> {
    let url = await ctx.evalParams(this.props.access_token);
    if (!url.startsWith(feishu_webhook_url)) {
      url = `${feishu_webhook_url}${url}`;
    }
    let content = await ctx.evalParams(this.props.content);
    let body = null;
    if ('text' === (this.props.msg_type || 'text')) {
      body = {
        msg_type: 'text',
        content: {
          text: content,
        },
      };
    }
    if (body) {
      let sendHttp = new SendHttp().setOwner(this)._useProps({
        method: 'POST',
        url,
        headers: 'Content-Type: application/json',
        body: body,
      });
      await sendHttp.exec(ctx);
      let result = JSON.parse(sendHttp.execRet);
      if ('success' != result.StatusMessage) {
        throw result.msg || '未知错误';
      }
    } else {
      throw `${this.props.msg_type} 类型消息暂未实现`;
    }
  }
}

//接收邮件
//@since 8.3
class ReceiveEmail extends Node {
  type = 'rpa.notify.ReceiveEmail';
  declare props: {
    protocol: 'imap' | 'pop3'; // 默认 imap
    username: string;
    password: string;
    host: string;
    port: number;
    tls: boolean; // 默认 true
    checkJunk: boolean; //是否检查垃圾邮件，默认 true
    checkDuration: number; // 只检查最近多长时间的邮件，单位小时，默认 24
    sender?: string; //过滤发件人
    subject?: string; //过滤主题
    checkRule: 'first' | 'all'; //接收规则，first: 只接收第一封邮件，all: 接收所有邮件，默认 first
    extract: 'subject' | 'content'; //提取标题还是正文
    extractRegexp?: string; //提取正则表达式，该属性不支持变量

    saveTo: string; //提取到一个变量
  };

  async exec(ctx: TaskContext): Promise<any> {
    let host = await ctx.evalParams(this.props.host);
    let port = ~~(await ctx.evalParams(this.props.port));
    let username = await ctx.evalParams(this.props.username);
    let password = await ctx.evalParams(this.props.password);
    let sender = await ctx.evalParams(this.props.sender);
    let subject = await ctx.evalParams(this.props.subject);
    let extractRegexp = undefined;
    if (this.props.extractRegexp) {
      extractRegexp = new RegExp(this.props.extractRegexp);
    }
    if (this.props.protocol === 'pop3') {
      await this.pop3(ctx, host, port, username, password, sender, subject, extractRegexp);
    } else {
      await this.imap(ctx, host, port, username, password, sender, subject, extractRegexp);
    }
  }

  private accept(
    filterSender: string,
    filterSubject: string,
    fetchBody: boolean,
    from: any,
    subject: string,
    body: string,
    extractRegexp?: RegExp,
  ) {
    let accepted = true;
    if (filterSender || filterSubject) {
      accepted = false;
      if (
        filterSender &&
        (from.address.indexOf(filterSender) != -1 || from.name.indexOf(filterSender) != -1)
      ) {
        accepted = true;
      }
      if (filterSubject && (subject || '').indexOf(filterSubject) != -1) {
        accepted = true;
      }
    }
    if (accepted) {
      let source = (fetchBody ? body : subject) || '';
      if (extractRegexp) {
        let match = extractRegexp.exec(source);
        if (match) {
          return match[0];
        } else {
          return null;
        }
      } else {
        return source;
      }
    }
    return false;
  }

  async imap(
    ctx: TaskContext,
    host: string,
    port: number,
    username: string,
    password: string,
    sender: string,
    subject: string,
    extractRegexp?: RegExp,
  ) {
    let checkJunk = typeof this.props.checkJunk === 'undefined' ? true : !!this.props.checkJunk;
    const connection = new ImapFlow({
      host,
      port,
      secure: !!this.props.tls,
      auth: {
        user: username,
        pass: password,
      },
    });
    try {
      try {
        await connection.connect();
      } catch (e: any) {
        throw '连接失败，请检查邮箱配置。错误原文：' + (e.responseText || e.response || String(e));
      }
      const since = new Date();
      since.setDate(since.getDate() - this.props.checkDuration / 24);
      let junkSids = [];
      if (checkJunk) {
        try {
          await connection.mailboxOpen('Junk', { readOnly: true });
          junkSids = await connection.search({
            since: since.toISOString(),
          });
        } catch (e: any) {
          this.debug(ctx, '尝试检测垃圾箱出现异常：' + (e.responseText || e.message || String(e)));
        }
      }
      await connection.mailboxOpen('INBOX', { readOnly: true });
      let inboxSids = await connection.search({
        since: since.toISOString(),
      });
      let sids = [].concat(inboxSids, junkSids).sort();
      sids = sids.reverse();
      let result: any[] | string = [];
      let first = this.props.checkRule === 'first';
      let fetchBody = this.props.extract === 'content';
      if (sids.length > 0) {
        let currentBox = 'INBOX';
        for (let sid of sids) {
          if (!ctx.keepRun || ctx.destroyed) {
            return;
          }
          let sidBox = junkSids.indexOf(sid) == -1 ? 'INBOX' : 'Junk';
          if (currentBox !== sidBox) {
            switch (sidBox) {
              case 'INBOX':
                await connection.mailboxOpen('INBOX', { readOnly: true });
                break;
              case 'Junk':
                await connection.mailboxOpen('Junk', { readOnly: true });
                break;
            }
            currentBox = sidBox;
          }
          const message = await connection.fetchOne(sid, {
            envelope: true,
            source: fetchBody,
          });
          let envelope = message.envelope;
          let body = ''; //this.parseBody(message.bodyParts.get('1').toString());
          if (fetchBody) {
            let eml: any = await new EmlParser(message.source).parseEml();
            if (eml.html) {
              body = this.parseHtmlBody(eml.html);
            }
            if (!body) {
              body = eml.text;
            }
            if (!body) {
              eml = await new Promise((resolve, reject) => {
                emlformat.read(message.source.toString(), function (error: any, data: any) {
                  if (error) {
                    reject(error);
                  }
                  resolve(data);
                });
              });
              body = eml.text;
              if (!body) {
                body = this.parseHtmlBody(eml.html);
              }
            }
            if (body) {
              body = this.beautify(body);
            }
          }
          let accepted = this.accept(
            sender,
            subject,
            fetchBody,
            envelope.from[0],
            envelope.subject,
            body,
            extractRegexp,
          );
          if (accepted === false) {
            continue;
          } else {
            result.push(accepted);
            if (first) {
              result = result[0];
              break;
            }
          }
        }
      }
      if (this.props.saveTo) {
        saveToParam(ctx.params, this.props.saveTo, result);
      }
      this.execRet = result;
    } catch (e: any) {
      throw e.responseText || e.message || String(e);
    } finally {
      await connection?.logout();
    }
  }

  async pop3(
    ctx: TaskContext,
    host: string,
    port: number,
    username: string,
    password: string,
    sender: string,
    subject: string,
    extractRegexp?: RegExp,
  ) {
    let result: any[] | string = [];
    let first = this.props.checkRule === 'first';
    let fetchBody = this.props.extract === 'content';
    const pop3 = new Pop3Command({
      host: host,
      port: port || 995,
      tls: !!this.props.tls,
    });
    try {
      await pop3.connect();
      if (password.length < 64) {
        await pop3.command('USER', username);
        await pop3.command('PASS', password);
      } else {
        const authString = `user=${username}\u0001auth=Bearer ${password}\u0001\u0001`; // OAuth2 验证格式
        const encodedAuthString = Buffer.from(authString, 'utf-8').toString('base64');
        await pop3.command('AUTH XOAUTH2');
        await pop3.command(encodedAuthString);
      }
      let stat = await pop3.STAT();
      stat = stat.split(' ');
      let last = stat.length > 0 ? ~~stat[0] : 0;
      if (last > 0) {
        const since = new Date();
        since.setDate(since.getDate() - this.props.checkDuration / 24);
        for (let i = last; i > 0; i--) {
          let mail = await (fetchBody ? pop3.RETR(i) : pop3.TOP(i, 0));
          let eml = await new EmlParser(mail).parseEml();
          if (eml.date.getTime() < since.getTime()) {
            //早于指定时间
            break;
          }
          let body = '';
          if (fetchBody) {
            if (eml.html) {
              body = this.parseHtmlBody(eml.html);
            }
            if (!body) {
              body = eml.text;
            }
            if (body) {
              body = this.beautify(body);
            }
          }
          let accepted = this.accept(
            sender,
            subject,
            fetchBody,
            eml.from.value[0],
            eml.subject,
            body,
            extractRegexp,
          );
          if (accepted === false) {
            continue;
          } else {
            result.push(accepted);
            if (first) {
              result = result[0];
              break;
            }
          }
        }
      }
    } finally {
      try {
        await pop3.QUIT();
      } catch (ignore) {}
    }
    if (this.props.saveTo) {
      saveToParam(ctx.params, this.props.saveTo, result);
    }
    this.execRet = result;
  }

  parseHtmlBody(html: string) {
    if (!!html && /<html/.test(html)) {
      const $ = cheerio.load(html, { decodeEntities: false });
      $('style').remove();
      $('[style*="display: none"]').remove();
      $('[style*="visibility: hidden"]').remove();
      return ($('body').text() || $.text() || '').trim();
    }
    return '';
  }

  beautify(str: string) {
    if (str) {
      //去掉空白行
      let ret = '';
      let lines = str.split('\n');
      for (let i = 0; i < lines.length; i++) {
        let line = lines[i].trim();
        if (line.length == 0) {
        } else {
          ret += line + '\n';
        }
      }
      return ret;
    }
    return str;
  }
}

export const NodeTypes = {
  'rpa.notify.SendEmail': SendEmail,
  'rpa.notify.SendSMS': SendSMS,
  'rpa.notify.SendWeChat': SendWeChat,
  'rpa.notify.ReceiveEmail': ReceiveEmail,
  'rpa.webhook.WechatWebhook': WechatWebhook,
  'rpa.webhook.DingTalkWebhook': DingTalkWebhook,
  'rpa.webhook.FeiShuWebhook': FeiShuWebhook,
};
