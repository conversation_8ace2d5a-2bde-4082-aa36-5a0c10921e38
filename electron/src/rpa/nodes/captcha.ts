//验证码相关的节点
import { Node, NodeContext, PageNode } from '@e/rpa/nodes/base';
import { TaskContext } from '@e/rpa/context';
import random from '@e/utils/random';
import {
  getBoundingClientRect,
  getSprintTab,
  saveToParam,
  tryUseIframe,
  waitMilliseconds,
  waitSeconds,
} from '@e/rpa/utils';
import { BrowserWindow, ipcMain, IpcMainEvent, systemPreferences } from 'electron';
import appConfig from '@e/configs/app';
import path from 'path';
import { SendWeChat } from '@e/rpa/nodes/notifies';
import { ScreenShot } from '@e/rpa/nodes/index';
import { resolveUrl } from '@e/utils/utils';
import db from '@e/components/db';
import axios from 'axios';
import os from 'os';

const FormData = require('form-data');

//远程人工干预节点
class OperateCaptcha extends PageNode {
  type = 'rpa.captcha.OperateCaptcha';
  declare props: {
    notifyType: 'SendWeChat' | 'SendSMS' | 'SendEmail'; //分别对应发送消息节点的模板消息，短信和邮件
    method: 'user' | 'text';
    userIds: number[]; //接收人员id列表
    userStr: string; //接收人员字符串（手机号或邮箱），逗号分隔
    openIdStr: string; //接收人员openId字符串，逗号分隔
    refreshNode: string; //看不清楚要执行的node
    selector: string; //如果只想向接收者展示一部分内容，可以指定这部分内容的selector
  };

  async exec(ctx: TaskContext): Promise<void> {
    await this.runHelper(ctx, { capture: 'stream' });
  }

  async runHelper(ctx: TaskContext, options: any) {
    this.helper = new CaptchaHelper();
    this.roomName = random.nextString(16);
    let stun = await this.getStunInfo(ctx);

    await ctx.requestAgent.request(
      `/api/rpa/task/captcha/createRoom/${this.roomName}?duration=${this.timeout || 600}`,
      {
        method: 'put',
      },
    );

    //获取窗口大小和可视区大小
    const client = await ctx.page.target().createCDPSession();
    const { bounds } = await client.send('Browser.getWindowForTarget', {
      //@ts-ignore
      targetId: ctx.page.target()._targetId,
    });
    client.detach();
    let innerSize = await ctx.page.evaluate(() => {
      return [window.innerWidth, window.innerHeight];
    });
    let box: any = {
      ww: bounds.width,
      wh: bounds.height,
      vl: bounds.width! - innerSize[0],
      vt: bounds.height! - innerSize[1],
      vw: innerSize[0],
      vh: innerSize[1],
      el: undefined,
      et: undefined,
      ew: undefined,
      eh: undefined,
    };

    if (this.props.selector) {
      let _iframe = this.iframe;
      if (
        typeof this.props.selector === 'string' &&
        /^element:\/\/([^\s]+)/.test(this.props.selector)
      ) {
        let elementId = RegExp.$1;
        let element = (ctx.flow.config.elements || []).find((e) => e.id === elementId);
        if (element) {
          _iframe = element.iframe;
        }
      }
      let el = await this.$(ctx, this.props.selector!);
      let rect = await getBoundingClientRect(ctx, el!, _iframe);
      if (rect) {
        box.el = box.vl + rect.x;
        box.et = box.vt + rect.y;
        box.ew = rect.width;
        box.eh = rect.height;
      }
    }
    options = Object.assign({ browserTitle: ctx.browserTitle }, options, { box });

    await this.helper.exec(ctx, this.roomName, stun, options);
    while (ctx.keepRun) {
      await waitSeconds(1, false);
      if (ctx.keepRun) {
        let { status, msg, captchaCode } = await this.helper.checkCaptcha();
        if (status != this.status) {
          this.status = String(status);
          switch (this.status) {
            case 'waiting':
              if (!this.notifySent) {
                let msgUrl = resolveUrl(
                  db.getWxUrl(),
                  `/wx/rpaCaptcha/token/${stun.channel}-${this.roomName}-${encodeURIComponent(
                    Buffer.from(stun.signaling).toString('base64'),
                  )}`,
                );
                this.debug(ctx, msgUrl);
                await this.sendNotify(ctx, msgUrl);
              }
              this.notifySent = true;
              break;
            case 'error':
              throw msg;
            case 'done': //操作完成
              return captchaCode;
          }
        }
      }
    }
  }

  roomName?: string;

  helper!: CaptchaHelper;
  status = 'preparing';

  notifySent = false;

  protected async getStunInfo(ctx: TaskContext) {
    return await ctx.requestAgent.request(`/api/rpa/task/getStunInfo`);
  }

  protected async sendNotify(ctx: TaskContext, url: string) {
    if (this.props.notifyType == 'SendWeChat') {
      let sendWeChat = new SendWeChat().setOwner(this)._useProps({
        subject: '',
        content: '请点击详情进行远程人工干预',
        method: this.props.method,
        userIds: this.props.userIds,
        userStr: this.props.userStr,
        openIdStr: this.props.openIdStr,
      });
      sendWeChat.msgUrl = url;
      sendWeChat.businessType = '操作型验证码';
      await sendWeChat.exec(ctx);
    } //else
  }

  release(ctx: TaskContext) {
    super.release(ctx);
    if (!!this.roomName) {
      ctx.requestAgent
        .request(`/api/rpa/task/captcha/closeRoom/${this.roomName}`, {
          method: 'delete',
        })
        .then(() => {})
        .catch(() => {});
    }
    this.helper?.close();
  }

  fromJson(nctx: NodeContext, nid: string, nodes: any) {
    super.fromJson(nctx, nid, nodes);
  }

  gc() {
    super.gc();
  }
}

class CaptchaHelper {
  helperWindow!: BrowserWindow;

  roomName!: string;

  channelHolds = new Map();
  async exec(ctx: TaskContext, roomName: string, stun: any, options: any) {
    if (!['freebsd', 'openbsd', 'linux'].includes(os.platform())) {
      let accessStatus = await systemPreferences.getMediaAccessStatus('screen');
      if ('granted' != accessStatus) {
        throw '未获得录制屏幕权限';
      }
    }

    this.roomName = roomName;
    this.helperWindow = new BrowserWindow({
      show: false,
      width: 400,
      height: 300,
      maximizable: false,
      fullscreenable: false,
      webPreferences: {
        devTools: appConfig.DEBUG,
        preload: path.join(__dirname, 'helper/captcha_helper.js'),
        nodeIntegration: true,
        // enableRemoteModule: true,
        webSecurity: false,
        backgroundThrottling: false,
      },
    });
    await this.helperWindow.loadFile(path.join(__dirname, './helper/index.html'));
    // try {
    //   this.helperWindow.webContents.openDevTools();
    // } catch (ignore) {}
    ipcMain.on(`rpa-${this.roomName}-log`, (evt, msg) => {
      if (typeof msg != 'string') {
        msg = JSON.stringify(msg);
      }
      ctx.log(msg);
    });
    ipcMain.on(`rpa-${this.roomName}-mouse`, (evt, m) => {
      this.onMouseEvent(ctx, m);
    });
    ipcMain.on(`rpa-${this.roomName}-type`, (evt, text) => {
      this.onTypeEvent(ctx, text);
    });
    ipcMain.on(`rpa-${this.roomName}-cb`, (evt: IpcMainEvent, result: any) => {
      const cb = result.cb;
      if (this.channelHolds.has(cb)) {
        let [resolve, reject] = this.channelHolds.get(cb);
        result.success ? resolve(result.data) : reject(result.data);
      }
    });
    await this.helperWindow.webContents.send(`rpa-channel`, {
      action: 'roomName',
      data: roomName,
    });
    await this.helperWindow.webContents.send(`rpa-channel`, {
      action: 'captcha',
      data: { roomName, stun, options },
    });
  }

  async checkCaptcha(): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const cb = random.nextString();
      await this.helperWindow.webContents.send('rpa-channel', { action: 'checkCaptcha', cb });
      this.channelHolds.set(cb, [resolve, reject]);
    });
  }

  mouseQueue: string[] = [];
  mousing = false;
  async onMouseEvent(ctx: TaskContext, m: string) {
    this.mouseQueue.push(m);
    if (!this.mousing) {
      this.mousing = true;
      try {
        while (this.mouseQueue.length > 0) {
          let first = this.mouseQueue.shift();
          //time,x,y,action,button
          let mouses = first!.split(' ');
          for (let mouse of mouses) {
            let ms = mouse.split(',');
            let time = ~~ms[0];
            let x = ~~ms[1];
            let y = ~~ms[2];
            if (time <= 0 && ms.length < 5) {
              continue;
            }
            if (time > 0) {
              await waitMilliseconds(time, false);
            }
            await ctx.page.mouse.move(x, y);
            if (ms.length == 5) {
              let action = ms[3]; //down, up
              let button = ms[4]; //'left' | 'right' | 'middle'
              ctx.fakeAction.currPos = { x, y };
              if (action == 'down') {
                //@ts-ignore
                await ctx.fakeAction.fitMouseDown(ctx.page, button);
              } else {
                //@ts-ignore
                await ctx.fakeAction.fitMouseUp(ctx.page, button);
              }
            }
          }
        }
      } finally {
        this.mousing = false;
      }
    }
  }

  async onTypeEvent(ctx: TaskContext, text: string) {
    await ctx.page.keyboard.type(text, { delay: 100 });
  }

  async close() {
    if (this.roomName) {
      ipcMain.removeAllListeners(`rpa-${this.roomName}-op`);
      ipcMain.removeAllListeners(`rpa-${this.roomName}-log`);
      ipcMain.removeAllListeners(`rpa-${this.roomName}-mouse`);
      ipcMain.removeAllListeners(`rpa-${this.roomName}-cb`);
    }
    // try {
    //   this.helperWindow.webContents.closeDevTools();
    // } catch (ignore) {}
    this.helperWindow?.close();
  }
}

//输入型验证码节点 (已不再支持，会完全交由 OperateCaptcha 实现)
class TypeCaptcha extends Node {
  type = 'rpa.captcha.TypeCaptcha';
  static compatibleClass(nodeJson: any) {
    let props = nodeJson.props || {};
    props.selector = undefined;
    return OperateCaptcha;
  }
}

//识别图片验证码节点
class ImageCaptchaOcrNode extends PageNode {
  type = 'rpa.captcha.ImageCaptchaOcrNode';
  declare props: {
    selector: string; //验证码图片选择器
    selector1: string; //有些验证码节点需要两个图片节点，如 tk.rotate

    provider: 'chaojiying' | 'ttshitu' | 'huayoung'; //识别服务提供商，目前只支持超级鹰

    //如果是超级鹰，表示验证码类型，默认值是 1010，参见 https://www.chaojiying.com/price.html
    //如果是图鉴，表示验证码类型，默认值3，参见 http://www.ttshitu.com/docs/index.html
    //如果是huayoung，表示验证码类型，支持 'tk.dragBlock' | ...
    codeType: string;

    customKey: boolean; //是否使用用户自己的key
    /*
     * 下面参数根据provider不同而不同
     * 如果是chaojiying，param1=user, param2=pass, param3=softid 参见 https://www.chaojiying.com/api-5.html
     * 如果是ttshitu，param1=username, param2=password
     */
    param1: string;
    param2: string;
    param3: string;
    param4: string;

    // 验证码附加参数
    extraParam1: string;
    extraParam2: string;
    extraParam3: string;
    extraParam4: string;

    saveTo: string; //保存识别结果到变量
  };

  get premiseSelector(): any[] {
    let selectors = [this.props.selector];
    switch (this.props.codeType || '') {
      case 'tk.rotate':
      case 'ac.matchImage':
        selectors.push(this.props.selector1);
        break;
    }
    return selectors;
  }

  private async captureElement(ctx: TaskContext, selector?: string) {
    let buffer = await new ScreenShot()
      .setOwner(this)
      ._useProps({
        selector: selector || this.props.selector,
      })
      .captureElement(ctx, 'captcha');
    let imageUrl = buffer.toString('base64');
    return imageUrl;
  }

  //tk.rotate
  private async prepareTkRotate(ctx: TaskContext, data: any) {
    const base64Fun = (image: any) => {
      if (typeof image == 'string') {
        image = document.querySelector(image);
      }
      return new Promise((resolve, reject) => {
        try {
          //@ts-ignore
          if (image.tagName == 'IMG' && image.complete) {
            let canvas = document.createElement('canvas');
            //@ts-ignore
            canvas.width = image.naturalWidth;
            //@ts-ignore
            canvas.height = image.naturalHeight;
            let context = canvas.getContext('2d')!;
            context.drawImage(image, 0, 0);
            // 将canvas的内容转换为base64编码的字符串
            let base64 = canvas.toDataURL('image/jpeg'); // 可以根据需要更改为其他格式，如'image/jpeg'等
            resolve(base64.substring('data:image/jpeg;base64,'.length));
          } else {
            resolve(false);
          }
        } catch (e) {
          reject(e);
        }
      });
    };
    let tryCnt = 0;
    const captureImage = async (imgSelector: any) => {
      //首先尝试采用直接下载图片的方法
      let imageEl = await this.$(ctx, imgSelector);
      let src = await imageEl?.evaluate((el: any) => el.src);
      if (src && /^https?/.test(src)) {
        try {
          let userAgent = await ctx.page.evaluate(() => window.navigator.userAgent);
          let referer = getSprintTab(ctx.page).url;
          ctx.rpaFiles.httpFetcher.headers = { 'User-Agent': userAgent, Referer: referer };
          let imageFile = await ctx.rpaFiles.fetch(src);
          let data = await ctx.rpaFiles.readFile(imageFile);
          return data.toString('base64');
        } catch (e) {}
      }

      let selector = await ctx.evalParams(imgSelector);
      if (
        typeof selector === 'string' && //selector为字符串
        !/^\//.test(selector) && //不是xpath
        !/^element:\/\/([^\s]+)/.test(selector) //不是元素变量
      ) {
        let frame = await tryUseIframe(ctx, this.iframe);
        return await (frame || ctx.page).evaluate(base64Fun, selector);
      } else {
        return await imageEl?.evaluate(base64Fun);
      }
    };
    while (ctx.keepRun) {
      try {
        data.image = await captureImage(this.props.selector);
        data.image1 = await captureImage(this.props.selector1);
        break;
      } catch (e) {
        if (tryCnt++ >= 5) {
          throw e;
        }
        await waitSeconds(1, false);
      }
    }
  }

  async exec(ctx: TaskContext): Promise<void> {
    let [innerWidth, innerHeight, outerWidth, outerHeight] = await ctx.page.evaluate(() => {
      return [window.innerWidth, window.innerHeight, window.outerWidth, window.outerHeight];
    });
    // if(innerWidth != outerWidth) {
    //   throw '当前页面存在缩放，无法进行验证码识别';
    // }
    let text;
    if (this.props.customKey) {
      text = await this.execAsCustomParam(ctx);
    } else {
      let codeType = (await ctx.evalParams(this.props.codeType)) || '1010';
      let data: any = {
        taskId: ctx.params.task.id,
        provider: this.props.provider,
        codeType: codeType,
      };
      switch (codeType) {
        case 'tk.rotate':
          await this.prepareTkRotate(ctx, data);
          break;
        case 'ac.matchImage': //ac = air china site: https://m.airchina.com.cn/
          data.image = await this.captureElement(ctx, this.props.selector);
          data.image1 = await this.captureElement(ctx, this.props.selector1);
          break;
        case 'ac.clickObjects':
          data.image = await this.captureElement(ctx, this.props.selector);
          data.param1 = await ctx.evalParams(this.props.extraParam1);
          break;
        case 'common.dragBlock':
        //通用的拖动缺口滑块验证码
        default:
          data.image = await this.captureElement(ctx);
          break;
      }

      let resp = await ctx.requestAgent.request(`/api/rpa/task/captcha/captchaOcr`, {
        method: 'post',
        teamId: ctx.item.teamId,
        data: data,
      });
      if (resp.success) {
        text = resp.text;
      } else {
        throw resp.message;
      }
    }
    if (this.props.saveTo) {
      saveToParam(ctx.params, this.props.saveTo, text);
    }
  }

  async execAsCustomParam(ctx: TaskContext): Promise<void> {
    let imageUrl = await this.captureElement(ctx);
    let codeType = (await ctx.evalParams(this.props.codeType)) || '1010';
    let param1 = await ctx.evalParams(this.props.param1);
    let param2 = await ctx.evalParams(this.props.param2);
    let param3 = await ctx.evalParams(this.props.param3);
    let param4 = await ctx.evalParams(this.props.param4);
    if (typeof param1 === 'undefined' || param1 == null) {
      throw '缺少自定义参数';
    }
    if ('chaojiying' === this.props.provider) {
      const formData = new FormData();
      formData.append('file_base64', imageUrl);
      formData.append('user', param1);
      formData.append('pass', param2);
      formData.append('softid', param3);
      formData.append('codetype', codeType);
      let { status, statusText, data } = await axios.post(
        'http://upload.chaojiying.net/Upload/Processing.php',
        formData,
        {
          headers: {
            ...formData.getHeaders(),
          },
        },
      );
      if (status != 200) {
        throw statusText;
      }
      if (data.err_no != 0) {
        throw data.err_str;
      }
      return data.pic_str;
    } else if ('ttshitu' === this.props.provider) {
      let { status, statusText, data } = await axios.post('http://api.ttshitu.com/predict', {
        timeout: 60000,
        username: param1, //用户名
        password: param2, //密码
        typeid: codeType,
        image: imageUrl,
      });
      if (status != 200) {
        throw statusText;
      }
      if (`${data.success}` === 'true') {
        return data.data.result;
      } else {
        throw data.message;
      }
    } else {
      throw '未知的识别服务提供商';
    }
  }
}

export const NodeTypes = {
  // 'rpa.captcha.TypeCaptcha': TypeCaptcha,
  'rpa.captcha.OperateCaptcha': OperateCaptcha,

  'rpa.captcha.ImageCaptchaOcrNode': ImageCaptchaOcrNode,
};
