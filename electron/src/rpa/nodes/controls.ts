import {Condition} from '@e/rpa/node_condition';
import {TaskContext} from '@e/rpa/context';
import {prepareSubParams, saveToParam, waitSeconds} from '@e/rpa/utils';
import {scripts} from '@e/rpa/nodes_script';
import {findNodeClass} from '@e/rpa/nodes/index';
import {HiddenLog, Node, NodeContext, PageNode} from '@e/rpa/nodes/base';
import {WalkDog} from '@e/rpa/dog';
import {HTTPResponse, Page} from 'donkey-puppeteer-core';
import random from '@e/utils/random';

// condition Node
export class ConditionNode extends Node {
  getConditions(): Array<Condition> {
    throw 'must implements';
  }

  async test(ctx: TaskContext, and: boolean = true) {
    let flag = true;
    let conditions = this.getConditions() || [];
    let falseNum = 0;
    for (let i = 0; i < conditions.length; i++) {
      let flagI = await conditions[i].test(ctx, this);
      if (and) {
        if (!flagI) {
          flag = false;
          break;
        }
      } else {
        if (flagI) {
          //或关系，有任何一个满足
          flag = true;
          break;
        } else {
          falseNum++;
          if (falseNum === conditions.length) {
            flag = false; //所有条件都不满足
          }
        }
      }
    }
    return flag;
  }
}

//if分支
class If extends ConditionNode {
  type = 'rpa.control.If';

  declare props: {
    relation: string; // 多个条件之间的关系 and | or
    conditions: Array<any>;
    ifNode: string;
    elseNode: string;
  };
  conditions: Array<Condition> = [];
  ifNode?: Node;
  elseNode?: Node;

  async exec(ctx: TaskContext): Promise<any> {
    if (ctx.destroyed) {
      return;
    }
    let and = this.props.relation !== 'or';
    let flag = await this.test(ctx, and);
    let header = null;
    if (flag) {
      this.log(ctx, `条件满足，将执行 IF 分支`);
      header = this.ifNode;
    } else {
      this.log(ctx, `条件未满足，将尝试执行 ELSE 分支`);
      header = this.elseNode;
    }
    if (header) {
      this.dog = new WalkDog(ctx.createNodeTaskContext(this.nid), header);
      await this.dog.walk();
      this.execRet = this.dog?.popRet().val;
    }
  }

  getConditions(): Array<Condition> {
    return this.conditions;
  }

  fromJson(nctx: NodeContext, nid: string, nodes: any) {
    super.fromJson(nctx, nid, nodes);

    //将属性还原成节点Obj
    this.props.relation = this.props.relation || 'and';
    if (this.props.conditions) {
      for (let i = 0; i < this.props.conditions.length; i++) {
        this.conditions.push(new Condition(this.props.conditions[i]));
      }
    }

    nctx.tiers.push(nid);
    if (this.props.ifNode) {
      let NodeClass = findNodeClass(this, this.props.ifNode, nodes);
      this.ifNode = new NodeClass();
      this.ifNode!.fromJson(nctx, this.props.ifNode, nodes);
    }
    if (this.props.elseNode) {
      let NodeClass = findNodeClass(this, this.props.elseNode, nodes);
      this.elseNode = new NodeClass();
      this.elseNode!.fromJson(nctx, this.props.elseNode, nodes);
    }
    nctx.tiers.pop();
  }

  gc() {
    super.gc();
    this.ifNode = undefined;
    this.elseNode = undefined;
  }
}

//直接退出整个流程，通常应该是和If配合使用
class Exit extends Node {
  type = 'rpa.control.Exit';
  declare props: {
    success: boolean; //是否成功，默认 true
    message: string; //只在 success == false 时有意义，表示失败原因
  };

  async exec(ctx: TaskContext): Promise<any> {
    this.log(ctx, '流程将被结束');
    let success = true;
    let message = '';
    if (typeof this.props.success !== 'undefined') {
      success = !!(await ctx.evalParams(this.props.success));
      message = await ctx.evalParams(this.props.message);
    }
    let ret: any = {
      success,
    };
    if (!success) {
      ret.error = message || '终止流程（未指定具体错误信息）';
    }
    ctx.meetExit(ret);
    ctx.keepRun = false;
    if (!success) {
      throw ret.error;
    }
  }
}

/**
 * 直接退出整个流程任务
 * @since 7.7
 */
class ExitWholeTask extends Node {
  type = 'rpa.control.ExitWholeTask';
  declare props: {
    interrupt: boolean; //是否中断已经开始的item，默认 true。如果为false则只中断未打开的item，已经开始的item会让它继续跑完
  };

  async exec(ctx: TaskContext): Promise<any> {
    ctx.meetExit({ success: true });
    ctx.keepRun = false;
    ctx.emitter.emit('rpa_evt_exitWholeTask', {
      sourceItem: ctx.item.id,
      interrupt: this.props.interrupt,
    });
  }
}

//调用子流程
export class InvokeSubFlow extends ConditionNode {
  type = 'rpa.control.InvokeSubFlow';
  declare props: {
    sid: string; //子流程的sid
    params: []; //[key, value, key, value, ...]格式数组，将value赋值给子流程的key变量
    saveTo: string;
  };
  header!: Node;
  subParams!: any[];

  async exec(ctx: TaskContext): Promise<void> {
    if (!this.header) {
      throw '未找到子流程';
    }
    let userParamsObj = await prepareSubParams(ctx, this.subParams, this.props.params);
    let subFlowCtx = ctx.createSubFlowTaskContext(this.nid, userParamsObj);
    subFlowCtx._browserParams = this.subParams.filter(p => !p.nodeOnly).map(p => p.name);
    await this.doInvokeSubflow(ctx, subFlowCtx);
  }

  protected async doInvokeSubflow(ctx: TaskContext, subFlowCtx: TaskContext) {
    if (ctx.destroyed) {
      return;
    }
    //执行子流程
    this.dog = new WalkDog(subFlowCtx, this.header);
    //手动串起主流程链和子流程链
    this.dog.parent = ctx.current?.dog;
    if (this.dog.parent) {
      this.dog.parent.child = this.dog;
    }
    await this.dog.walk();
    if (ctx.destroyed) {
      return;
    }
    let ret = this.dog?.popRet();
    if (!ret?.success) {
      let err = ret.error || '子流程出现不包含详细信息的异常';
      throw err;
    }
    saveToParam(ctx.params, this.props.saveTo, ret.val);
    this.execRet = ret.val;
  }

  fromJson(nctx: NodeContext, nid: string, nodes: any) {
    super.fromJson(nctx, nid, nodes);
    let header = nodes[this.props.sid];
    if (header) {
      this.subParams = header?.subParams || {};
      let NodeClass = findNodeClass(this, this.props.sid, nodes);
      this.header = new NodeClass();
      this.header.fromJson(new NodeContext(nctx.config, nctx), this.props.sid, nodes);
    }
  }

  getConditions(): Array<Condition> {
    return [];
  }

  gc() {
    super.gc();
    //@ts-ignore
    this.header = undefined;
  }
}

//本机人工干预节点
class Manual extends PageNode {
  type = 'rpa.control.Manual';
  declare props: {
    subject: string;
    content: string;
  };

  manualPage?: Page;
  async exec(ctx: TaskContext): Promise<any> {
    const subject = await ctx.evalParams(this.props.subject);
    const content = await ctx.evalParams(this.props.content);
    await ctx.page.evaluate(scripts['rpa.control.Manual.confirm'], { subject, content });

    this.log(ctx, '等待用户点击操作完成按钮...');
    this.manualPage = ctx.page;
    const emitter = this.manualPage.on('domcontentloaded', () => {
      ctx.page.evaluate(scripts['rpa.control.Manual.confirm'], { subject, content });
    });
    try {
      let keepQuery = true;
      this.manualPage.on('close', () => {
        keepQuery = false;
        emitter.removeAllListeners('domcontentloaded');
      });
      while (keepQuery) {
        try {
          const hasFinished = await this.manualPage.evaluate(`window.__hy_rpa_manual_finished__`);
          if (hasFinished) {
            emitter.removeAllListeners('domcontentloaded');
            await this.manualPage.evaluate(`window.__hy_rpa_manual_finished__ = undefined`);
            break;
          }
        } catch (e) {}
        await waitSeconds(1);
      }
      this.log(ctx, '用户已点击操作完成按钮');
    } finally {
      emitter?.removeAllListeners('domcontentloaded');
    }
  }

  release(ctx: TaskContext) {
    super.release(ctx);
    this.manualPage?.removeAllListeners('domcontentloaded');
    this.manualPage?.evaluate(() => {
      let el = document.getElementById('rpa_manual_div');
      if (el) {
        el.remove();
      }
    });
  }

  gc() {
    super.gc();
    this.manualPage = undefined;
  }
}
interface TriggerInvokeSubFlow_props {
  sid: string; //子流程的sid
  params: []; //[key, value, key, value, ..., response: ]格式数组，将value赋值给子流程的key变量
  saveTo: string;

  relation: string; // 多个条件之间的关系 and | or
  conditions: Array<any>;
}
abstract class TriggerInvokeSubFlow extends InvokeSubFlow {
  declare props: TriggerInvokeSubFlow_props;
  conditions: Array<Condition> = [];

  //事件子流程不与主流程共享上下文
  vmCtx!: TaskContext;

  getConditions(): Array<Condition> {
    return this.conditions;
  }

  conditionInterval: any;
  private async checkCondition(ctx: TaskContext) {
    if (!ctx.keepRun || ctx.destroyed) {
      this.stop();
      return;
    }
    let and = this.props.relation !== 'or';
    if (await this.test(ctx, and)) {
      this.log(ctx, '符合注销条件，停止监听');
      this.stop();
    }
  }

  protected initTriggerBeforeExec(ctx: TaskContext) {
    this.vmCtx = ctx.createEventTaskContext(this.nid, {});
    this.vmCtx.current = ctx.current;
    this.vmCtx.initVM();
    if (this.getConditions().length > 0) {
      this.conditionInterval = setInterval(() => this.checkCondition(ctx), 1000);
    }
  }

  protected stop() {
    clearInterval(this.conditionInterval);
    this.vmCtx?.destroy();
    //@ts-ignore
    this.vmCtx = undefined;
  }

  gc() {
    super.gc();
    this.stop();
  }

  fromJson(nctx: NodeContext, nid: string, nodes: any) {
    super.fromJson(nctx, nid, nodes);
    //将属性还原成节点Obj
    this.props.relation = this.props.relation || 'and';
    if (this.props.conditions) {
      for (let i = 0; i < this.props.conditions.length; i++) {
        this.conditions.push(new Condition(this.props.conditions[i]));
      }
    }
  }
}

/**
 * 定时触发
 * 用于定时触发一个子流程
 * @since 7.5
 */
class Timer extends TriggerInvokeSubFlow {
  type = 'rpa.control.Timer';
  since = 7.5;
  declare props: {
    interval: number; // 定时器的间隔时间，单位秒
  } & TriggerInvokeSubFlow_props;

  timerInterval: any;
  subFlowRunning = false;

  async exec(ctx: TaskContext): Promise<any> {
    this.stop();
    if (this.props.interval && this.props.interval >= 1) {
      this.initTriggerBeforeExec(ctx);
      const fun = async () => {
        if (this.subFlowRunning) {
          return;
        }
        this.subFlowRunning = true;
        try {
          if (ctx.suspendLock?.needWait('random_id_' + random.nextString(8))) {
            //线程被暂停，不触发此次timer
            return;
          }
          if (!ctx.keepRun || ctx.destroyed) {
            this.stop();
          } else {
            let userParamsObj = await prepareSubParams(ctx, this.subParams, this.props.params);
            let subFlowCtx = this.vmCtx.createEventTaskContext(this.nid, userParamsObj);
            await this.doInvokeSubflow(ctx, subFlowCtx);
          }
        } finally {
          this.subFlowRunning = false;
        }
      };
      this.timerInterval = setInterval(() => {
        fun();
      }, this.props.interval * 1000);
    } else {
      throw '定时触发间隔必须不小于 1 秒';
    }
  }

  protected stop() {
    this.subFlowRunning = false;
    clearInterval(this.conditionInterval);
    clearInterval(this.timerInterval);
    super.stop();
  }
}

/**
 * 监听AJAX请求
 * @since 7.5
 */
class Ajax extends TriggerInvokeSubFlow {
  type = 'rpa.control.Ajax';
  isPageNode = true;

  declare props: {
    concurrent: boolean; //如果有多个ajax请求同时触发，是否并发触发事件子流程
    filter?: string; //url过滤器，如果是普通字符串则判断url是否包含这个字符串，如果是正则表达式则判断url是否匹配这个正则表达式
  } & TriggerInvokeSubFlow_props;

  urlFilter?: any;

  responses: any[] = [];
  executing = false;
  private ajaxPage?: Page;

  async exec(ctx: TaskContext): Promise<any> {
    this.stop();
    this.initTriggerBeforeExec(ctx);
    this.onResponse_bind = this.onResponse.bind(this, ctx);
    let filter = await ctx.evalParams(this.props.filter);
    if (filter) {
      filter = filter.trim();
      if (filter.startsWith('/')) {
        try {
          filter = await ctx.runNodeScript(`(()=>${filter})()`);
        } catch (ignore) {}
      }
      this.urlFilter = filter;
    }
    this.ajaxPage?.removeAllListeners('response'); //不允许同时监听多个
    this.ajaxPage = ctx.page;
    this.ajaxPage.on('response', this.onResponse_bind);
  }

  private onResponse_bind: any;
  async onResponse(ctx: TaskContext, response: HTTPResponse) {
    if (!ctx.keepRun || ctx.destroyed) {
      this.stop();
      return;
    }

    if (this.acceptResponse(response)) {
      if(!!this.props.concurrent) { //并发执行
        await this.doHandleResponse(ctx, response);
      } else {//排队执行
        this.responses.push(response);
        if (!this.executing) {
          try {
            let resp = this.responses.shift();
            this.executing = true;
            while (resp) {
              if (!ctx.keepRun || ctx.destroyed) {
                this.stop();
                return;
              }
              if (ctx.suspendLock?.needWait('random_id_' + this.nid)) {
                await ctx.suspendLock.promise();

                if (!ctx.keepRun || ctx.destroyed) {
                  this.stop();
                  return;
                }
              }

              await this.doHandleResponse(ctx, resp);

              resp = this.responses.shift();
            }
          } finally {
            this.executing = false;
          }
        }
      }
    }
  }

  private async doHandleResponse(ctx: TaskContext, resp: HTTPResponse) {
    let userParamsObj: any = await prepareSubParams(ctx, this.subParams, this.props.params);
    userParamsObj.response = resp;
    // 这里使用双层 EventTaskContext 的原因是：子流程有一个局部变量 response ，同时并发请求的时候 response 可能会互相覆盖
    let subFlowCtx = this.vmCtx.createEventTaskContext(this.nid, userParamsObj);
    await this.doInvokeSubflow(ctx, subFlowCtx);
    userParamsObj.response = undefined;
  }

  private acceptResponse(response: HTTPResponse): boolean {
    const resourceType = response.request().resourceType();
    if (resourceType === 'fetch' || resourceType === 'xhr') {
      let url = response.request().url();
      if(!this.urlFilter) {
        return true;
      } else {
        if (this.urlFilter instanceof RegExp) {
          if (this.urlFilter.test(url)) {
            return true;
          }
        } else {
          if (url.indexOf(this.urlFilter) !== -1) {
            return true;
          }
        }
      }
    }
    return false;
  }

  protected stop() {
    if (this.ajaxPage && this.onResponse_bind) {
      this.ajaxPage.off('response', this.onResponse_bind);
    }
    this.responses = [];
    this.ajaxPage = undefined;
    this.onResponse_bind = undefined;
    super.stop();
  }

  gc() {
    super.gc();
    this.stop();
  }
}

//监听页面里的事件，并允许调起一个子流程
//@deprecated 已经由 Time 和 ListeningAjax 代替
class ListeningPageEvent extends Node {
  type = 'rpa.control.ListeningEvent';

  static compatibleClass(nodeJson: any) {
    let props = nodeJson.props || {};
    if (props.event === 'timer') {
      props.interval = props.eventParams[0];
      return Timer;
    } else if (props.event === 'ajax-response') {
      return Ajax;
    }
    return undefined;
  }

  async exec(ctx: TaskContext): Promise<any> {
    // 已经由 Time 和 ListeningAjax 代替
  }
}

//返回节点，返回指定值
export class Return extends Node {
  type = 'rpa.control.Return';
  declare props: {
    value: string;
  };

  async exec(ctx: TaskContext): Promise<any> {
    let value = await ctx.evalParams(this.props.value);
    ctx.meetReturn(this);
    this.next = undefined; //Return节点不允许有next节点
    this.execRet = value;
  }
}

//锚点，Goto节点跳转的目标点
class Anchor extends Node {
  type = 'rpa.control.Anchor';
  declare props: {
    label: string; //
  };
  async exec(ctx: TaskContext): Promise<any> {
    //无执行逻辑
  }

  get walkLog(): boolean {
    return false;
  }

  fromJson(nctx: NodeContext, nid: string, nodes: any) {
    super.fromJson(nctx, nid, nodes);
    //缓存Anchors ?
    nctx.addAnchor(this);
  }
}

//允许跳转到一个Anchor节点。Goto必须和对应的Anchor节点同在主流程或位于同一个子流程
class Goto extends Node {
  type = 'rpa.control.Goto';
  declare props: {
    target: string; //必须是一个Anchor节点的nid
  };
  anchor!: Anchor; //要goto的锚点

  async exec(ctx: TaskContext): Promise<any> {
    if (this.anchor) {
      ctx.meetAnchor(this.anchor);
      await this.log(ctx, `跳转到${this.anchor.props.label}`);
      this.next = undefined; //Goto节点不允许有next节点
    } else {
      throw '没有找到对应的Goto锚点';
    }
  }

  get walkLog(): boolean {
    return false;
  }

  fromJson(nctx: NodeContext, nid: string, nodes: any) {
    super.fromJson(nctx, nid, nodes);
    nctx.addGoto(this);
  }

  gc() {
    super.gc();
    //@ts-ignore
    this.anchor = undefined;
  }
}

class TryCatch extends Node {
  type = 'rpa.control.TryCatch';
  declare props: {
    tryNode: string; // try 要执行的代码
    catchNode: string; // 有异常时要执行的代码
  };
  tryNode?: Node;
  catchNode?: TryCatchCatcher;

  async exec(ctx: TaskContext): Promise<any> {
    if (ctx.destroyed) {
      return;
    }
    if (this.tryNode) {
      this.dog = new WalkDog(ctx.createTryCatchNodeTaskContext(this), this.tryNode);
      await this.dog.walk();
      this.execRet = this.dog?.popRet().val;
    }
  }

  fromJson(nctx: NodeContext, nid: string, nodes: any) {
    super.fromJson(nctx, nid, nodes);
    nctx.tiers.push(nid);
    if (this.props.tryNode) {
      let NodeClass = findNodeClass(this, this.props.tryNode, nodes);
      this.tryNode = new NodeClass();
      this.tryNode!.fromJson(nctx, this.props.tryNode, nodes);
    }
    this.catchNode = new TryCatchCatcher().setOwner(this);
    this.catchNode.catchNodeNid = this.props.catchNode;
    let catcherNid = nid + '_catcher';
    this.catchNode.fromJson(nctx, catcherNid, nodes);
    this.catchNode.timeout = this.timeout;
    this.catchNode.interval = this.interval;

    nctx.tiers.pop();
  }

  gc() {
    super.gc();
    this.tryNode = undefined;
    this.catchNode = undefined;
  }
}

class TryCatchCatcher extends Node {
  type = 'rpa.control.TryCatchCatcher';
  catchNodeNid?: string;
  catchNode!: Node;

  get walkLog(): boolean {
    return false;
  }

  async exec(ctx: TaskContext): Promise<any> {
    if (ctx.destroyed) {
      return;
    }
    this.dog = new WalkDog(ctx.createTryCatchCatcherTaskContext(this.nid), this.catchNode);
    await this.dog.walk();
    this.execRet = this.dog?.popRet().val;
  }

  fromJson(nctx: NodeContext, nid: string, nodes: any) {
    super.fromJson(nctx, nid, nodes);
    nctx.tiers.push(nid);
    if (this.catchNodeNid) {
      let NodeClass = findNodeClass(this, this.catchNodeNid, nodes);
      this.catchNode = new NodeClass();
      this.catchNode!.fromJson(nctx, this.catchNodeNid, nodes);
    } else {
      this.catchNode = new HiddenLog('没有找到异常处理节点').setOwner(this._owner || this);
      this.catchNode.tier = nctx.tier;
    }
    nctx.tiers.pop();
  }
}

//Assert断言，如果未满足指定的条件则抛出 message 异常
class Assert extends ConditionNode {
  type = 'rpa.control.Assert';
  declare props: {
    relation: string; // 多个条件之间的关系 and | or
    conditions: Array<any>;
    message?: string; //错误提示信息
  };
  conditions: Array<Condition> = [];

  getConditions(): Array<Condition> {
    return this.conditions;
  }

  async exec(ctx: TaskContext): Promise<any> {
    let and = this.props.relation !== 'or';
    let flag = await this.test(ctx, and);
    if (!flag) {
      let message = await ctx.evalParams(this.props.message);
      throw message || '给定的条件未满足';
    }
  }

  fromJson(nctx: NodeContext, nid: string, nodes: any) {
    super.fromJson(nctx, nid, nodes);

    //将属性还原成条件Obj
    this.props.relation = this.props.relation || 'and';
    for (let i = 0; i < this.props.conditions?.length; i++) {
      this.conditions.push(new Condition(this.props.conditions[i]));
    }
  }
}

/**
 * 暂停其它 WalkDog，直到当前 WalkDog 执行了 ResumeOthers 节点
 */
class SuspendedOthers extends Node {
  type = 'rpa.control.SuspendedOthers';

  async exec(ctx: TaskContext): Promise<any> {
    ctx.suspendLock.suspend(ctx);
  }
}

/**
 * 恢复其它 WalkDog 的执行
 */
class ResumeOthers extends Node {
  type = 'rpa.control.ResumeOthers';

  async exec(ctx: TaskContext): Promise<any> {
    ctx.suspendLock.resume(ctx);
  }
}

export const NodeTypes = {
  'rpa.control.If': If,
  'rpa.control.Exit': Exit,
  'rpa.control.ExitWholeTask': ExitWholeTask,
  'rpa.control.Manual': Manual,
  'rpa.control.InvokeSubFlow': InvokeSubFlow,
  'rpa.control.ListeningEvent': ListeningPageEvent, //@deprecated 不太好理解，新版本建议用户用 Timer 或者 ListeningAjax
  'rpa.control.Timer': Timer,
  'rpa.control.Ajax': Ajax,
  'rpa.control.Return': Return,
  'rpa.control.Anchor': Anchor,
  'rpa.control.Goto': Goto,
  'rpa.control.TryCatch': TryCatch,
  'rpa.control.Assert': Assert,
  'rpa.control.SuspendedOthers': SuspendedOthers,
  'rpa.control.ResumeOthers': ResumeOthers,
};
