import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>} from 'donkey-puppeteer-core';
import {evalParams, getSprintTab, noop, waitPromise, waitSeconds} from './utils';
import {SelectTab} from './nodes/index';
import {Node} from './nodes/base';
import {FakeAction} from '@e/rpa/fake_action';
import {RpaFlow} from '@e/rpa/rpa';
import {dispatchMsg} from '@e/utils/ipc';
import {getRpaFlowPreviewWindow} from '@e/utils/window';
import {RpaFiles} from '@e/rpa/files/index';
import moment from 'moment';
import {RequestAgent} from '@e/services/request';
import {RpaPlugin} from '@e/rpa/plugins/RpaPlugins';
import _ from 'lodash';
import {VMSandbox} from '@e/rpa/vm';
import {RpaDebugger} from '@e/rpa/rdb';
import {WalkDog} from '@e/rpa/dog';
import EventEmitter from 'events';
import {RpaSpirit} from '@e/rpa/spirit';
import {LogWatcher} from '@e/rpa/log.watcher';
import {PauseHolder} from '@e/rpa/common';
import {RpaShopInfo} from '@e/rpa/task/common';
import {AndroidDevice} from '@e/mobile/android/AndroidDevice';
import {rpaLogger} from '@e/services/logger';
import {MobileDevice} from "@e/mobile/MobileDevice";

export const js_mark = '#js>';

export declare type SnapshotType = 'Not' | 'Node' | 'OnFail';

export declare type FileType = 'Unknown' | 'Log' | 'Screenshot' | 'Record' | 'Data' | 'Upload';

export declare type LogLevel = 0 | 1 | 2; //debug | info | error

export interface Step {
  node: Node;
  dog: WalkDog;
  ret?: ReturnValue;
  childRet?: ReturnValue;
}

export interface ReturnValue {
  success: boolean;
  val?: any;
  error?: string; //错误消息
  node?: Record<string, any>;
}

export interface ParamProvider {
  (): any;
}

export interface RpaTaskListener {
  onBeforeExec?: (ctx: TaskContext, node: Node) => Promise<any>;
  onNodeSuccess?: (ctx: TaskContext, node: Node) => Promise<any>;
  onNodeFailed?: (ctx: TaskContext, node: Node) => Promise<any>;
  onAfterExec?: (ctx: TaskContext, node: Node, ret: ReturnValue) => Promise<any>;
  onFlowEnd?: (ctx: TaskContext) => Promise<any>;
}

export class LogsCache {
  _logs: Array<string> = [];

  get length() {
    return this._logs.length;
  }

  array() {
    return this._logs;
  }

  push(str: string) {
    this._logs.push(str);
  }

  clear() {
    this._logs = [];
  }
}

export class SystemConfigs {
  ignore_ERROR_PAGE_CRASHED: boolean = true; //是否忽略页面崩溃异常
}

class SuspendLock {
  private _lockThread?: string;
  private _lockPromise?: Promise<any>;
  private _lockPromiseResolve?: (data: any) => void;

  needWait(currentThread: string) {
    return !!this._lockPromise && this._lockThread !== currentThread;
  }

  suspend(ctx: TaskContext) {
    let currentThread: string = ctx.threadId;
    if (this._lockThread === currentThread) {
      return;
    }
    this._lockThread = currentThread;
    this._lockPromise = new Promise((resolve) => {
      this._lockPromiseResolve = resolve;
    });
    ctx.debug(`当前线程 ${currentThread} 暂停其它线程成功`);
  }

  resume(ctx: TaskContext) {
    let currentThread: string = ctx.threadId;
    if (this._lockThread !== currentThread) {
      return false;
    }
    ctx.debug(`当前线程 ${currentThread} 恢复其它线程成功`);
    this._lockThread = undefined;
    this._lockPromiseResolve && this._lockPromiseResolve(undefined);
    this._lockPromiseResolve = undefined;
    this._lockPromise = undefined;
    return true;
  }

  async promise() {
    return this._lockPromise;
  }
}

const RPA_Log_Format = '{datetime} {node.nid} {node.type} {node.name}';
let ctx_index_flag = 0;

abstract class AbstractContext {
  rpaType!: 'Browser' | 'Mobile' | 'IOS';

  //为了代码更清晰而抽取的父类，继承关系并不重要
  get parent(): TaskContext | undefined {
    return undefined;
  }

  abstract error(_msg: any, naked: boolean): Promise<any>;
}

abstract class BrowserContext extends AbstractContext {
  //为了代码更清晰，将浏览器相关的属性放在这里，继承关系并不重要
  _fakeAction!: FakeAction;
  get fakeAction(): FakeAction {
    return this.parent ? this.parent.fakeAction : this._fakeAction;
  }

  _browser?: Browser;
  get browser(): Browser {
    let browser = this.parent ? this.parent.browser : this._browser;
    if (!browser) {
      throw '找不到会话浏览器';
    }
    return browser;
  }
  _browserTitle!: string;
  get browserTitle(): string {
    return this.parent ? this.parent.browserTitle : this._browserTitle;
  }

  _spirit?: RpaSpirit;
  /**
   * @deprecated 请使用 await ctx.getSpirit()，有做重试。该函数弃用但暂不删除
   */
  get spirit(): RpaSpirit | undefined {
    let spirit = this.parent ? this.parent.spirit : this._spirit;
    if (!spirit) return spirit;
    let plugin = spirit.ws.getClientByIdentify('plugin:rpa');
    if (!plugin) {
      throw '无法找到会话浏览器，请确认其是否已被关闭';
    }
    return spirit!;
  }
  async getSpirit(): Promise<RpaSpirit | undefined> {
    let spirit = this.parent ? this.parent.spirit : this._spirit;
    if(spirit) {
      let retryCount = 0;
      while(true) {
        let plugin = spirit.ws.getClientByIdentify('plugin:rpa');
        if(plugin) {
          return spirit;
        } else {
          if(retryCount++ > 3) {
            throw '无法找到会话浏览器，请确认其是否已被关闭';
          }
          await waitSeconds(3, false);
        }
      }
    }
    return undefined;
  }

  /**
   * 是否为浏览器模拟手机
   */
  _mobile: boolean = false;
  get mobile(): boolean {
    return this.parent ? this.parent.mobile : this._mobile;
  }

  _headless: boolean = false;
  get headless(): boolean {
    return this.parent ? this.parent.headless : this._headless;
  }

  _page?: Page; //当前的tab页签
  get page(): Page {
    if (this.parent) {
      return this.parent.page;
    }
    if (!this._page || this._page.isClosed()) {
      throw new Error('未指定标签页或标签页已关闭');
    }
    return this._page;
  }
  set page(value: Page | undefined) {
    this.parent ? (this.parent.page = value) : (this._page = value);
  }

  _dialogs: Map<string, Dialog> = new Map();
  _dialogHandling?: string;
  get dialogHandling(): string | undefined {
    return this.parent ? this.parent.dialogHandling : this._dialogHandling;
  }
  /**
   * 尝试获取page的dialog(alert等)，
   * @param page
   */
  dialog(page: Page): Dialog | undefined {
    if (this.parent) {
      return this.parent.dialog(page);
    }
    //@ts-ignore
    let id = page.mainFrame()._id;
    let result = this._dialogs.get(id);
    this._dialogs.delete(id);
    //@ts-ignore
    if (result && !result._handled) {
      return result;
    }
  }
  onDialog(page: Page, dialog: Dialog) {
    try {
      //@ts-ignore
      let id = page.mainFrame()._id;
      this._dialogs.set(id, dialog);
      this.tryHandleDialog(page);
    } catch (ignore) {}
  }

  async tryHandleDialog(page?: Page) {
    let dialog = this.dialog(page ?? this.page);
    if (dialog) {
      try {
        if (dialog.type() === 'beforeunload') {
          await dialog.accept();
          return;
        }
        if ('ignore' === this.dialogHandling) {
          //do nothing
        } else if (this.dialogHandling) {
          await dialog.accept(this.dialogHandling);
        } else {
          await dialog.dismiss();
        }
      } catch (ignore) {}
    }
  }
}

abstract class MobileContext extends BrowserContext {
  //为了代码更清晰，将手机相关的属性放在这里，继承关系并不重要
  _driver?: WebdriverIO.Browser;
  get driver(): WebdriverIO.Browser {
    if(this.rpaType !== 'Mobile') {
      throw 'Only Android Supported!';
    }
    return (this.parent ? this.parent.driver : this._driver)!;
  }

  async createDriver(): Promise<WebdriverIO.Browser> {
    if(this.rpaType !== 'Mobile') {
      throw 'Only Android Supported!';
    }
    if (this.parent) {
      return this.parent.createDriver();
    }
    if (this._driver) {
      let connected = true;
      try {
        if (connected) {
          if (!(await this._driver.getDeviceTime())) {
            connected = false;
          }
        }
      } catch (ignore) {
        connected = false;
        this._driver?.deleteSession({ shutdownDriver: false }).catch(() => {});
      }
      if (connected) {
        return this._driver;
      }
    }
    try {
      const driver = await waitPromise((this.device as AndroidDevice).driver(), 180, '启动RPA Driver超时');
      driver.__create_time__ = Date.now();
      this._driver = driver;
      return this._driver!;
    } catch (e) {
      let message = new String(e).toString();
      if(message.indexOf('Failed to create session') != -1) {
        this.error(message, false);
        throw '连接RPA驱动失败，请按这个文档检查手机设置：https://www.szdamai.com/help/kol/mobileTroubleshooting';
      }
      throw e;
    }
  }

  _device?: MobileDevice;
  get device(): MobileDevice {
    return (this.parent ? this.parent.device : this._device)!;
  }
}

export class TaskContext extends MobileContext {
  _ctx_id_ = ctx_index_flag++;
  //是否已经调用了 destroy()
  destroyed = false;
  item: any;
  //当前context代码所在的层
  tier: string = 'root';
  //当前context执行的线程id
  threadId: string;

  nodeInterval = 1; //节点间执行默认间隔，单位秒
  nodeTimeout = 0; //单节点执行默认超时，单位秒，0表示不超时
  nodeSim = true; //拟人化操作
  snapshot!: SnapshotType;

  prev?: Step | undefined;
  current!: Step; //栈，每递归一个WalkDog都使用最后一个Step
  logs!: LogsCache;
  requestAgent: RequestAgent;

  params: Record<string, any> = {};
  private _sysParams: ParamProvider;
  protected _environments: ParamProvider;
  protected _userParams: ParamProvider;

  _rpaFiles!: RpaFiles;
  get rpaFiles(): RpaFiles {
    return this.parent ? this.parent.rpaFiles : this._rpaFiles;
  }
  _listener?: RpaTaskListener;
  get listener(): RpaTaskListener | undefined {
    return this.parent ? this.parent.listener : this._listener;
  }

  _plugins?: RpaPlugin[];
  get plugins(): RpaPlugin[] {
    return this.parent ? this.parent.plugins : this._plugins || [];
  }

  private _keepRun = true;
  private _keepRunWatchPromiseResolver!: (value: any) => void;
  _keepRunWatchPromise!: Promise<any>;
  get keepRunWatchPromise(): Promise<any> {
    return this.parent ? this.parent.keepRunWatchPromise : this._keepRunWatchPromise;
  }

  private _breakNode?: Node; //must be a Break
  get breakNode(): Node | undefined {
    return this._breakNode;
  }
  set breakNode(value: Node | undefined) {
    this._breakNode = value;
  }
  private _continueNode?: Node; //must be a Continue
  get continueNode(): Node | undefined {
    return this._continueNode;
  }
  set continueNode(value: Node | undefined) {
    this._continueNode = value;
  }
  returnNode?: Node; //意味着当前Dog碰到必须return停止执行的逻辑节点了
  meetReturn(returnNode: Node) {
    this.returnNode = returnNode;
  }
  anchorNode?: Node; //must be an anchor
  meetAnchor(anchor: Node) {
    this.anchorNode = anchor;
  }
  exitVal?: ReturnValue; //must be an exit，程序碰到Exit节点了
  meetExit(exitVal: ReturnValue) {
    this.exitVal = exitVal;
  }

  readonly preview: boolean;
  flow: RpaFlow;
  flowId: number; //ctx销毁之后,flow会被清空，但是异步的日志还没完成，还需要使用到flowId，所以直接将flowId保存起来

  _rdb?: RpaDebugger;
  get rdb(): undefined | RpaDebugger {
    return this.parent ? this.parent.rdb : this._rdb;
  }

  _suspendLock!: SuspendLock;
  get suspendLock(): SuspendLock {
    return this.parent ? this.parent.suspendLock : this._suspendLock;
  }

  // require 的缓存
  _script_cache: Map<string, string> = new Map<string, string>();
  get scriptCache(): Map<string, string> {
    return this.parent ? this.parent.scriptCache : this._script_cache;
  }

  _eventEmitter?: EventEmitter;
  get emitter(): EventEmitter {
    let ret = this.parent ? this.parent.emitter : this._eventEmitter;
    return ret!;
  }

  get isExtra(): boolean {
    return false;
  }

  _logWatcher?: LogWatcher;
  get logWatcher(): LogWatcher {
    return this.parent ? this.parent.logWatcher : this._logWatcher!;
  }

  _sys!: SystemConfigs;
  get sys(): SystemConfigs {
    return this.parent ? this.parent.sys : this._sys;
  }

  /**
   * 是否打印 正在执行... 执行成功 这种口水日志
   */
  get walkLog() {
    return true;
  }

  _pauseHolder?: PauseHolder;
  get paused(): boolean {
    let ctx: TaskContext = this;
    while (ctx.parent) {
      ctx = ctx.parent;
    }
    return ctx._pauseHolder?.isItemPaused(this.item.id) || false;
  }

  shopInfo?: RpaShopInfo;

  constructor({
    sysParams,
    environments,
    userParams,
    preview,
    flow,
    item,
    requestAgent,
  }: {
    sysParams: ParamProvider;
    environments: ParamProvider;
    userParams: ParamProvider;
    preview: boolean;
    flow: RpaFlow;
    item: any;
    requestAgent: RequestAgent;
  }) {
    super();
    this.threadId = _.uniqueId('thread_');
    this.item = item;
    this.preview = preview;
    this.flow = flow;
    this.rpaType = flow.rpaType;
    this.flowId = flow.id;
    this.requestAgent = requestAgent;
    this._sysParams = sysParams;
    this._environments = environments;
    this._userParams = userParams;
    let sps = sysParams();
    Object.assign(this.params, userParams(), sps);
    for (let k in sps) {
      //系统变量不允许被覆盖
      Object.defineProperty(this.params, k, {
        value: sps[k],
        writable: false,
      });
    }
  }

  initRoot() {
    //初始化一些只压根在root Context 才需要初始化的属性
    this._keepRunWatchPromise = new Promise((resolve, reject) => {
      this._keepRunWatchPromiseResolver = resolve;
    });
    this._dialogHandling = this.flow.config?.dialogHandling;
    this.logs = new LogsCache();
    this._suspendLock = new SuspendLock();
    this._sys = new SystemConfigs();
    this.initVM();
  }

  /**
   * 初始化nodejs执行沙箱
   * @protected
   */
  initVM() {
    this._vm = new VMSandbox(this, 99);
  }

  doDestroyCallbacks = new Map();
  doDestroy() {
    try {
      let subs = Array.from(this.doDestroyCallbacks.keys());
      for (let i = 0; i < subs.length; i++) {
        let callback = this.doDestroyCallbacks.get(subs[i]);
        if (callback) {
          try {
            callback();
          } catch (e) {}
        }
      }
      this.doDestroyCallbacks.clear();
      this.destroyed = true;
      if (this._vm) {
        this._vm.destroy();
        this._vm = undefined;
      }
      delete this.prev;
      this._page = undefined;
      this._browser = undefined;
      //@ts-ignore
      this._fakeAction = undefined;
      this.params = {};
      //@ts-ignore
      this._rpaFiles = undefined;
      //@ts-ignore
      this._keepRunWatchPromise = undefined;
      //@ts-ignore
      this._keepRunWatchPromiseResolver = undefined;
      //@ts-ignore
      this._sysParams = undefined;
      //@ts-ignore
      this._userParams = undefined;
      //@ts-ignore
      this._environments = undefined;
      //@ts-ignore
      this.requestAgent = undefined;
      //@ts-ignore
      this.flow = undefined;
      this._rdb = undefined;
      //@ts-ignore
      this.current = undefined;
      //@ts-ignore
      this._script_cache = undefined;
      this._device = undefined;
    } catch (err) {
      console.log('destroy context error', err);
    }
  }

  /**
   * 默认调用 doDestroy（真实销毁），但是循环节点子Context需要重用，在循环节点内部自行调用 doDestroy
   */
  destroy() {
    this.doDestroy();
  }

  private async _log(level: string, _msg: any, naked = false) {
    await this.nodeLog(naked ? null : this.current?.node, level, _msg);
  }

  async debug(_msg: any, naked = false) {
    await this._log('debug', _msg, naked);
  }

  async log(_msg: any, naked = false) {
    await this._log('log', _msg, naked);
  }

  async info(_msg: any, naked = false) {
    await this._log('info', _msg, naked);
  }

  async error(_msg: any, naked = false) {
    await this._log('err', _msg, naked);
  }

  async print(_msg: any, naked = false) {
    await this._log('print', _msg, naked);
  }

  async printError(_msg: any, naked = false) {
    await this._log('printError', _msg, naked);
  }

  async nodeLog(node: Node | null, level: string, _msg: any) {
    let logParams = {
      node: {
        id: node?.nid ?? 'undefined',
        nid: node?.nid ?? 'undefined',
        name: node?.name ?? 'undefined',
        type: node?.type ?? 'undefined',
      },
      datetime: moment().format('yyyy-MM-DD HH:mm:ss'),
    };
    await this._doLog(logParams, level, _msg);
  }

  protected async _doLog(logParams: any, level: string, _msg: any) {
    let msg =
      ((await evalParams(`${level} ${RPA_Log_Format} `, logParams)) || '').trim() + ' ' + _msg;
    if (msg.length > 512) {
      msg = msg.substring(0, 512) + '...';
    }
    this.logs.push(msg);
    this._dispatchLogToRenderProcess(msg);
    this._dispatchLogToBrowserRpaExt(msg);
    if (['print', 'printError'].includes(level)) {
      this.logWatcher?.emitLog(this.item.id, msg);
    }
  }

  _dispatchLogToRenderProcess(msg: string) {
    if (this.preview) {
      const win = getRpaFlowPreviewWindow(this.flowId);
      if (win) {
        dispatchMsg('rpa-flow-preview-log', { msg }, win);
      }
      // KOL某些手机流程以预览方式运行，需要将日志存在本地，方便排查
      if (this.rpaType !== 'Browser') {
        rpaLogger.log(msg);
      }
    }
  }

  _dispatchLogToBrowserRpaExt(msg: string) {
    try {
      if (this.item.taskId && this.spirit && !this.headless) {
        this.spirit.appendLog(msg);
      }
    } catch (e) {}
  }

  _memoryDb!: any;
  memoryDb(namespace: string): any {
    if (this.parent) {
      return this.parent.memoryDb(namespace);
    }
    return {
      get: () => this._memoryDb.get('rpaDb' + namespace).value(),
      write: (data: any) => this._memoryDb.set('rpaDb' + namespace, data).write(),
    };
  }

  currentSuccess(): boolean {
    let success = this.current?.ret?.success;
    if (typeof success == 'undefined') {
      success = true;
    }
    return success;
  }

  async evalParams(str: any, forceStr = false) {
    if (str && typeof str === 'string' && str.startsWith(js_mark)) {
      str = str.substr(js_mark.length);
      return this.runNodeScript(str);
    }
    return evalParams(str, this.params, forceStr);
  }

  get root(): TaskContext {
    return this.parent ? this.parent.root : this;
  }

  async onBeforeExec(node: Node) {
    if (this.listener && this.listener.onBeforeExec) {
      return await this.listener.onBeforeExec(this, node);
    }
  }
  async onNodeSuccess(node: Node) {
    if (this.listener && this.listener.onNodeSuccess) {
      return await this.listener.onNodeSuccess(this, node);
    }
  }
  async onNodeFailed(node: Node) {
    if (this.listener && this.listener.onNodeFailed) {
      return await this.listener.onNodeFailed(this, node);
    }
  }
  async onAfterExec(node: Node, ret: ReturnValue) {
    if (this.listener && this.listener.onAfterExec) {
      return await this.listener.onAfterExec(this, node, ret);
    }
  }

  async onFlowEnd() {
    if (this.listener && this.listener.onFlowEnd) {
      return await this.listener.onFlowEnd(this);
    }
  }

  get sysParams(): ParamProvider {
    return this._sysParams;
  }
  get environments(): ParamProvider {
    return this._environments;
  }
  get userParams(): ParamProvider {
    return this._userParams;
  }
  arguments(): any {
    return this.parent ? this.parent.arguments() : this.userParams();
  }
  get keepRun() {
    return this.parent ? this.parent.keepRun : this._keepRun;
  }
  set keepRun(value: boolean) {
    if (this.parent) {
      this.parent.keepRun = value;
    } else {
      this._keepRun = value;
      if (!this._keepRun) {
        !!this._keepRunWatchPromiseResolver && this._keepRunWatchPromiseResolver(0);
      }
    }
  }

  protected _vm?: VMSandbox;
  get vm(): VMSandbox {
    return this._vm ?? (this.parent ? this.parent.vm : this._vm!);
  }
  async runNodeScript(script?: string) {
    let max_seconds = (this.rpaType === 'Browser') ? 180 : 86400;
    if (script) {
      try {
        let result = this.vm!.run(this, script);
        if (result instanceof Promise) {
          result = await waitPromise(
            result,
            max_seconds,
            `执行超时：出于安全原因，脚本执行时间不允许超过${max_seconds}秒`,
          );
        }
        return result;
      } catch (err: any) {
        let message = err.message || String(err);
        if (message.indexOf('Script execution timed out') != -1) {
          throw `执行超时：出于安全原因，脚本执行时间不允许超过${max_seconds}秒`;
        }
        throw err;
      }
    }
  }

  async insurePage() {
    let retryCount = 0;
    let currentTime = Date.now();
    let timeoutTime = currentTime + (this.current?.node?.timeout || 10) * 1000;
    while (true) {
      try {
        let page;
        try {
          page = this.page;
          if (page) {
            let current = (await this.spirit?.run('rpa/tabs/current')) || {};
            let old = getSprintTab(page);
            if (
              old.id == current.id &&
              old.position == current.position &&
              old.url == current.url
            ) {
              return current;
            }
          }
        } catch (e: any) {
          if (e && e.message === '未指定标签页或标签页已关闭') {
          } else if (Date.now() > timeoutTime) {
            throw e;
          }
        }
        await new SelectTab({ tab: 0 }).setOwner(this.current?.node).exec(this);
        //@ts-ignore
        return getSprintTab(this.page);
      } catch (e: any) {
        let message = '未知异常';
        if (e) {
          message = e.message || e.toString() || '未知异常';
        }
        //如果遇到的是 most likely because of a navigation 异常，最多试3次
        if (++retryCount < 3 && message.indexOf('most likely because of a navigation') != -1) {
          this.log('似乎发生了重定向，正在重试');
          try {
            await this.page.waitForNavigation({ timeout: 5000 }).catch(noop);
          } catch (_) {}
          continue;
        } else {
          throw message;
        }
      }
    }
  }

  /**
   * 创建一个事件流程上下文
   * @param params
   */
  createEventTaskContext(tier: string, subParams: any): EventTaskContext {
    let result = new EventTaskContext(this, subParams);
    result.tier = tier;
    if (subParams) {
      let parentParams = this.params;
      //事件流程和父流程有同样的变量
      result.params = new Proxy(
        { ...subParams, ...parentParams },
        {
          get: function (target, prop) {
            if (subParams.hasOwnProperty(prop)) {
              return subParams[prop];
            }
            //@ts-ignore
            return parentParams[prop];
          },
          set: function (target, prop, value): boolean {
            if (subParams.hasOwnProperty(prop)) {
              subParams[prop] = value;
            } else {
              //@ts-ignore
              parentParams[prop] = value;
            }
            return true;
          },
        },
      );
    }
    return result;
  }

  /**
   * 创建一个子流程上下文
   * @param input
   */
  createSubFlowTaskContext(tier: string, subParams: any): SubFlowTaskContext {
    let result = new SubFlowTaskContext(this, subParams);
    result.tier = tier;
    return result;
  }

  /**
   * 创建一个Node分支上下文
   */
  createNodeTaskContext(tier: string): NodeTaskContext {
    let result = new NodeTaskContext(this);
    result.tier = tier;
    return result;
  }

  /**
   * 循环Node要用到的上下文，dog walk完后不销毁，而是重复利用
   * @param tier
   */
  createWhileNodeTaskContext(tier: string): WhileNodeTaskContext {
    let result = new WhileNodeTaskContext(this);
    result.tier = tier;
    return result;
  }

  /**
   * 创建TryCatch代码块Node分支上下文
   * @param tryCatch
   */
  createTryCatchNodeTaskContext(tryCatch: Node): NodeTaskContext {
    let result = new TryCatchNodeTaskContext(this, tryCatch);
    result.tier = tryCatch.nid;
    return result;
  }

  /**
   * 用来执行 catch 里的节点链
   * @param tier
   */
  createTryCatchCatcherTaskContext(tier: string): NodeTaskContext {
    //@ts-ignore
    let result = new TryCatchCatcherTaskContext(this);
    result.tier = tier;
    return result;
  }

  /**
   * 获取当前节点所在的 TryCatch 代码块节点
   */
  currentTryCatch(): Node | undefined {
    return undefined;
  }
}

//有些分支流程需要有自已的上下文，比如事件流程，子流程
//分支流程的上下文与父流程是相对独立的
abstract class ChildTaskContext extends TaskContext {
  ctx: TaskContext;

  protected constructor(ctx: TaskContext, userParams: ParamProvider) {
    super({
      sysParams: ctx.sysParams,
      environments: ctx.environments,
      userParams: userParams,
      preview: ctx.preview,
      flow: ctx.flow,
      item: ctx.item,
      requestAgent: ctx.requestAgent,
    });
    this.ctx = ctx;
    this.threadId = ctx.threadId;
    this.nodeTimeout = ctx.nodeTimeout;
    this.nodeInterval = ctx.nodeInterval;
    this.nodeSim = ctx.nodeSim;
    this.logs = ctx.logs;
    this.snapshot = ctx.snapshot;
    this.shopInfo = ctx.shopInfo;
    this.ctx.doDestroyCallbacks.set(this._ctx_id_, () => {
      this.doDestroy();
    });
  }

  doDestroy() {
    super.doDestroy();
    this.ctx?.doDestroyCallbacks?.delete(this._ctx_id_);
  }

  get parent(): TaskContext | undefined {
    return this.ctx;
  }

  currentTryCatch(): Node | undefined {
    if (this instanceof SubFlowTaskContext) {
      return undefined;
    }
    return this.ctx.currentTryCatch();
  }

  get isExtra(): boolean {
    return this.ctx?.isExtra;
  }

  meetExit(exitVal: ReturnValue) {
    this.exitVal = exitVal;
    this.ctx.meetExit(exitVal);
  }
}

abstract class SharedChildTaskContext extends ChildTaskContext {
  protected constructor(parent: TaskContext, userParams?: ParamProvider) {
    let subUserParams = userParams ? userParams() : {};
    let userParamsFun = () => {
      return { ...parent.userParams(), ...subUserParams };
    };
    super(parent, userParamsFun);
    this.useParentParams(parent);
  }

  useParentParams(parent: TaskContext) {
    let p = parent;
    while (p) {
      if (!p.parent || p instanceof SubFlowTaskContext) {
        this.params = p.params;
        break;
      }
      p = p.parent;
    }
  }

  get breakNode(): Node | undefined {
    return this.ctx.breakNode;
  }
  set breakNode(value: Node | undefined) {
    this.ctx.breakNode = value;
  }
  get continueNode(): Node | undefined {
    return this.ctx.continueNode;
  }
  set continueNode(value: Node | undefined) {
    this.ctx.continueNode = value;
  }
}

//子流程的上下文，不与parent共享变量，InvokeSubFlow调用子流程的ctx就是这种
export class SubFlowTaskContext extends ChildTaskContext {
  constructor(parent: TaskContext, subParams: any) {
    super(parent, () => subParams || {});
  }
}

//事件流程上下文，与parent共享变量
export class EventTaskContext extends SubFlowTaskContext {
  constructor(parent: TaskContext, subParams: any) {
    let subUserParams = subParams ?? {};
    super(parent, { ...parent.userParams(), ...subUserParams });
    this.threadId = _.uniqueId('thread_');
  }

  get walkLog() {
    return true;
  }
}

//节点分支上下文，与parent共享变量，如If,While的子节点
export class NodeTaskContext extends SharedChildTaskContext {
  constructor(parent: TaskContext) {
    super(parent);
  }

  meetReturn(returnNode: Node) {
    this.returnNode = returnNode;
    this.ctx.meetReturn(returnNode);
  }

  meetAnchor(anchor: Node) {
    if (anchor.tier == this.tier) {
      this.anchorNode = anchor;
    } else {
      this.returnNode = anchor;
      this.ctx.meetAnchor(anchor);
    }
  }
}

//循环节点要使用的上下文
export class WhileNodeTaskContext extends NodeTaskContext {
  constructor(parent: TaskContext) {
    super(parent);
  }

  destroy() {
    // do nothing here
    // WhileNodeTaskContext 需要调用 doDestroy() 才真正做销毁
  }
}

export class TryCatchNodeTaskContext extends NodeTaskContext {
  tryCatch: Node;

  constructor(parent: TaskContext, tryCatch: Node) {
    super(parent);
    this.tryCatch = tryCatch;
  }

  currentTryCatch(): Node | undefined {
    return this.tryCatch;
  }
}

export class TryCatchCatcherTaskContext extends NodeTaskContext {
  constructor(parent: TryCatchNodeTaskContext) {
    super(parent);
    if (!(parent instanceof TryCatchNodeTaskContext)) {
      throw '代码块错误，TryCatchCatcherTaskContext的parent必须为TryCatchNodeTaskContext';
    }
  }

  currentTryCatch(): Node | undefined {
    let tryCatchNodeTaskContext = this.parent as TryCatchNodeTaskContext;
    return tryCatchNodeTaskContext.ctx?.currentTryCatch();
  }
}

//准备流程和清理流程用到的 TaskContext
export class ExtraTaskContext extends TaskContext {
  type!: 'prerun' | 'postrun' | string;
  private originShopIds: number[] = [];
  shopIds: number[] = [];

  constructor(params: any) {
    super(params);
  }

  get isExtra(): boolean {
    return true;
  }

  arguments() {
    //准备子流程和清理子流程无法读取到流程启动输入变量
    return {};
  }

  setOriginShopIds(shopIds: number[]) {
    this.originShopIds = shopIds;
    this.shopIds = shopIds;
  }

  shopIdsChanged() {
    return !_.isEqual(this.originShopIds, this.shopIds);
  }
}
