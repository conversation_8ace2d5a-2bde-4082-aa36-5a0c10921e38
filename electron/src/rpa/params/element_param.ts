import { ClickOptions, ElementHandle } from 'donkey-puppeteer-core';
import { noop } from '@e/rpa/utils';
import { UnSerializableParam } from '@e/rpa/params/un_serializable';

export class ElementParam extends UnSerializableParam {
  selector: string;
  element: ElementHandle;
  iframe?: string;
  private _textContent?: string;

  constructor(selector: string, element: ElementHandle, iframe?: string) {
    super();
    this.selector = selector;
    this.element = element;
    this.iframe = iframe;
    //缓存文字以便在toString函数能够获取到
    this.textContent().then(noop);
  }

  async textContent() {
    if (typeof this._textContent === 'undefined') {
      //@ts-ignore
      this._textContent = (await this.element.evaluate((el) => el.value || el.textContent)) || '';
    }
    return this._textContent!;
  }

  async getAttribute(attr: string) {
    return this.element.evaluate((el, name) => el.getAttribute(name), attr);
  }

  async tagName() {
    return this.element.evaluate((el) => el.tagName);
  }

  async visible() {
    let isVisible = await this.element.evaluate((el) => {
      //@ts-ignore
      return el.checkVisibility({
        checkOpacity: true, // Check CSS opacity property too
        checkVisibilityCSS: true, // Check CSS visibility property too
      });
    });
    return isVisible;
  }

  /**
   * 检查当前节点是否处于可视状态
   */
  async isInView(): Promise<boolean> {
    let isInView = await this.element.evaluate((el) => {
      return new Promise(async (resolve, reject) => {
        const observer = new IntersectionObserver((entries) => {
          observer.disconnect();
          if (entries[0].isIntersecting) {
            resolve(true);
          } else {
            resolve(false);
          }
        });
        observer.observe(el);
      });
    });
    return isInView as boolean;
  }

  async click(options?: ClickOptions) {
    await this.element.click(options);
  }

  async hasClass(className: string) {
    let clazz = await this.getAttribute('class');
    return clazz?.split(/\s+/).indexOf(className) != -1;
  }

  /**
   * 查找单个子元素
   * @param expression 相对于当前元素的 selector 或者 xpath
   */
  async $(expression: string): Promise<ElementParam | undefined> {
    let el = null;
    try {
      // @ts-ignore
      el = await this.element.$(expression);
    } catch (_) {
      if (expression.charAt(0) == '/') {
        expression = '.' + expression;
      }
      let arr = await this.element.$x(expression);
      if (arr && arr.length > 0) {
        el = arr[0];
      }
    }
    if (el) {
      //@ts-ignore
      return new ElementParam(expression, el, this.iframe);
    }
    return undefined;
  }

  /**
   * 查找符合条件的所有子元素
   * @param expression
   */
  async $$(expression: string): Promise<Array<ElementParam>> {
    let all = [];
    try {
      all = await this.element.$$(expression);
    } catch (_) {
      all = await this.element.$x(expression);
    }
    let result: ElementParam[] = [];
    if (all && all.length > 0) {
      for (let el of all) {
        //@ts-ignore
        result.push(new ElementParam(expression, el, this.iframe));
      }
    }
    return result;
  }

  async parent(): Promise<ElementParam | undefined> {
    const parentHandle = await this.element.evaluateHandle((node) => node.parentNode);
    if (parentHandle) {
      //@ts-ignore
      return new ElementParam('', parentHandle, this.iframe);
    }
    return undefined;
  }

  serialize(): any {
    let fun = `()=>{return document.querySelector('${
      this.selector || '#__un_serializable_param__'
    }');}`;
    return {
      __un_serializable_param__: true,
      fun: fun,
    };
  }

  async displayAsString(): Promise<string> {
    return `[Object Element ${await this.tagName()} ${await this.textContent()}]`;
  }

  public toString = (): string => {
    return this._textContent || '';
  };

  async attr(paramName: string, asStr: boolean) {
    if (!paramName && !asStr) {
      //未指定属性名 并且 不要求强制返回string
      return this;
    } else {
      //返回一个string
      let tiers = paramName.split('.');
      let attr = null;
      if (tiers.length > 0) {
        attr = tiers[0];
        if ('value' == attr || 'textContent' == attr) {
          attr = null;
        }
      }
      let val;
      if (!attr) {
        val = await this.textContent();
      } else if (attr === 'tagName') {
        val = await this.tagName();
      } else {
        val = await this.getAttribute(attr);
      }
      return String(val);
    }
  }
}
