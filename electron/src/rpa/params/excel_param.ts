//@ts-ignore
import ExcelJS, {Cell, Column, Row, Workbook, Worksheet} from 'hy_excelize/excel';
import {UnSerializableParam} from '@e/rpa/params/un_serializable';
import {isNaN} from 'lodash';
import moment from 'moment';
import axios from 'axios';
import fs from 'fs-extra';
import {TaskContext} from '@e/rpa/context';
import {resolveApiUrl} from '@e/services/request';
import db from '@e/components/db';
import fsutil from "@e/rpa/fs.utils";
import Jimp from 'jimp';

const FormData = require('form-data');

interface ReadOption {
  readAsString: boolean;
  cellTrim: boolean;
};

const default_read_option: ReadOption = {
  readAsString: true,
  cellTrim: false
};

interface ExcelForEachEntry {
  label: any;
  data: any;
}

export class Excel extends UnSerializableParam {
  ctx: TaskContext;

  file!: string; //原来的保存路径，没有太多的用户
  tempFile?: string; //写入时的临时路径，为了避免重复创建临时文件

  //日期格式
  dateFormat = 'yyyy-MM-DD HH:mm:ss';

  workbook!: Workbook;

  //是否已经打开过文件
  private opened = false;

  constructor(ctx: TaskContext) {
    super();
    this.ctx = ctx;
  }

  /**
   * 打开一个文件，filePath
   * @param filePath 一定是一个本地文件，如果为空表示一个新的空白文件
   */
  async open(filePath?: string, password?: string) {
    if (this.opened) {
      throw '当前对象已经打开过文件';
    }
    this.opened = true;
    this.workbook = new ExcelJS.Workbook();
    if (filePath) {
      if (password) {
        try {
          await this.workbook.xlsx.readFile(filePath, {password});
        } catch (e) {
          //客户端解密失败，尝试上传到服务器端解密。不过一般不会失败
          let formdata = new FormData();
          let lock = await fsutil.lock(filePath);
          try {
            formdata.append('file', fs.readFileSync(filePath), { filename: 'excel.xlsx' });
          } finally {
            lock.release();
          }
          formdata.append('password', password);
          let headers: any = {
            'Content-Type': `multipart/form-data; boundary=${formdata.getBoundary()}`,
            Authorization: db.getJwt() ?? '',
            Cookie: db.getCookies().join(';') ?? '',
            'x-dm-team-id': this.ctx.item.teamId,
          };
          if (this.ctx.requestAgent.getProps().userId) {
            headers['x-dm-user-id'] = this.ctx.requestAgent.getProps().userId;
          }
          const response = await axios.request({
            url: resolveApiUrl('/api/excel/decrypt'),
            method: 'POST',
            responseType: 'arraybuffer',
            headers: headers,
            data: formdata.getBuffer(),
          });
          await this.workbook.xlsx.load(response.data);
        }
      } else {
        await this.workbook.xlsx.readFile(filePath);
      }
    } else {
      //表示打开一个空白excel表
      this.workbook.addWorksheet();
    }
  }

  async writeFile(filePath: string) {
    await this.workbook.xlsx.writeFile(filePath);
    let saveTwice = true;
    for(let i = 1; i <= this.sheetCount(); i++) {
      if(this.rowCount(i) > 10000) {
        saveTwice = false; //大文件不保存两次
        break;
      }
    }
    if(saveTwice) {
      //excel框架有个bug，对特定的excel文件只一次写入后office会提示损坏
      await this.workbook.xlsx.readFile(filePath);
      await this.workbook.xlsx.writeFile(filePath);
    }
  }

  /**
   * 获取当前excel有几个sheet表
   */
  sheetCount(): number {
    return this.workbook.worksheets.length;
  }

  /**
   * 获取当前excel的所有sheet的名称
   */
  sheetNames(): string[] {
    return this.workbook.worksheets.map((sheet: Worksheet) => sheet.name);
  }

  /**
   * 获取总共行数(从1到最后一个有效行)
   * @param sheet
   */
  rowCount(sheet: number | string): number {
    return this.doGetRowCount(this.worksheet(sheet));
  }
  private doGetRowCount(worksheet: Worksheet) {
    let ret = worksheet.actualRowCount;
    while (ret > 0 && !worksheet.getRow(ret).hasValues) {
      ret--;
    }
    return ret;
  }

  /**
   * 获取总列数(从1到最后一个有效列)
   * @param sheet
   */
  colCount(sheet: number | string): number {
    return this.doGetColCount(this.worksheet(sheet));
  }
  private doGetColCount(worksheet: Worksheet) {
    let valuesCount = worksheet.actualColumnCount;
    let colsCount = 0, colsCount2 = 0;
    for(let i = 0; colsCount2 < valuesCount && i < 16384; i++) {
      let column = worksheet.getColumn(i+1);
      if(column?.values?.length > 0) {
        colsCount2++;
      }
      colsCount++;
    }
    return colsCount;
  }

  /**
   * 获取第一个有效行的行号 1-based
   * @param sheet
   * @return 如果没有可用行则返回0
   */
  firstRowNum(sheet: number | string): number {
    return this.doGetFirstRowNum(this.worksheet(sheet));
  }
  private doGetFirstRowNum(worksheet: Worksheet): number {
    let row = 1;
    let endRow = this.doGetRowCount(worksheet);
    for(; row <= endRow; row++) {
      if(worksheet.getRow(row).hasValues) {
        return row;
      }
    }
    return 0;
  }

  /**
   * 获取第一个有效列的列号 1-based
   * @param sheet
   * @return 如果没有可用列则返回0
   */
  firstColNum(sheet: number | string): number {
    return this.doGetFirstColNum(this.worksheet(sheet));
  }
  private doGetFirstColNum(worksheet: Worksheet): number {
    let col = 1;
    let endCol = this.doGetColCount(worksheet);
    for(; col <= endCol; col++) {
      if(worksheet.getColumn(col).values?.length > 0) {
        return col;
      }
    }
    return 0;
  }

  /**
   * 往excel写入数据
   * @param sheet
   * @param writeType
   * @param row
   * @param endRow
   * @param col
   * @param endCol
   * @param content
   */
  writeData(
    sheet: number | string,
    writeType: string, //'cell' | 'row' | 'col' | 'table'
    row: number,
    endRow: number,
    col: number | string,
    endCol: number,
    content: any,
  ) {
    switch (writeType) {
      case 'cell':
        return this.writeCell(sheet, row, col, content);
      case 'row':
        return this.writeRow(sheet, row, content);
      case 'col':
        return this.writeCol(sheet, col, content);
      case 'table':
        return this.writeTable(sheet, row, endRow, col, endCol, content);
    }
  }

  /**
   * 更改一个单元格
   * @param sheet
   * @param row
   * @param col
   * @param content
   */
  writeCell(sheet: number | string, row: number, col: number | string, content: any) {
    this.doWriteCell(this.worksheet(sheet), row, col, content);
  }
  private doWriteCell(worksheet: Worksheet, row: number, col: number | string, content: any) {
    row = this.comfortRow(worksheet, row)!;
    col = this.comfortCol(worksheet, col)!;
    this.comfortValue(worksheet.getCell(row, col), content);
  }

  /**
   * 写入整行内容
   * @param sheet
   * @param row
   * @param content
   */
  writeRow(sheet: number | string, row: number, content: number) {
    this.doWriteRow(this.worksheet(sheet), row, content);
  }
  private doWriteRow(worksheet: Worksheet, row: number, content: any) {
    row = this.comfortRow(worksheet, row)!;
    if (Array.isArray(content)) {
      for (let i = 0; i < content.length; i++) {
        this.comfortValue(worksheet.getCell(row, i + 1), content[i]);
      }
    } else {
      this.comfortValue(worksheet.getCell(row, 1), content);
    }
  }

  /**
   * 写入整列内容
   * @param sheet
   * @param col
   * @param content
   */
  writeCol(sheet: number | string, col: number | string, content: any) {
    this.doWriteCol(this.worksheet(sheet), col, content);
  }
  private doWriteCol(worksheet: Worksheet, col: number | string, content: any) {
    col = this.comfortCol(worksheet, col)!;
    if (Array.isArray(content)) {
      for (let i = 0; i < content.length; i++) {
        this.comfortValue(worksheet.getCell(i + 1, col), content[i]);
      }
    } else {
      this.comfortValue(worksheet.getCell(1, col), content);
    }
  }

  /**
   * 写入区域
   * @param sheet
   * @param row
   * @param endRow
   * @param col
   * @param endCol
   * @param content
   */
  writeTable(
    sheet: number | string,
    row: number,
    endRow: number,
    col: number | string,
    endCol: number | string,
    content: any,
  ) {
    this.doWriteTable(this.worksheet(sheet), row, endRow, col, endCol, content);
  }
  private doWriteTable(
    worksheet: Worksheet,
    row: number,
    endRow: number,
    col: number | string,
    endCol: number | string,
    content: any,
  ) {
    row = this.comfortRow(worksheet, row)!;
    endRow = this.comfortRow(worksheet, endRow)!;
    col = this.comfortCol(worksheet, col)!;
    endCol = this.comfortCol(worksheet, endCol)!;
    let rs = Math.min(row, endRow),
      re = Math.max(row, endRow);
    let cs = Math.min(col, endCol),
      ce = Math.max(col, endCol);
    for (let r = rs, i = 0; r <= re; r++, i++) {
      let rowData = Array.isArray(content) ? content[i] : content;
      if (typeof rowData != 'undefined' && rowData != null) {
        for (let c = cs, j = 0; c <= ce; c++, j++) {
          let value = Array.isArray(rowData) ? rowData[j] : rowData;
          this.comfortValue(worksheet.getCell(r, c), value);
        }
      }
    }
  }

  /**
   * 读取excel里的数据
   * @param sheet
   * @param readType 'cell' | 'row' | 'col' | 'table'
   */
  readData(
    sheet: number | string,
    readType: string,
    row: number,
    col: number | string,
    count?: number,
    endRow?: number,
    endCol?: number | string,
    option?: ReadOption,
  ) {
    switch (readType) {
      case 'cell':
        return this.readCell(sheet, ~~row, col, option);
      case 'row':
        return this.readRow(sheet, ~~row, ~~count!, option);
      case 'col':
        return this.readCol(sheet, col, ~~count!, option);
      case 'table':
        return this.readTable(sheet, ~~row, ~~endRow!, col, endCol!, option);
      case 'all':
        return this.readAll(sheet, option);
    }
  }

  readDataForLoop(
    sheet: number | string,
    readType: string,
    row: number,
    col_: number | string,
    endRow: number,
    endCol_: number | string,
    option: ReadOption
  ): ExcelForEachEntry[] {
    let temp = [row, endRow].sort();
    row = temp[0], endRow = temp[1];
    let worksheet = this.worksheet(sheet);
    let result: ExcelForEachEntry[] = [];
    let col, endCol;
    switch (readType) {
      case 'row':
        for(; row <= endRow; row++) {
          result.push({label: row, data: this.doReadRow(worksheet, row, 1, option)});
        }
        break;
      case 'col':
        temp = [this.comfortCol(worksheet, col_), this.comfortCol(worksheet, endCol_)].sort();
        col = temp[0], endCol = temp[1];
        for(; col <= endCol; col++) {
          result.push({label: worksheet.getColumn(col).letter, data: this.doReadCol(worksheet, col, 1, option)});
        }
        break;
      case 'all':
        row = this.firstDataRow(worksheet);
        endRow = this.doGetRowCount(worksheet);
        col_ = this.firstDataCol(worksheet);
        endCol_ = this.doGetColCount(worksheet);
        // 这里没有 break
      case 'table':
        temp = [this.comfortCol(worksheet, col_), this.comfortCol(worksheet, endCol_)].sort();
        col = temp[0], endCol = temp[1];
        for(; row <= endRow; row++) {
          result.push({label: row, data: this.doReadTable(worksheet, row, row, col, endCol, option)[0]});
        }
        break;
    }
    return result;
  }

  /**
   * 读取单元格
   * @param sheet
   * @param row
   * @param col
   */
  readCell(sheet: number | string, row: number, col: number | string, option?: ReadOption) {
    return this.doReadCell(this.worksheet(sheet), row, col, option);
  }
  private doReadCell(worksheet: Worksheet, row: number, col: number | string, option?: ReadOption) {
    row = this.comfortRow(worksheet, row)!;
    col = this.comfortCol(worksheet, col)!;
    let cell = worksheet.getCell(row, col);
    return this.stringifyCell(cell, option);
  }

  private fetchMediaImage(worksheet: Worksheet, row: number, col: number) {
    const cell = worksheet.getCell(row, col);
    const cellValue = cell.value;
    let mediaImage: any;
    //@ts-ignore
    if(cellValue && cellValue.formula && /DISPIMG\("(ID_[^"]+)"/.test(cellValue.formula)) {
      let imageId = RegExp.$1;
      mediaImage = worksheet.workbook.model.media.find((m: any) => m.cellImage === imageId);
    } else {
      const images = worksheet.getImages();
      let image: any;
      for(let i = 0; i < images.length; i++) {
        const range = images[i].range;
        if(row > range.tl.nativeRow && row <= (range.br.nativeRow+1) && col > range.tl.nativeCol && col <= (range.br.nativeCol+1)) {
          image = images[i];
          break;
        }
      }
      if(image) {
        mediaImage = worksheet.workbook.model.media.find((m: any) => m.index === image.imageId);
      }
    }
    return mediaImage;
  }
  readImage(sheet: number | string, row: number, col: number | string) {
    const worksheet = this.worksheet(sheet);
    row = this.comfortRow(worksheet, row)!;
    col = this.comfortCol(worksheet, col)!;
    let mediaImage = this.fetchMediaImage(worksheet, row, col);
    return mediaImage;
  }

  async insertImage(sheet: number | string, row: number, col: number | string, imagePath: string) {
    let worksheet = this.worksheet(sheet);
    row = this.comfortRow(worksheet, row)!;
    col = this.comfortCol(worksheet, col)!;
    let jimpImg = await Jimp.read(imagePath);
    let buffer = await jimpImg.getBufferAsync(Jimp.MIME_PNG);
    let cell = worksheet.getCell(row, col);
    let area = this.getMergeArea(cell);
    const imageId = worksheet.workbook.addImage({
      buffer: buffer,
      extension: 'png',
    });
    worksheet.addImage(imageId, {
      //@ts-ignore
      tl: { row: area.tl.row - 1, col: area.tl.col - 1 },
      br: area.br,
      editAs: 'oneCell'
    });
  }

  /**
   * 以wps格式插入一张图片
   */
  async insertCellImage(sheet: number | string, row: number, col: number | string, imagePath: string) {
    let worksheet = this.worksheet(sheet);
    row = this.comfortRow(worksheet, row)!;
    col = this.comfortCol(worksheet, col)!;
    let jimpImg = await Jimp.read(imagePath);
    let buffer = await jimpImg.getBufferAsync(Jimp.MIME_PNG);
    //@ts-ignore
    const imageId = worksheet.workbook.addCellImage({
      buffer: buffer,
      extension: 'png',
    });
    let cell = worksheet.getCell(row, col);
    cell.value = {
      formula: `_xlfn.DISPIMG("${imageId}",1)`,
      result: `=DISPIMG("${imageId}",1)`,
    };
  }

  /**
   * 整行读取数据
   * @param sheet
   * @param row
   * @param count
   */
  readRow(sheet: number | string, row: number, count: number, option?: ReadOption) {
    return this.doReadRow(this.worksheet(sheet), row, count, option);
  }
  private doReadRow(worksheet: Worksheet, row: number, count: number = 1, option?: ReadOption) {
    const colCount = this.doGetColCount(worksheet);
    row = this.comfortRow(worksheet, row)!;
    count = ~~count;
    if (count <= 0) {
      throw `illegality count ${count}`;
    }
    let rowValues = (r: Row) => {
      let ret = [];
      if (r) {
        for (let i = 1; i <= r.cellCount && i <= colCount; i++) {
          let cell = r.getCell(i);
          ret.push(this.stringifyCell(cell, option));
        }
      }
      return ret;
    };
    if (count == 1) {
      let ret = rowValues(worksheet.getRow(row));
      while (ret.length && (ret[ret.length - 1] === null || ret[ret.length - 1] === undefined)) {
        ret.pop();
      }
      return ret;
    } else {
      let ret = [];
      for (let i = 0; i < count; i++) {
        ret.push(rowValues(worksheet.getRow(row + i)));
      }
      return ret;
    }
  }

  /**
   * 整列读取数据
   * @param sheet
   * @param col
   * @param count
   */
  readCol(sheet: number | string, col: number | string, count: number, option?: ReadOption) {
    return this.doReadCol(this.worksheet(sheet), col, count, option);
  }
  private doReadCol(worksheet: Worksheet, col: number | string, count: number = 1, option?: ReadOption) {
    col = this.comfortCol(worksheet, col)!;
    count = ~~count;
    if (count <= 0) {
      throw `illegality count ${count}`;
    }
    let colValues = (c: Column) => {
      let ret: any[] = [];
      if (c) {
        c.eachCell((cell: Cell, rowNumber: number) => {
          ret[rowNumber - 1] = this.stringifyCell(cell, option);
        });
      }
      return ret;
    };
    if (count == 1) {
      return colValues(worksheet.getColumn(col));
    } else {
      let ret = [];
      for (let i = 0; i < count; i++) {
        ret.push(colValues(worksheet.getColumn(col + i)));
      }
      return ret;
    }
  }

  readTable(
    sheet: number | string,
    row: number,
    endRow: number,
    col: number | string,
    endCol: number | string, option?: ReadOption
  ) {
    return this.doReadTable(this.worksheet(sheet), row, endRow, col, endCol, option);
  }
  private doReadTable(
    worksheet: Worksheet,
    row: number,
    endRow: number,
    col: number | string,
    endCol: number | string, option?: ReadOption
  ) {
    row = this.comfortRow(worksheet, row)!;
    endRow = this.comfortRow(worksheet, endRow)!;
    col = this.comfortCol(worksheet, col)!;
    endCol = this.comfortCol(worksheet, endCol)!;
    let rs = Math.min(row, endRow),
      re = Math.max(row, endRow);
    let cs = Math.min(col, endCol),
      ce = Math.max(col, endCol);
    let ret = [];
    for (let i = rs; i <= re; i++) {
      let excelRow = worksheet.getRow(i);
      let retRow = [];
      for (let j = cs; j <= ce; j++) {
        let cell = excelRow.getCell(j);
        retRow.push(this.stringifyCell(cell, option));
      }
      ret.push(retRow);
    }
    return ret;
  }

  readAll(sheet: number | string, option?: ReadOption) {
    let worksheet = this.worksheet(sheet);
    let endRow = this.doGetRowCount(worksheet);
    let endCol = this.doGetColCount(worksheet);
    if(endRow <= 0 || endCol <= 0) {
      return [];
    }
    let row = this.firstDataRow(worksheet), col = this.firstDataCol(worksheet);
    return this.doReadTable(worksheet, row, endRow, col, endCol, option);
  }

  private firstDataRow(worksheet: Worksheet) {
    let row = 1;
    let endRow = this.doGetRowCount(worksheet);
    for(; row <= endRow; row++) {
      if(worksheet.getRow(row).hasValues) {
        break;
      }
    }
    return row;
  }

  private firstDataCol(worksheet: Worksheet) {
    let col = 1;
    let endCol = this.doGetColCount(worksheet);
    for(; col <= endCol; col++) {
      if(worksheet.getColumn(col).values?.length > 0) {
        break;
      }
    }
    return col;
  }

  /**
   * 删除excel的行
   * @param sheet
   * @param row
   * @param count
   */
  delRows(sheet: number | string, row: number, count: number) {
    return this.doDelRows(this.worksheet(sheet), row, count);
  }
  private doDelRows(worksheet: Worksheet, row: number, count: number = 1) {
    row = this.comfortRow(worksheet, row)!;
    count = ~~count;
    if (count <= 0) {
      throw `illegality count ${count}`;
    }
    worksheet.spliceRows(row, count);
  }

  /**
   * 插入行
   * @param sheet
   * @param row
   * @param count
   */
  insertRows(sheet: number | string, row: number, count: number) {
    return this.doInsertRows(this.worksheet(sheet), row, count);
  }
  private doInsertRows(worksheet: Worksheet, row: number, count: number = 1) {
    row = this.comfortRow(worksheet, row)!;
    count = ~~count;
    if (count <= 0) {
      throw `illegality count ${count}`;
    }
    worksheet.insertRows(row, Array(count).fill([]));
  }

  /**
   * 删除excel的列
   * @param sheet
   * @param col
   * @param count
   */
  delCols(sheet: number | string, col: number | string, count: number) {
    return this.doDelCols(this.worksheet(sheet), col, count);
  }
  private doDelCols(worksheet: Worksheet, col: number | string, count: number) {
    col = this.comfortCol(worksheet, col)!;
    count = ~~count;
    if (count <= 0) {
      throw `illegality count ${count}`;
    }
    worksheet.spliceColumns(col, count);
  }

  insertCols(sheet: number | string, col: number | string, count: number) {
    return this.doInsertCols(this.worksheet(sheet), col, count);
  }
  private doInsertCols(worksheet: Worksheet, col: number | string, count: number) {
    col = this.comfortCol(worksheet, col)!;
    count = ~~count;
    if (count <= 0) {
      throw `illegality count ${count}`;
    }
    col = this.comfortCol(worksheet, col)!;
    count = ~~count;
    if (count <= 0) {
      throw `illegality count ${count}`;
    }
    for (let i = 0; i < count; i++) {
      worksheet.spliceColumns(col, 0, []);
    }
  }

  async textContent() {
    return this.toString();
  }

  attr(paramName: string, asStr: boolean): any {
    if (!paramName && !asStr) {
      //未指定属性名 并且 不要求强制返回string
      return this;
    } else {
      let temp = String(paramName).toUpperCase();
      if (/(([^\.]+)\.)?([A-Z]+)(\d+)/.test(temp)) {
        let sheet = RegExp.$2 || 1;
        let row = ~~RegExp.$4,
          col = RegExp.$3;
        return this.doReadCell(this.worksheet(sheet), row, col);
      }
      let arr = paramName.split('.');
      let sheet: number | string = '';
      let cmd = '';
      if (arr.length == 1) {
        sheet = 1;
        cmd = arr[0];
      } else if (arr.length == 2) {
        sheet = arr[0];
        cmd = arr[1];
      }
      switch (cmd) {
        case 'rowCount':
          return this.rowCount(sheet);
        case 'colCount':
          return this.colCount(sheet);
        case 'firstRowNum':
          return this.firstRowNum(sheet);
        case 'firstColNum':
          return this.firstColNum(sheet);
      }
    }
  }

  /**
   * 通过第 row 行的表头来获取列名
   * @param sheet
   * @param header
   * @param row
   */
  colNameByHeader(sheet: number | string, header: string, row: number = 1) {
    let worksheet = this.worksheet(sheet);
    row = this.comfortRow(worksheet, row)!;
    let excelRow = worksheet.getRow(row);
    for (let i = 1; i <= excelRow.cellCount; i++) {
      let cell = excelRow.getCell(i);
      if (this.stringifyCell(cell) == header) {
        return worksheet.getColumn(cell.col).letter;
      }
    }
    throw `未找到列${header}`;
  }

  serialize(): any {
    return {
      __un_serializable_param__: true,
      fun: '()=>{return {};}',
    };
  }

  public toString = (): string => {
    return `[Object Excel ${this.file}]`;
  };

  //下面都是工具方法
  private worksheet(sheet: number | string): Worksheet {
    if (sheet == null || typeof sheet === 'undefined') {
      sheet = 1;
    }
    if (typeof sheet === 'string') {
      sheet = sheet.trim() || '1';
      if (/^\d+$/.test(sheet)) {
        //@ts-ignore
        sheet = ~~sheet;
      }
    }
    let worksheet =
      typeof sheet === 'number'
        ? this.workbook.worksheets[sheet - 1]
        : this.workbook.getWorksheet(sheet);
    if (!worksheet) {
      throw `工作表${sheet}不存在`;
    }
    return worksheet;
  }

  private comfortCol(worksheet: Worksheet, col?: number | string): number {
    let _col = col;
    if (typeof col === 'string') {
      col = col.trim().toUpperCase();
      if (/^\d+$/.test(col)) {
        //字符串转成数字
        //@ts-ignore
        col = ~~col;
      } else {
        try {
          col = worksheet.getColumn(col).number;
        } catch (e) {
          if((''+e).indexOf('Invalid column letter') != -1) {
            throw `列名 ${_col} 不正确`;
          }
        }
      }
    }
    if (typeof col === 'number' && col < 0) {
      col = worksheet.actualColumnCount + col + 1;
    }
    let ret = Number(col);
    if (isNaN(ret)) {
      throw `列名 ${_col} 不正确`;
    }
    return ret;
  }

  private comfortRow(worksheet: Worksheet, row?: number): number {
    if (typeof row === 'string') {
      if (/^\d+$/.test(row)) {
        //字符串转成数字
        //@ts-ignore
        row = ~~row;
      }
    }
    if (typeof row === 'number' && row < 0) {
      row = worksheet.actualRowCount + row + 1;
    }
    let ret = Number(row);
    if (isNaN(ret)) {
      throw '行号不正确';
    }
    return ret;
  }

  private comfortValue(cell: Cell, value: any) {
    if (typeof value != 'undefined' && value != null) {
      if (value instanceof Date) {
        value = moment(value).format(this.dateFormat);
      } else if (typeof value == 'object') {
        value = String(value);
      }
      cell.value = value;
    } else {
      cell.value = undefined;
    }
  }

  private stringifyCell(cell: Cell, option?: ReadOption) {
    option = option || default_read_option;
    if (cell) {
      //@ts-ignore
      if(this.fetchMediaImage(cell.worksheet, cell.row, cell.col)) {
        return {type: 'image'};
      }
      let result = cell.result || cell.value;
      if (result) {
        //@ts-ignore
        if (result.hyperlink) {
          //@ts-ignore
          result = result.text || result.hyperlink;
        }
        if (result instanceof Date) {
          if(!!option.readAsString) {
            result = moment(result).format(this.dateFormat);
          }
        } else if (typeof result == 'object') {
          //@ts-ignore
          if (result.richText) {
            //@ts-ignore
            result = result.richText.map((r) => r.text).reduce((a, b) => a + b);
          } else {
            //@ts-ignore
            result = result.text || result.result || result.error;
          }
        }
      }
      if(result && !!option.cellTrim && typeof result === 'string') {
        result = result.trim();
      }
      return result;
    }
    return null;
  }

  private getMergeArea(cell: Cell): any {
    if(cell.isMerged) {
      const worksheet = cell.worksheet;
      const master = cell.master;
      let tl = {row: master.row, col: master.col};
      let rr = master.row, rc = master.col;
      for(let i: any = master.row + 1; ; i++) {
        let c = worksheet.getCell(i, master.col);
        if(!c.isMerged || c.master.address !== master.address) {
          break;
        }
        rr = i;
      }
      for(let i: any = master.col + 1; ; i++) {
        let c = worksheet.getCell(master.row, i);
        if(!c.isMerged || c.master.address !== master.address) {
          break;
        }
        rc = i;
      }
      return {
        tl, br: {row: rr, col: rc}
      };
    } else {
      return {
        tl: {row: cell.row, col: cell.col},
        br: {row: cell.row, col: cell.col}
      }
    }
  }
}
