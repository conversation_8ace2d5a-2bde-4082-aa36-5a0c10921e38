import { RpaFlow } from '@e/rpa/rpa';
import { Node } from '@e/rpa/nodes/base';
import { getRpaFlowPreviewWindow } from '@e/utils/window';
import { dispatchMsg } from '@e/utils/ipc';
import { TaskContext } from '@e/rpa/context';
import _ from 'lodash';
import { UnSerializableParam } from '@e/rpa/params/un_serializable';

export interface Debugger {
  getBreakpoints: () => Map<string, any>;
  setBreakpoints: (breakpoints: Map<string, any>) => void;
  addBreakpoint: (nid: string, expression: any) => void;
  removeBreakpoint: (nid: string) => void;
  /**
   * 切换是否忽略所有断点
   * @param skipAll
   */
  setSkipAll: (skipAll: boolean) => void;
  /**
   * 继续执行
   */
  resume: () => void;
  /**
   * 执行到下一步
   */
  stepNext: () => void;
  /**
   * 获取当前参数值
   * @param key 参数名，如果不填表示获取所有参数
   */
  getParams: (key?: string) => any;
  /**
   * 执行脚本
   * @param script
   */
  runNodeScript: (script: string) => any;
}

class BreakPint {
  ctx: TaskContext;
  node: Node;
  resolve!: (data: any) => void;
  reject!: (err: any) => void;
  promise!: Promise<any>;

  constructor(ctx: TaskContext, node: Node) {
    this.ctx = ctx;
    this.node = node;
  }

  createPromise() {
    this.promise = new Promise((resolve, reject) => {
      this.resolve = resolve;
      this.reject = reject;
    });
    return this;
  }
}

/**
 * rpa调试器
 */
export class RpaDebugger implements Debugger {
  //可以进调试的流程ids
  debugFlowIds: string[] = [];
  //断点列表
  breakpoints: Map<string, any> = new Map();
  //是否忽略所有断点
  skipAll: boolean = false;
  pauseOnNext: boolean = false;

  //当前暂停的节点
  breakPoint?: BreakPint;
  //为了防止有事件子流程时点击下一步乱跳，事件子流程等待的不是breakPoint，而是nextBreakPoint
  nextBreakPoint?: BreakPint;

  readonly flow: RpaFlow;

  constructor(flow: RpaFlow) {
    this.flow = flow;
  }

  getBreakpoints(): Map<string, any> {
    return this.breakpoints;
  }

  setBreakpoints(breakpoints: Map<string, any>) {
    this.breakpoints = breakpoints;
  }

  setDebugFlowIds(debugFlowIds: string[]) {
    this.debugFlowIds = debugFlowIds || [];
  }

  setSkipAll(skipAll: boolean) {
    this.skipAll = skipAll;
  }

  //添加一个断点
  addBreakpoint(nid: string, expression: any) {
    this.breakpoints.set(nid, expression);
  }

  //删除一个断点
  removeBreakpoint(nid: string) {
    this.breakpoints.delete(nid);
  }

  resume() {
    this.breakPoint?.resolve!(1);
    this.breakPoint = undefined;
    this.nextBreakPoint?.resolve!(1);
    this.nextBreakPoint = undefined;
  }

  stepNext() {
    if (this.breakPoint) {
      this.pauseOnNext = true;
      this.breakPoint.resolve!(1);
      this.breakPoint = undefined;
    }
  }

  private lastNodeEmitTime: number = 0;
  private lastNodeEmitTimer: any;
  async meetNode(ctx: TaskContext, node: Node) {
    if (this.breakPoint) {
      //如果当前已经断住了，等待当前断点执行完成
      await this.nextBreakPoint!.promise;
    } else if (this.nextBreakPoint) {
      if (ctx.threadId != this.nextBreakPoint.ctx.threadId) {
        await this.nextBreakPoint!.promise;
      }
    }
    if (!this.debugFlowIds.includes(node.fid || 'flow id not found')) {
      clearTimeout(this.lastNodeEmitTimer);
      if (Date.now() - this.lastNodeEmitTime > 500) {
        this.notifyCurrentRunNode(node);
      } else {
        //避免下个节点如果是等待节点，会一起不高亮的问题
        this.lastNodeEmitTimer = setTimeout(() => {
          this.notifyCurrentRunNode(node);
        }, 500);
      }
      return;
    }
    if (this.pauseOnNext) {
      this.pauseOnNext = false;
      return this.pauseOnNode(ctx, node);
    } else if (!this.skipAll && this.breakpoints.has(node.nid)) {
      return this.pauseOnNode(ctx, node);
    }
  }

  private pauseOnNode(ctx: TaskContext, node: Node): Promise<any> {
    this.breakPoint = new BreakPint(ctx, node);
    this.breakPoint.promise = new Promise((resolve, reject) => {
      this.breakPoint!.resolve = resolve;
      this.breakPoint!.reject = reject;
      this.notifyMeetBreakpoint(node);
    });
    if (!this.nextBreakPoint) {
      //如果nextBreakPoint存在，说明这次断点是通过stepNext触发的
      this.nextBreakPoint = new BreakPint(ctx, node);
      this.nextBreakPoint.createPromise();
    }
    return this.breakPoint.promise;
  }

  notifyMeetBreakpoint(node: Node) {
    const win = getRpaFlowPreviewWindow(this.flow.id);
    if (win) {
      dispatchMsg('rpa-debugger-breakpoint', { nid: node.nid, fid: node.fid }, win);
    }
  }

  private notifyCurrentRunNode(node: Node) {
    const win = getRpaFlowPreviewWindow(this.flow.id);
    if (win) {
      this.lastNodeEmitTime = Date.now();
      dispatchMsg('rpa-current-node', { nid: node.nid, fid: node.fid }, win);
    }
  }

  async getParams(key?: string) {
    let params = {};
    if (this.breakPoint) {
      let keys = key ? [key] : Object.keys(this.breakPoint.ctx.params);
      for (let key of keys) {
        // @ts-ignore
        let val = this.breakPoint.ctx.params[key];
        if (val) {
          if (val instanceof UnSerializableParam) {
            val = await (val as UnSerializableParam).displayAsString();
          } else if (val instanceof Buffer) {
            val = `Buffer<${val.length}>`;
          }
        }
        // @ts-ignore
        params[key] = val;
      }
    }
    return params;
  }

  async runNodeScript(script: string): Promise<any> {
    if (this.breakPoint) {
      return await this.breakPoint.ctx.runNodeScript(script);
    }
  }
}
