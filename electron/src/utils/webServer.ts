import Koa from 'koa';
import Router from '@koa/router';
import staticServer from 'koa-static';
import bodyParser from 'koa-bodyparser';
import { AddressInfo } from 'net';
import { Server } from 'http';
import fs from 'fs';
import path from 'path';
import _ from 'lodash';
import moment from 'moment';
import { app as electronApp } from 'electron';

import type { ShopInfo } from '../types';
import type { FingerprintDetail } from './fingerprint';
import FingerprintConfig, { IP } from './fingerprint';
import db from '@e/components/db';
import { rawRequest, RequestAgent } from '../services/request';
import logger from '@e/services/logger';
import { MessageDispatcher } from '@e/components/messageDispatcher';
import { dispatchMsg } from '@e/utils/ipc';
import { ActionBlockWriteModal } from '@e/typings';
import { TunnelRouter } from '@e/utils/tunnelRouter';
import type { RecordControllerOwner } from '@e/recorder/controller';
import { shell } from 'electron';
import { isWin7Platform, resolveUrl } from '@e/utils/utils';
import { getHyBrowser, getMainWindow, openMainWindow, openRunTaskModal } from '@e/utils/window';
import { localWebServerIns } from '@e/components/localWebServer';
import { runAiAgent, stopPreview } from '@e/rpa/task_schedule';
import appConfig from '@e/configs/app';
import { ChannelTunnel } from '@e/utils/tunnels/ChannelTunnel';
import { netProbe } from '@e/utils/ipc/ping';
import i18n from '@e/utils/i18n';

interface Props {
  sessionId: number;
  rpaFlowId?: number;
  rpaTaskId?: number;
  rpaPreview?: boolean;
  showMouseTrack?: boolean;
  tunnelRouter: TunnelRouter;
  recorderController: RecordControllerOwner;
  wsDispatcher: MessageDispatcher;
  shopInfo: ShopInfo;
  fingerprintDetail: FingerprintDetail;
  ipDetail?: API.TeamIpVo;
  fpToOverride?: IP;
  fingerprintConfig: FingerprintConfig;
  accountLoginUrl: string;
  paymentPlatformInfo?: API.ShopPlatformVo;
  mailPlatformInfo?: API.ShopPlatformVo;
  clientIpLocation: API.IpLocationDto;
  actionBlockWriteModal: ActionBlockWriteModal;
  extensionCount: number;
  transitList: API.TransitWithLocationVo[];
  requestAgent: RequestAgent;
  browserLanguage: string;
}

function getGroupLabel(epVo?: API.TunnelEndpointVo) {
  if (epVo?.tunnelType === 'direct') {
    return i18n.t('直连');
  }
  if (epVo?.tunnelType === 'localFrontend') {
    return i18n.t('用户自己的加速通道');
  }
  return epVo?.groupName ?? '--';
}

/**
 * web server
 */
export default class WebServer {
  private props: Props;
  port: number;
  server?: Server;
  private createTime: Date;
  domainWhitelist: string[];
  blockPageList: string[];
  blockElementList: string[];
  outboundIp?: string;

  constructor(props: Props) {
    this.props = props;
    this.port = 0;
    this.createTime = new Date();
    this.domainWhitelist = [];
    // 云端功能屏蔽列表
    this.blockPageList = [];
    this.blockElementList = [];
    this.outboundIp = this.props.fpToOverride?.address || this.props.ipDetail?.ip;
  }

  loadWhitelist() {
    return this.props.requestAgent
      .request(`/api/meta/dwl`, {
        teamId: this.props.shopInfo.teamId,
        params: {
          scope: ['IpGo', 'IpProxy', 'UserExclusiveIp', ''].includes(
            this.props.ipDetail?.goodsType ?? '',
          )
            ? 'UserIp'
            : 'Team',
          ipId: this.props.ipDetail?.id ?? '',
        },
      })
      .then((domainWhitelistDto = []) => {
        this.domainWhitelist = domainWhitelistDto.map(
          (dto: API.DomainWhitelistItem) => dto.domain ?? '',
        );
      })
      .catch((err) => {
        logger.error('[BROWSER] fetch domainWhitelist error', err);
      });
  }

  init() {
    const {
      requestAgent,
      shopInfo,
      ipDetail,
      fpToOverride,
      fingerprintDetail,
      accountLoginUrl,
      mailPlatformInfo,
      paymentPlatformInfo,
    } = this.props;
    return new Promise(async (resolve, reject) => {
      const app = new Koa();
      app.use(bodyParser());
      const router = new Router();

      let userInfo: any = {};
      try {
        userInfo = await requestAgent.request('/api/account/curUser');
      } catch (e) {
        logger.error('[API] get current user info failed', e);
      }
      // ip探测服务
      let ipCheckers: API.RemoteIpProviderConfig[] = [{ provider: 'huayoung', isDefault: true }];
      // 当前用户功能权限列表
      let grantedFunctionCodes: string[] = [];
      // 用户角色信息
      let roleInfo: any = {};
      let creditConfig: any = {};
      let networkConfig: any = {};
      let transitGroupVos: API.TransitGroupVo[] = [];
      let probeUrl: string | undefined;
      const transitGroupVosMap: Record<number, API.TransitGroupVo[]> = {};
      const promiseList = [];
      promiseList.push(
        requestAgent
          .request('/api/meta/ip/checkers', {
            teamId: shopInfo.teamId,
            params: { ipId: ipDetail?.id, remoteIp: this.props.tunnelRouter.getLastRemoteIp() },
          })
          .then((res) => {
            ipCheckers = res.checkers;
            probeUrl = res.probeUrl;
          }),
      );
      promiseList.push(
        requestAgent
          .request(`/api/user/${userInfo.id}/grantedFunctions`, {
            teamId: shopInfo.teamId,
          })
          .then((res) => {
            grantedFunctionCodes = res.filter((f: any) => !!f).map((f: API.FunctionVo) => f.id);
          }),
      );
      promiseList.push(
        requestAgent
          .request(`/api/account/currentRole`, {
            teamId: shopInfo.teamId,
          })
          .then((res) => {
            roleInfo = res;
          })
          .catch((e) => {
            logger.error('[API] get user role info failed', e);
          }),
      );
      promiseList.push(
        requestAgent
          .request('/api/goods/credit/config', {
            teamId: shopInfo.teamId,
          })
          .then((res) => {
            creditConfig = res;
          }),
      );
      promiseList.push(
        requestAgent
          .request('/api/shop/session/networkConfig', {
            teamId: shopInfo.teamId,
          })
          .then((res) => {
            networkConfig = res;
          }),
      );
      shopInfo.channels?.forEach((channel) => {
        if (channel.ipId) {
          promiseList.push(
            requestAgent
              .request(`/api/transit/groups?ipId=${channel.ipId}`, { teamId: shopInfo.teamId })
              .then((res) => {
                transitGroupVosMap[channel.ipId!] = res;
              }),
          );
        }
      });
      promiseList.push(this.loadWhitelist());
      if (shopInfo.securityPolicyEnabled) {
        promiseList.push(
          requestAgent
            .request(`/api/shop/policies/${shopInfo.id}/shopBlockElements`, {
              teamId: shopInfo.teamId,
            })
            .then((blockElmVos: API.BlockElementVo[] = []) => {
              _.forEach(blockElmVos, (vo) => {
                if (vo.enabled) {
                  if (vo.wholePage) {
                    this.blockPageList.push(vo.url ?? '');
                  } else if (vo.element) {
                    this.blockElementList.push(vo.element);
                  }
                }
              });
            }),
        );
      }
      await Promise.allSettled(promiseList);

      app.use(async (ctx, next) => {
        try {
          await next();
          const status = ctx.status || 404;
          if (status === 404) {
            ctx.throw(404);
          }
        } catch (err: any) {
          ctx.status = err.status || 500;
          if (ctx.status === 404) {
            ctx.redirect('/');
          } else {
            ctx.body = '服务器错误，请尝试重启应用程序后重试';
          }
        }
      });

      router.get('/', async (ctx) => {
        if (process.env.OEM_NAME === 'gg') {
          ctx.response.redirect('/newTab');
          return;
        }
        const content = await readFileThunk(path.resolve(__dirname, '../browserPage/index.html'));
        const json = {
          sessionId: this.props.sessionId,
          rpaTaskId: this.props.rpaTaskId,
          shopInfo,
          browserEnv: this.props.fingerprintConfig.getEnv(),
          channelList: this.props.tunnelRouter.getChannelList(),
          routerRules: this.props.tunnelRouter
            .getRouterRules()
            ?.filter((r) => r.ruleType !== 'Default'),
          accountLoginUrl: accountLoginUrl,
          paymentPlatformInfo: paymentPlatformInfo,
          mailPlatformInfo: mailPlatformInfo,
          createTime: this.createTime.toString(),
          clientIpLocation: this.props.clientIpLocation,
          grantedFunctionCodes,
          extensionCount: this.props.extensionCount,
          userInfo,
          transitList: this.props.transitList,
          transitGroupVosMap,
          creditConfig,
          ipCheckers,
          'huayoung-language': this.props.browserLanguage,
        };
        const str = stringifyData(json);
        ctx.body = content.replace('SERVER_DATA', str);
      });

      router.get('/accessDeny', async (ctx) => {
        const content = await readFileThunk(
          path.resolve(__dirname, '../browserPage/accessDeny/index.html'),
        );
        const str = stringifyData({
          shopInfo,
          userInfo,
          'huayoung-language': this.props.browserLanguage,
        });
        ctx.body = content.replace('SERVER_DATA', str);
      });

      router.get('/newTab', async (ctx) => {
        const content = await readFileThunk(
          path.resolve(__dirname, '../browserPage/newTab/index.html'),
        );
        const str = stringifyData({ shopInfo, 'huayoung-language': this.props.browserLanguage });
        ctx.body = content.replace('SERVER_DATA', str);
      });

      router.get('/creditAlert', async (ctx) => {
        const content = await readFileThunk(
          path.resolve(__dirname, '../browserPage/creditAlert/index.html'),
        );
        const str = stringifyData({
          shopInfo,
          userInfo,
          creditConfig,
          'huayoung-language': this.props.browserLanguage,
        });
        ctx.body = content.replace('SERVER_DATA', str);
      });

      router.get('/checking', async (ctx) => {
        let fileName = 'browserChecking.html';
        if (process.env.OEM_NAME === 'gg') {
          fileName = 'browserChecking_gg.html';
        }
        const content = await readFileThunk(
          path.resolve(__dirname, 'html', fileName),
        );
        const json = {
          channelList: this.props.tunnelRouter.getChannelList(),
          transitList: this.props.transitList,
          ipCheckers,
          'huayoung-language': this.props.browserLanguage,
        };
        const str = stringifyData(json);
        ctx.body = content.replace('SERVER_DATA', str);
      });

      // for debug purpose, 'fingerprint' base64 encode
      router.get('/ZmluZ2VycHJpbnQ=', (ctx) => {
        ctx.body = JSON.stringify(
          {
            args: this.props.fingerprintConfig.getArgs(),
            env: this.props.fingerprintConfig.getEnv(),
            data: fingerprintDetail,
          } as any,
          null,
          ' ',
        );
      });

      const sessionStartTime = new Date().getTime();
      router.get('/api/getEnv', (ctx) => {
        ctx.response.body = {
          clientApiOrigin: `http://localhost:${localWebServerIns.getPort()}`,
          wsUrl: `ws://127.0.0.1:${this.props.wsDispatcher.getPort()}`,
          apiUrl: db.getApiUrl(),
          serverUrl: appConfig.isOEM
            ? `http://${electronApp.getName().toLowerCase()}.local/`
            : 'http://szdamai.local/',
          userInfo,
          grantedFunctionCodes,
          sessionId: this.props.sessionId,
          shopInfo: shopInfo,
          sscToken: this.props.requestAgent.getProps().token,
          fingerprint: fingerprintDetail,
          hasAdminAuth: !shopInfo.parentShopId && ['superadmin', 'boss'].includes(roleInfo.code),
          recordInfo: {
            sessionId: this.props.sessionId,
            wsUrl: this.props.recorderController?.getWSUrl(),
            sessionStartTime: sessionStartTime,
          },
          networkConfig,
          rpaTaskId: this.props.rpaTaskId,
          rpaFlowId: this.props.rpaFlowId,
          rpaPreview: this.props.rpaPreview,
          showMouseTrack: this.props.showMouseTrack,
          language: this.props.browserLanguage,
          deviceId: db.getDeviceIdFromCookies(),
          outboundIp: this.outboundIp,
          createTime: moment(this.createTime).format('YYYY-MM-DD HH:mm:ss'),
          isWin7: isWin7Platform(),
        };
      });
      router.get('/api/getFingerprint', (ctx) => {
        ctx.response.body = fingerprintDetail;
      });
      router.get('/api/getActionBlockPages', (ctx) => {
        ctx.response.body = this.blockPageList;
      });
      router.get('/api/getActionBlockRules', (ctx) => {
        ctx.response.body = this.blockElementList;
      });
      router.get('/api/isActionBlockReadOnly', (ctx) => {
        ctx.response.body = this.props.actionBlockWriteModal === 'None';
      });
      // 获取当前分身安全策略的开启状态
      router.get('/api/isSecurityPolicyEnabled', (ctx) => {
        ctx.response.body = shopInfo.securityPolicyEnabled;
      });
      // 当前用户能否禁用ActionBlock
      router.get('/api/getActionBlockAuth', (ctx) => {
        if (shopInfo.parentShopId) {
          ctx.response.body = -1;
        } else if (!['superadmin', 'boss'].includes(roleInfo.code)) {
          ctx.response.body = 0;
        } else {
          ctx.response.body = 1;
        }
      });
      router.get('/api/getLanguage', (ctx) => {
        ctx.response.body = {
          language: this.props.browserLanguage,
        };
      });
      router.post('/api/addActionBlockRule', (ctx) => {
        const rule = ctx.request.body as any;
        if (rule.wholePage) {
          // 更新缓存
          this.blockPageList.push(rule.url);
          this.props.wsDispatcher.dispatcher(JSON.stringify({ action: 'block-page-list-changed' }));
        }
        if (this.props.actionBlockWriteModal === 'Team') {
          // 团队功能屏蔽
          return requestAgent.request('/api/team/settings/shopPolicies/createBlockElement', {
            teamId: shopInfo.teamId,
            method: 'POST',
            data: rule,
          });
        } else {
          // 账号功能屏蔽
          dispatchMsg('create-block-element-rule', {
            shopId: shopInfo.id,
            rule,
          });
        }
        ctx.response.body = { success: true };
      });
      router.get('/api/switchTransit', async (ctx) => {
        const params = ctx.request.query;
        try {
          // @ts-ignore
          if (params.auto) {
            await this.props.tunnelRouter.autoSwitchPrimaryChannelTransit(true);
          } else {
            await this.props.tunnelRouter.switchChannelTransit(Number(params.channelId), {
              tunnelType: params.tunnelType as string,
              groupId: Number(params.groupId),
              nodeId: params.nodeId ? Number(params.nodeId) : undefined,
              proxyId: params.proxyId ? Number(params.proxyId) : undefined,
            });
          }
          ctx.response.body = { success: true };
        } catch (e: any) {
          ctx.response.body = { success: false, message: e.message };
        }
      });
      router.get('/api/autoSwitchTransit', async (ctx) => {
        const autoSwitchProp = this.props.tunnelRouter.getAutoChangeTransitProp();
        if (!autoSwitchProp.disabled && autoSwitchProp.checked) {
          await this.props.tunnelRouter.autoSwitchPrimaryChannelTransit();
        }
        ctx.response.body = { success: true };
      });
      router.get('/api/getSessionChannelTokenVo', async (ctx) => {
        const { refresh } = ctx.request.query;
        const channel = this.props.tunnelRouter.getDefaultChannel() as ChannelTunnel;
        if (refresh) {
          await channel.refreshToken();
        }
        ctx.response.body = {
          success: true,
          sessionChannelTokenVo: channel.getSessionChannelTokenVo(),
        };
      });
      router.post('/api/netProbe', async (ctx) => {
        const { token, proxyConfig, endpointVo } = ctx.request.body;
        try {
          const res = await netProbe({
            ...endpointVo,
            repeatTimes: 1,
            targetUrl: probeUrl,
            proxyConfig,
            clashProxy: ['clash', 'localFrontend'].includes(endpointVo.tunnelType)
              ? {
                  url: endpointVo.endpoint,
                }
              : undefined,
            transitProbe: ['transit', 'jump'].includes(endpointVo.tunnelType)
              ? {
                  token,
                  endpoint: endpointVo.endpoint,
                }
              : undefined,
          });
          ctx.response.body = {
            success: true,
            ...res,
          };
        } catch (err: any) {
          ctx.response.body = {
            success: false,
            status: 'Failed',
            error: err.message,
          };
        }
      });
      router.get('/api/refreshLoginDeviceTransitPing', async (ctx) => {
        const params = ctx.request.query;
        try {
          const loginDeviceTransitDtoList =
            await this.props.tunnelRouter.refreshLoginDeviceTransitPing(Number(params.channelId));
          if (loginDeviceTransitDtoList) {
            ctx.response.body = { success: true, data: loginDeviceTransitDtoList };
          } else {
            ctx.response.body = { success: false };
          }
        } catch (e) {
          ctx.response.body = { success: false };
        }
      });
      router.get('/api/refreshIpTransitPing', async (ctx) => {
        const params = ctx.request.query;
        try {
          const ipTransitDtoList = await this.props.tunnelRouter.refreshIpTransitPing(
            Number(params.channelId),
          );
          if (ipTransitDtoList) {
            ctx.response.body = { success: true, data: ipTransitDtoList };
          } else {
            ctx.response.body = { success: false };
          }
        } catch (e) {
          ctx.response.body = { success: false };
        }
      });
      router.get('/api/getPacScript', async (ctx) => {
        try {
          ctx.response.body = { success: true, pacScript: this.props.tunnelRouter.getPacScript() };
        } catch (err: any) {
          ctx.response.body = { success: false };
        }
      });
      router.get('/api/getWhitelist', async (ctx) => {
        const params = ctx.request.query;
        if (params.reload) {
          await this.loadWhitelist();
          this.props.wsDispatcher.dispatcher(JSON.stringify({ action: 'white-list-changed' }));
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
        ctx.response.body = { success: true, whitelist: this.domainWhitelist };
      });
      router.get('/api/openCustomServiceUrl', async (ctx) => {
        shell.openExternal('https://work.weixin.qq.com/kfid/kfc6f5523b90f3e402d');
        ctx.response.body = { success: true };
      });
      router.post('/api/openAppUrl', async (ctx) => {
        const { body } = ctx.request;
        const { url = '' } = body as any;
        const targetUrl = resolveUrl(db.getPortalUrl(), url);
        openMainWindow(targetUrl);
        ctx.response.body = { success: true };
      });
      router.get('/api/openExternalUrl', async (ctx) => {
        const params = ctx.request.query;
        const { url = '' } = params as any;
        try {
          const urlObj = new URL(url);
          urlObj.searchParams.delete('__openInExternalBrowser');
          shell.openExternal(urlObj.toString());
        } catch (e) {}
        ctx.response.body = { success: true };
      });
      router.get('/api/reloadUserInfo', async (ctx) => {
        userInfo = await requestAgent.request('/api/account/curUser');
        ctx.response.body = { success: true, userInfo };
      });
      router.get('/api/openUserCenterModal', async (ctx) => {
        const mainWin = getMainWindow();
        if (mainWin) {
          mainWin.show();
          mainWin.webContents.send('open-user-center');
        }
        ctx.response.body = { success: true };
      });
      router.get('/api/reportOutboundIp', async (ctx) => {
        const params = ctx.request.query;
        if (params.ip) {
          this.outboundIp = params.ip as string;
          try {
            await this.props.tunnelRouter.updateRemoteIp(params.ip as string);
          } catch (e) {}
          this.props.wsDispatcher.dispatcher(
            JSON.stringify({ action: 'outbound-ip-changed', data: this.outboundIp }),
          );
        }
        ctx.response.body = { success: true };
      });
      router.get('/api/restartBrowser', async (ctx) => {
        const params = ctx.request.query;
        const hyBrowser = getHyBrowser(shopInfo.id!);
        if (hyBrowser) {
          hyBrowser.restart({
            isIppIp: params.isIppIp === 'true',
            cleanCookieAndStorage: params.cleanCookieAndStorage === 'true',
          });
        }
      });
      router.get('/api/runRpaTask', async (ctx) => {
        const params = ctx.request.query;
        if (params.flowId) {
          openRunTaskModal({
            teamId: shopInfo.teamId!,
            shopId: shopInfo.id!,
            bizCode: String(params?.bizCode ?? ''),
            rpaFlowId: Number(params.flowId as string),
            sscToken: this.props.requestAgent.getProps().token,
          });
        }
        ctx.response.body = { success: true };
      });
      router.get('/api/clearMouseTrack', () => {
        this.props.wsDispatcher.dispatcher(
          JSON.stringify({
            action: 'clear-hy-rpa-user-action-layer',
          }),
        );
      });
      router.get('/api/showMouseTrack', () => {
        this.props.wsDispatcher.dispatcher(
          JSON.stringify({
            action: 'show-hy-rpa-user-action-layer',
          }),
        );
      });
      router.get('/api/hideMouseTrack', () => {
        this.props.wsDispatcher.dispatcher(
          JSON.stringify({
            action: 'hide-hy-rpa-user-action-layer',
          }),
        );
      });
      router.get('/api/uploadBrowserData', async (ctx) => {
        const hyBrowser = getHyBrowser(this.props.shopInfo.id!);
        if (hyBrowser) {
          await hyBrowser.uploadBrowserData();
        }
        ctx.response.body = { success: true };
      });
      router.get('/api/clearBrowserData', async (ctx) => {
        this.props.wsDispatcher.dispatcher(
          JSON.stringify({
            action: 'clear-browser-data',
          }),
        );
        ctx.response.body = { success: true };
      });
      router.post('/api/aiAgent/prompt', async (ctx) => {
        const { body } = ctx.request;
        const { prompt = '', pluginJsUrl } = body as any;
        try {
          const res = await runAiAgent(
            shopInfo.teamId!,
            'Browser',
            shopInfo.id!,
            prompt,
            pluginJsUrl,
          );
          ctx.response.body = {
            success: true,
            data: res,
          };
        } catch (e: any) {
          ctx.response.body = {
            success: false,
            message: e.message,
          };
        }
      });
      router.get('/api/aiAgent/stopTask', async (ctx) => {
        stopPreview(shopInfo.id!);
        ctx.response.body = { success: true };
      });
      router.post('/api/pingUrlByPrimaryChannel', async (ctx) => {
        try {
          const { body } = ctx.request;
          const { path = '', timeout = 10 * 1000 } = body as any;

          // 参数验证
          if (!path || typeof path !== 'string') {
            ctx.response.body = {
              status: 200,
              success: false,
              message: '缺少有效的请求路径',
            };
            return;
          }

          const channel = this.props.tunnelRouter
            .getDefaultChannel() as ChannelTunnel;

          if (channel) {
            const impl = channel.getImpl();
            const result = await impl.request(path, timeout);
            ctx.response.body = {
              status: 200,
              success: true,
              data: result,
            };
          } else {
            ctx.response.body = {
              status: 200,
              success: false,
              message: '未绑定IP，无法发送请求',
            };
          }
        } catch (error: any) {
          ctx.response.body = {
            status: 200,
            success: false,
            message: error.message || '请求失败',
          };
        }
      });
      router.post('/donkeyRequest', async (ctx) => {
        const { body } = ctx.request;
        const { path = '', options = {} } = body as any;
        try {
          const resData = await requestAgent.request(path, {
            teamId: shopInfo.teamId,
            ...options,
          });
          ctx.response.body = {
            status: 200,
            success: true,
            data: resData,
          };
        } catch (err: any) {
          ctx.response.body = {
            status: 200,
            success: false,
            message: err.message,
          };
        }
      });
      router.post('/rawRequest', async (ctx) => {
        const { body } = ctx.request;
        const { url = '', options = {} } = body as any;
        try {
          const resData = await rawRequest(url, {
            ...options,
          });
          ctx.response.body = {
            status: 200,
            success: true,
            data: resData,
          };
        } catch (err: any) {
          ctx.response.body = {
            status: 200,
            success: false,
            message: err.message,
          };
        }
      });
      router.post('/geolocation/v1/geolocate', async (ctx) => {
        // 勿删！！！
        //花漾浏览器定位时会调用这个接口，具体格式见：https://developers.google.com/maps/documentation/geolocation/requests-geolocation?hl=zh-cn
        // 这个接口响应的数据结构供内核参考，具体数据会使用环境变量中的值
        ctx.response.body = {
          location: {
            lat: 37.4241876,
            lng: -122.0917381,
          },
          accuracy: 32.839,
        };
      });
      router.get('/neterror', async (ctx) => {
        ctx.type = 'html';
        const lastRemoteIp = this.props.tunnelRouter.getLastRemoteIp();
        const currentEndpointVo = this.props.tunnelRouter
          .getDefaultChannel()
          ?.getCurrentEndpointVo();
        const remoteIp = lastRemoteIp || this.props.ipDetail?.ip;
        const nodeId = currentEndpointVo?.proxyId || currentEndpointVo?.nodeId;
        ctx.response.body = `
<html>
<head>
<style>
body {
  overflow: hidden;
  margin: 0;
  background-color: transparent;
  font-size: 14px;
  color: rgb(95, 99, 104);
}
.row-label {
  display: inline-block;
  width: 70px;
}
</style>
</head>
<body>
${remoteIp ? `<p><span class="row-label">出口IP</span>: ${remoteIp}</p>` : ''}
${
  currentEndpointVo?.groupId
    ? `<p><span class="row-label">加速通道</span>: ${getGroupLabel(currentEndpointVo)}</p>`
    : ''
}
${nodeId ? `<p><span class="row-label">链路ID</span>: ${nodeId}</p>` : ''}
<script>
// 在 iframe 内容变化时发送高度信息
setTimeout(() => {
  window.parent.postMessage({
    type: 'resize',
    height: document.documentElement.scrollHeight
  }, '*');
}, 100);
</script>
</body>
</html>
        `;
      });

      app.use(router.routes());

      app.use(
        staticServer(path.resolve(__dirname, '../browserPage/'), {
          extensions: ['css', 'js', 'png', 'jpg', 'ico', 'woff2', 'ttf', 'woff', 'svg'],
        }),
      );
      try {
        const server = app.listen(0, () => {
          const address = server.address() as AddressInfo;
          logger.verbose('[BROWSER] webserver info', address);
          if (typeof address === 'object') {
            this.port = address.port;
            this.props.tunnelRouter.setWebserverPort(this.port);
          }
          resolve(address);
        });
        this.server = server;
      } catch (err) {
        reject(err);
      }
    });
  }

  getUrl() {
    return `http://127.0.0.1:${this.port}`;
  }

  close() {
    this.server && this.server.close();
  }
}

function readFileThunk(src: string): Promise<string> {
  return new Promise(function (resolve, reject) {
    fs.readFile(src, { encoding: 'utf8' }, function (err, data) {
      if (err) return reject(err);
      resolve(data);
    });
  });
}

function stringifyData(obj: any) {
  const str = JSON.stringify(obj);
  return str
    .replace(/\\/g, '\\\\')
    .replace(/'/g, "\\'")
    .replace(/"/g, '\\"')
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\r')
    .replace(/\t/g, '\\t');
}
