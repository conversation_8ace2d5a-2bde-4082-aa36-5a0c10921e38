import logger from '@e/services/logger';
import { app, ipcMain } from 'electron';
import axios from 'axios';
import db from '@e/components/db';
import request, { RequestAgent, resolveApiUrl } from '@e/services/request';
import { resolveUrl, runCommand } from '@e/utils/utils';
import { PROBE_TIME_OUT, requestFromAgent } from '@e/utils/proxy_util';
import { createSocksTunnel, FrontendSocksTunnel } from '@e/utils/tunnels/direct/frontend';
import { SocksProxyAgent } from 'socks-proxy-agent';
import { HuaYoungTunnel } from '@e/utils/tunnels/HuaYoungTunnel';
import { hyDecrypt, hyDecryptVo } from '@e/utils/crypto';
import { portalRpcClient } from '@e/components/backendTask';
import {
  createOriginDirectTunnel,
  OriginalHttpTunnel,
  OriginalSocksTunnel,
  OriginalSshTunnel,
} from '@e/utils/tunnels/direct/OriginDirectTunnel';

const PING_BASE_MS = 200;
const PING_INTERVAL = 30 * 60 * 1000;

class Client2TransitDetector {
  private timer: NodeJS.Timeout | null = null;
  probeUrl = 'http://www.google.com/';
  start() {
    // 测速用axios，其它用request
    logger.verbose(`client2TransitDetector start`);
    this.ping();
  }

  async requestUrlByAgent(host: string, port: number) {
    let startTime = Date.now();
    let endTime = await new Promise<number>((resolve, reject) => {
      const agent = new SocksProxyAgent(`socks5h://127.0.0.1:${port}`);
      axios
        .request({
          url: this.probeUrl,
          httpAgent: agent,
          httpsAgent: agent,
          timeout: PROBE_TIME_OUT * 1000,
        })
        .then((res) => {
          resolve(Date.now());
        })
        .catch((e) => {
          reject(e);
        });
    });
    return {
      success: true,
      testingTime: endTime - startTime,
    };
  }

  async ping() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    logger.verbose(`client2TransitDetector ping`);
    const ipCheckers = await request(`/api/meta/ip/checkers`);
    if (ipCheckers?.probeUrl) {
      this.probeUrl = ipCheckers.probeUrl;
    }
    return new Promise((resolve) => {
      request('/api/tunnel/probeNodes').then(
        async (tunnelProbeNodeVos: API.TunnelProbeNodeVo[]) => {
          const promiseAll: Promise<any>[] = [];
          tunnelProbeNodeVos?.forEach((tunnelProbeNodeVo) => {
            const { clashProxy, transitProbe, groupId, nodeId, nodeName } = tunnelProbeNodeVo;
            const baseProps = {
              groupId,
              nodeId,
              status: 'Pending',
            };
            if (clashProxy && clashProxy.url) {
              // Clash 前置代理类型
              promiseAll.push(
                new Promise(async (_resolve) => {
                  let frontendSocksTunnel: FrontendSocksTunnel | undefined;
                  try {
                    frontendSocksTunnel = createSocksTunnel(clashProxy.url!);
                    const costTime = await frontendSocksTunnel?.ping([this.probeUrl]);
                    const pingRes = {
                      testingTime: costTime,
                    };
                    logger.info(
                      `[PING] 节点 ${nodeName}(${groupId}:${nodeId}) 测速成功 - ${pingRes.testingTime}ms`,
                    );
                    _resolve({
                      ...baseProps,
                      status: 'Available',
                      testingTime: pingRes.testingTime,
                      weight: Math.ceil(
                        (Math.min(
                          PING_BASE_MS * 3,
                          Math.max(PING_BASE_MS / 3, pingRes.testingTime!),
                        ) /
                          (PING_BASE_MS * 3)) *
                          100,
                      ),
                    });
                  } catch (e: any) {
                    logger.info(`[PING] 节点 ${nodeName}(${groupId}:${nodeId}) 测速失败`);
                    _resolve({
                      ...baseProps,
                      status: 'Unavailable',
                      testingTime: PING_BASE_MS * 3,
                      weight: 100,
                    });
                  } finally {
                    frontendSocksTunnel?.close();
                  }
                }),
              );
            } else if (transitProbe) {
              // 花漾接入点类型
              promiseAll.push(
                new Promise(async (_resolve) => {
                  let hyTunnel: HuaYoungTunnel | undefined;
                  try {
                    hyTunnel = new HuaYoungTunnel({
                      token: hyDecrypt(transitProbe.token!),
                      teamId: 0,
                      sessionId: 0,
                      sessionTokenVo: {},
                      sessionChannelTokenVo: {
                        endpoints: [
                          {
                            tunnelType: 'transit',
                            nodeId: nodeId,
                            endpoint: transitProbe.endpoint!,
                          },
                        ],
                      },
                      transitList: [],
                      requestAgent: new RequestAgent(),
                      autoSwitchTransit: () => {},
                      canSwitchTransit: () => false,
                    });
                    await hyTunnel.init({ defaultTransitId: nodeId });
                    const { host, port } = hyTunnel.authInfo();
                    const pingRes = await this.requestUrlByAgent(host, port);
                    logger.info(
                      `[PING] 节点 ${nodeName}(${groupId}:${nodeId}) 测速成功 - ${pingRes.testingTime}ms`,
                    );
                    _resolve({
                      ...baseProps,
                      status: 'Available',
                      testingTime: pingRes.testingTime,
                      weight: Math.ceil(
                        (Math.min(
                          PING_BASE_MS * 3,
                          Math.max(PING_BASE_MS / 3, pingRes.testingTime!),
                        ) /
                          (PING_BASE_MS * 3)) *
                          100,
                      ),
                    });
                  } catch (e: any) {
                    logger.info(`[PING] 节点 ${nodeName}(${groupId}:${nodeId}) 测速失败`);
                    _resolve({
                      ...baseProps,
                      status: 'Unavailable',
                      testingTime: PING_BASE_MS * 3,
                      weight: 100,
                    });
                  } finally {
                    hyTunnel?.close();
                  }
                }),
              );
            }
          });
          Promise.all(promiseAll).then(async (res) => {
            resolve(res);
            logger.verbose('client2TransitDetector result update begin');
            request('/api/tunnel/probeResult', {
              method: 'PUT',
              data: {
                results: res,
              },
            }).then(() => {
              logger.verbose('client2TransitDetector result update success');
            });
            this.timer = setTimeout(() => {
              this.ping();
            }, PING_INTERVAL);
          });
        },
      );
    });
  }
  myLocation() {
    const url = resolveApiUrl('/api/meta/ip/myLocation');
    return requestFromAgent({
      url,
      startTime: Date.now(),
    }).then((res) => {
      logger.verbose('client2TransitDetector myLocation');
      if (res.success) {
        try {
          const { data } = JSON.parse(<string>res.ip);
          const { ip, country, province, city } = data;
          return {
            ip,
            location: `${country}${province}${city}`,
          };
        } catch (e) {
          return null;
        }
      }
      return null;
    });
  }
  client2Transit(endpoint: string) {
    return new Promise((resolve) => {
      const url = resolveUrl(endpoint, '/transitMyIp');
      const startTime = new Date().getTime();
      requestFromAgent({
        url,
        startTime,
      })
        .then((res) => {
          const { ip, testingTime } = res;
          resolve({
            success: true,
            remoteIp: ip,
            testingTime,
            status: 'Available',
          });
        })
        .catch((err) => {
          const { error, code } = err;
          resolve({
            success: false,
            code,
            error,
            status: 'Unavailable',
          });
        });
    });
  }
}

export function netProbe(netProbeNodeVo: any): Promise<{
  status: string;
  connectTime?: number;
  downloadTime?: number;
  size?: number;
  error?: string;
}> {
  return new Promise(async (resolve) => {
    const {
      groupId,
      nodeId,
      nodeName,
      proxyConfig,
      targetUrl = 'https://api.ip.sb/ip',
      clashProxy,
      transitProbe,
      timeout,
      repeatTimes = 5,
    } = netProbeNodeVo;
    if (transitProbe) {
      // 花漾 TUNNEL 测速
      let hyTunnel: HuaYoungTunnel | undefined;
      try {
        hyTunnel = new HuaYoungTunnel({
          token: hyDecrypt(transitProbe.token!),
          teamId: 0,
          sessionId: 0,
          sessionTokenVo: {},
          sessionChannelTokenVo: {
            endpoints: [
              {
                tunnelType: 'transit',
                nodeId: nodeId,
                endpoint: transitProbe.endpoint!,
              },
            ],
          },
          transitList: [],
          requestAgent: new RequestAgent(),
          autoSwitchTransit: () => {},
          canSwitchTransit: () => false,
        });
        await hyTunnel.init({ defaultTransitId: nodeId });
        const successRes = [];
        const failedRes = [];
        for (let i = 0; i < repeatTimes; i++) {
          try {
            const res = await hyTunnel.request(targetUrl, timeout).then((probeRes) => {
              logger.info(
                `[PROBE] 节点 ${nodeName}(${groupId}:${nodeId}) 第【${i + 1}】次测速成功（${
                  probeRes.dataLength
                } - ${targetUrl}） - ${probeRes.timing?.connect}ms/${probeRes.timing?.download}ms`,
              );
              return probeRes;
            });
            successRes.push(res);
          } catch (e) {
            failedRes.push(e);
          }
        }
        // 如果至少有一次测速成功，算平均值
        if (successRes.length > 0) {
          const connectTime = successRes.reduce((sum, item: any) => {
            return sum + item.timing?.connect;
          }, 0);
          const downloadTime = successRes.reduce((sum, item: any) => {
            return sum + item.timing?.download;
          }, 0);
          const size = successRes.reduce((sum, item: any) => {
            return sum + item.dataLength;
          }, 0);
          logger.info(
            `[PROBE] 节点 ${nodeName}(${groupId}:${nodeId}) 测速成功（${
              size / successRes.length
            } - ${targetUrl}） - ${connectTime / successRes.length}ms/${
              downloadTime / successRes.length
            }ms`,
          );
          resolve({
            status: 'Succeed',
            connectTime: Math.ceil(connectTime / successRes.length),
            downloadTime: Math.ceil(downloadTime / successRes.length),
            size: Math.ceil(size / successRes.length),
          });
        } else {
          throw failedRes[0] || new Error('测试失败');
        }
      } catch (e: any) {
        logger.info(`[PROBE] 节点 ${nodeName}(${groupId}:${nodeId}) 测速失败 - ${targetUrl}`);
        resolve({
          status: 'Failed',
          error: e.message,
        });
      } finally {
        hyTunnel?.close();
      }
    } else {
      // 直连/前置代理 测速
      let originTunnel: OriginalSocksTunnel | OriginalHttpTunnel | OriginalSshTunnel | undefined;
      try {
        const _proxyConfig = hyDecryptVo(proxyConfig);
        originTunnel = createOriginDirectTunnel(_proxyConfig, 0, {}, {}, new RequestAgent());
        if (clashProxy?.url) {
          originTunnel.setFrontendProxyUrl(clashProxy.url);
        }
        await originTunnel.init();
        const successRes = [];
        const failedRes = [];
        for (let i = 0; i < repeatTimes; i++) {
          try {
            const res = await originTunnel.request(targetUrl, timeout).then((probeRes) => {
              logger.info(
                `[PROBE] 节点 ${nodeName}(${groupId}:${nodeId}) 第【${i + 1}】次测速成功（${
                  probeRes.dataLength
                } - ${targetUrl}） - ${probeRes.timing?.connect}ms/${probeRes.timing?.download}ms`,
              );
              return probeRes;
            });
            successRes.push(res);
          } catch (e) {
            failedRes.push(e);
          }
        }
        // 如果至少有一次测速成功，算平均值
        if (successRes.length > 0) {
          const connectTime = successRes.reduce((sum, item: any) => {
            return sum + item.timing?.connect;
          }, 0);
          const downloadTime = successRes.reduce((sum, item: any) => {
            return sum + item.timing?.download;
          }, 0);
          const size = successRes.reduce((sum, item: any) => {
            return sum + item.dataLength;
          }, 0);
          logger.info(
            `[PROBE] 节点 ${nodeName}(${groupId}:${nodeId}) 测速成功（${
              size / successRes.length
            } - ${targetUrl}） - ${connectTime / successRes.length}ms/${
              downloadTime / successRes.length
            }ms`,
          );
          resolve({
            status: 'Succeed',
            connectTime: Math.ceil(connectTime / successRes.length),
            downloadTime: Math.ceil(downloadTime / successRes.length),
            size: Math.ceil(size / successRes.length),
          });
        } else {
          throw failedRes[0] || new Error('测试失败');
        }
      } catch (e: any) {
        logger.info(`[PROBE] 节点 ${nodeName}(${groupId}:${nodeId}) 测速失败 - ${targetUrl}`);
        resolve({
          status: 'Failed',
          error: e.message,
        });
      } finally {
        originTunnel?.close();
      }
    }
  });
}

// 监听测速任务
export function listenerProbeTask() {
  portalRpcClient.onAppEmit('ops-device-probe-event', async (data) => {
    logger.info(`[PROBE] ops-device-probe-event`, data);
    const { async: isAsync = true, netProbeIds = [], netCommandIds = [] } = data;
    const netProbeQueue = (netProbeIds || []).map((id: number) => {
      return () =>
        new Promise(async (resolve) => {
          let netProbeNodeVo: any | undefined;
          try {
            netProbeNodeVo = await request(`/api/netprobe/fetchProbe/${id}`);
            if (!netProbeNodeVo) {
              resolve();
              return;
            }
          } catch (e) {
            logger.error(`[PROBE] 获取测速节点失败`, e);
            return;
          }
          netProbe(netProbeNodeVo).then((probeResult) => {
            return axios
              .request({
                url: resolveApiUrl(`/api/netprobe/probeResult/${id}`),
                headers: {
                  Cookie: db.getCookies().join(';') ?? '',
                  'Content-Type': 'application/json',
                },
                method: 'POST',
                responseType: 'json',
                data: probeResult,
              })
              .catch((err) => {
                logger.error(`[PROBE] 节点测速结果上报失败`, err);
                throw err;
              });
          });
        });
    });
    const netCommandQueue = (netCommandIds || []).map((id: number) => {
      return () =>
        new Promise(async (resolve) => {
          const netCommandVo = await request(`/api/netprobe/fetchCommand/${id}`);
          if (!netCommandVo) {
            resolve();
            return;
          }
          const { command, timeout } = netCommandVo;
          const commandRes = await runCommand(command, timeout);
          resolve(commandRes);
        }).then((commandRes) => {
          return axios
            .request({
              url: resolveApiUrl(`/api/netprobe/commandResult/${id}`),
              headers: {
                Cookie: db.getCookies().join(';') ?? '',
                'Content-Type': 'application/json',
              },
              method: 'POST',
              responseType: 'json',
              data: commandRes,
            })
            .catch((err) => {
              logger.error(`[PROBE] 指令结果上报失败`, err);
              throw err;
            });
        });
    });
    if (isAsync) {
      Promise.all(netProbeQueue).then(() => {
        logger.info('[PROBE] 节点测速完成');
      });
      Promise.all(netCommandQueue).then(() => {
        logger.info('[PROBE] Command Finished');
      });
    } else {
      for (const netProbe of netProbeQueue) {
        await netProbe();
      }
      for (const netCommand of netCommandQueue) {
        await netCommand();
      }
    }
  });
}

export const client2TransitDetector = new Client2TransitDetector();
export default () => {
  app.whenReady().then(() => {
    ipcMain.handle('client-to-transit-ping', async (event) => {
      return await client2TransitDetector.ping();
    });
    ipcMain.handle('client-location', async (event) => {
      return await client2TransitDetector.myLocation();
    });
    ipcMain.handle('client-to-transit', async (event, data: any) => {
      return await client2TransitDetector.client2Transit(data);
    });
  });
};
