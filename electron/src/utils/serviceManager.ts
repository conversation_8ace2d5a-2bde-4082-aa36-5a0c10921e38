import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import logger from '@e/services/logger';

const execAsync = promisify(exec);

interface ServiceInfo {
  name: string;
  displayName: string;
  status: 'running' | 'stopped' | 'paused' | 'unknown';
  startType: 'auto' | 'manual' | 'disabled' | 'unknown';
}

/**
 * 检查服务是否存在
 */
export async function serviceExists(serviceName: string): Promise<boolean> {
  try {
    await execAsync(`sc query "${serviceName}"`);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 获取服务状态
 */
export async function getServiceStatus(serviceName: string): Promise<ServiceInfo> {
  try {
    const { stdout } = await execAsync(`sc query "${serviceName}"`);
    const lines = stdout.split('\n');

    const serviceInfo: ServiceInfo = {
      name: serviceName,
      displayName: '',
      status: 'unknown',
      startType: 'unknown',
    };

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('SERVICE_NAME:')) {
        serviceInfo.name = trimmed.split(':')[1].trim();
      } else if (trimmed.startsWith('DISPLAY_NAME')) {
        serviceInfo.displayName = trimmed.split(':')[1].trim();
      } else if (trimmed.startsWith('STATE')) {
        const state = trimmed.split(':')[1].trim();
        if (state.includes('RUNNING')) {
          serviceInfo.status = 'running';
        } else if (state.includes('STOPPED')) {
          serviceInfo.status = 'stopped';
        } else if (state.includes('PAUSED')) {
          serviceInfo.status = 'paused';
        }
      } else if (trimmed.startsWith('START_TYPE')) {
        const startType = trimmed.split(':')[1].trim();
        if (startType.includes('AUTO')) {
          serviceInfo.startType = 'auto';
        } else if (startType.includes('DEMAND')) {
          serviceInfo.startType = 'manual';
        } else if (startType.includes('DISABLED')) {
          serviceInfo.startType = 'disabled';
        }
      }
    }

    return serviceInfo;
  } catch (error) {
    logger.error(`Failed to get service status for ${serviceName}:`, error);
    throw new Error(`Service '${serviceName}' not found or query failed`);
  }
}

/**
 * 停止服务
 */
export async function stopService(serviceName: string, force: boolean = false): Promise<boolean> {
  try {
    // 首先检查服务是否存在
    const exists = await serviceExists(serviceName);
    if (!exists) {
      logger.warn(`Service '${serviceName}' does not exist`);
      return false;
    }

    // 获取当前服务状态
    const serviceInfo = await getServiceStatus(serviceName);
    if (serviceInfo.status === 'stopped') {
      logger.info(`Service '${serviceName}' is already stopped`);
      return true;
    }

    // 停止服务
    const command = force ? `sc stop "${serviceName}"` : `sc stop "${serviceName}"`;
    const { stdout } = await execAsync(command);

    logger.info(`Service '${serviceName}' stop command executed`);

    // 等待服务完全停止
    await waitForServiceStatus(serviceName, 'stopped', 30000); // 30秒超时

    return true;
  } catch (error) {
    logger.error(`Failed to stop service '${serviceName}':`, error);
    throw error;
  }
}

/**
 * 启动服务
 */
export async function startService(serviceName: string): Promise<boolean> {
  try {
    const exists = await serviceExists(serviceName);
    if (!exists) {
      logger.warn(`Service '${serviceName}' does not exist`);
      return false;
    }

    const serviceInfo = await getServiceStatus(serviceName);
    if (serviceInfo.status === 'running') {
      logger.info(`Service '${serviceName}' is already running`);
      return true;
    }

    await execAsync(`sc start "${serviceName}"`);
    logger.info(`Service '${serviceName}' start command executed`);

    // 等待服务完全启动
    await waitForServiceStatus(serviceName, 'running', 30000);

    return true;
  } catch (error) {
    logger.error(`Failed to start service '${serviceName}':`, error);
    throw error;
  }
}

/**
 * 禁用服务
 */
export async function disableService(serviceName: string): Promise<boolean> {
  try {
    const exists = await serviceExists(serviceName);
    if (!exists) {
      logger.warn(`Service '${serviceName}' does not exist`);
      return false;
    }

    // 先停止服务
    await stopService(serviceName, true);

    // 禁用服务
    await execAsync(`sc config "${serviceName}" start= disabled`);
    logger.info(`Service '${serviceName}' has been disabled`);

    return true;
  } catch (error) {
    logger.error(`Failed to disable service '${serviceName}':`, error);
    throw error;
  }
}

/**
 * 等待服务达到指定状态
 */
export async function waitForServiceStatus(
  serviceName: string,
  targetStatus: 'running' | 'stopped' | 'paused',
  timeoutMs: number = 30000,
): Promise<boolean> {
  const startTime = Date.now();

  while (Date.now() - startTime < timeoutMs) {
    try {
      const serviceInfo = await getServiceStatus(serviceName);
      if (serviceInfo.status === targetStatus) {
        return true;
      }
    } catch (error) {
      // 忽略查询错误，继续等待
    }

    // 等待1秒后重试
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  throw new Error(`Timeout waiting for service '${serviceName}' to reach status '${targetStatus}'`);
}

/**
 * 使用 PowerShell 管理服务（替代方案）
 */
export async function stopServiceWithPowerShell(serviceName: string): Promise<boolean> {
  try {
    const command = `powershell -Command "Stop-Service -Name '${serviceName}' -Force -PassThru"`;
    const { stdout } = await execAsync(command);

    logger.info(`Service '${serviceName}' stopped via PowerShell`);
    return true;
  } catch (error) {
    logger.error(`Failed to stop service '${serviceName}' via PowerShell:`, error);
    throw error;
  }
}

/**
 * 关闭 Microsoft PC Manager Service
 * Microsoft PC Manager 的服务名称通常是 'MsMpSvc' (Windows Defender) 或 'Microsoft PC Manager Service'
 */
export async function stopMicrosoftPCManagerService(): Promise<boolean> {
  const possibleServiceNames = [
    'Microsoft PC Manager Service',
    'PCManager Service Store',
    'MpcService', // Microsoft PC Manager Service
  ];

  for (const serviceName of possibleServiceNames) {
    try {
      logger.info(`Attempting to stop service: ${serviceName}`);

      if (await serviceExists(serviceName)) {
        const serviceInfo = await getServiceStatus(serviceName);
        logger.info(
          `Found service: ${serviceInfo.displayName} (${serviceInfo.name}), status: ${serviceInfo.status}`,
        );

        if (serviceInfo.status === 'running') {
          // 尝试使用 sc 命令停止
          try {
            await stopService(serviceName, true);
            logger.info(`Successfully stopped service: ${serviceName}`);
            return true;
          } catch (scError) {
            logger.warn(`Failed to stop with sc command, trying PowerShell: ${serviceName}`);

            // 如果 sc 命令失败，尝试使用 PowerShell
            try {
              await stopServiceWithPowerShell(serviceName);
              logger.info(`Successfully stopped service via PowerShell: ${serviceName}`);
              return true;
            } catch (psError) {
              logger.error(`Failed to stop service with both methods: ${serviceName}`);
            }
          }
        } else {
          logger.info(`Service ${serviceName} is not running (status: ${serviceInfo.status})`);
        }
      }
    } catch (error) {
      logger.warn(`Error checking service ${serviceName}:`, error);
    }
  }

  logger.warn('No Microsoft PC Manager service found or all attempts failed');
  return false;
}
