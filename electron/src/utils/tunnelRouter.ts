import _ from 'lodash';
import { dialog, powerMonitor } from 'electron';
import { LocalSocksTunnel } from '@e/utils/tunnels/LocalTunnel';
import { RequestAgent } from '@e/services/request';
import logger from '@e/services/logger';
import { ChannelTunnel } from '@e/utils/tunnels/ChannelTunnel';
import { ITunnel } from '@e/utils/tunnels';
import { proxyProbe, readSystemProxy } from '@e/utils/proxy_util';
import { getChromium } from '@e/utils/window';
import db from '@e/components/db';
import i18n from '@e/utils/i18n';
import { AjaxEventClient } from '@e/utils/AjaxEventClient';
import { regs } from '@e/utils/utils';

type Props = {
  shopInfo: API.ShopDetailVo;
  sessionTokenVo: API.SessionTokenVo;
  transitList: API.TransitWithLocationVo[];
  requestAgent: RequestAgent;
  ajaxEventClient?: AjaxEventClient;
};

export class TunnelRouter {
  props: Props;
  // 浏览器代理配置参数
  pacArg: string;
  localTunnel!: LocalSocksTunnel;
  fallbackTunnel!: ITunnel;
  channelMap: Record<number | string, ITunnel>;
  sessionId: number;
  heartbeatTimer: any;
  ipSwitchCheckTimer: any;
  lastRemoteIp: string;
  ipSwitchDialogVisibility: boolean;
  ajaxEventListeners: string[] = [];
  closed: boolean;
  afterResumeTimer: any = 0;
  constructor(props: Props) {
    this.props = props;
    const { sessionId } = this.props.sessionTokenVo;
    this.closed = false;
    this.sessionId = sessionId!;
    this.heartbeatTimer = 0;
    this.channelMap = {};
    this.pacArg = '';
    this.ipSwitchCheckTimer = 0;
    this.lastRemoteIp = '';
    this.ipSwitchDialogVisibility = false;
    this._onPowerSuspend = this._onPowerSuspend.bind(this);
    this._onPowerResume = this._onPowerResume.bind(this);
    this.listenerTransitOfflineEvent();
  }

  async init() {
    const { teamId, channelTokens, routers } = this.props.sessionTokenVo;
    // 创建本地代理 LOCAL
    this.fallbackTunnel = this.localTunnel = this.channelMap['local'] = new LocalSocksTunnel();
    let systemProxyRes: any = undefined;
    if (
      channelTokens?.some(
        (sessionChannelTokenVo) => sessionChannelTokenVo.channelScene === 'lanSystem',
      )
    ) {
      systemProxyRes = await readSystemProxy();
      if (!systemProxyRes.success) {
        logger.error('[TUNNEL] read system proxy failed', systemProxyRes.msg);
        const error: any = new Error(systemProxyRes.msg);
        error.code = 'readSystemProxyError';
        throw error;
      }
    }
    let systemProxy = systemProxyRes?.proxy;
    // 创建通道 TUNNEL1, TUNNEL2...
    channelTokens?.forEach((sessionChannelTokenVo) => {
      const isDirect = sessionChannelTokenVo.channelScene === 'lanDirect';
      const isSystemProxy = sessionChannelTokenVo.channelScene === 'lanSystem';
      if (
        sessionChannelTokenVo.channelId ||
        (['lanDirect', 'lanSystem', 'lanProxy', 'sessionProxy'].includes(
          sessionChannelTokenVo.channelScene!,
        ) &&
          !db.isRpaExecutor())
      ) {
        if (isDirect || isSystemProxy) {
          if (isDirect) {
            // 走直连
            return;
          }
          //走本地代理
          if (isSystemProxy) {
            //使用系统代理
            if (systemProxy && systemProxy.host && systemProxy.port) {
              sessionChannelTokenVo.proxyConfig = {
                host: systemProxy.host,
                port: systemProxy.port,
                proxyType: systemProxy.proxyType,
              };
            } else {
              return; //未开启读到系统代理
            }
          }
        }
        let channelTunnel = new ChannelTunnel({
          sessionTokenVo: this.props.sessionTokenVo,
          sessionChannelTokenVo: sessionChannelTokenVo,
          sessionId: this.sessionId,
          teamId: teamId!,
          transitList: this.props.transitList,
          requestAgent: this.props.requestAgent,
        });
        if (sessionChannelTokenVo.channelId) {
          this.channelMap[sessionChannelTokenVo.channelId] = channelTunnel;
        } else {
          //本地代理
          this.fallbackTunnel = this.channelMap['lanProxy'] = channelTunnel;
        }
      }
    });
    const promiseArr: Promise<any>[] = [];
    _.forEach(this.channelMap, (tunnel, key) => {
      promiseArr.push(tunnel.init());
    });
    await Promise.all(promiseArr);
    const pacScript = this.getPacScript();
    const base64 = Buffer.from(pacScript).toString('base64');
    this.pacArg = `--proxy-pac-url=data:application/x-javascript-config;base64,${base64}`;
    powerMonitor.on('suspend', this._onPowerSuspend);
    powerMonitor.on('resume', this._onPowerResume);
  }

  startHeartbeat() {
    clearInterval(this.heartbeatTimer);
    this.heartbeatTimer = setInterval(async () => {
      await this.heartbeat();
    }, 60 * 1000);
    let remoteIpCheckInterval = 10 * 60 * 1000;
    if (this.props.shopInfo.ipSwitchCheckInterval) {
      remoteIpCheckInterval = Math.min(
        remoteIpCheckInterval,
        this.props.shopInfo.ipSwitchCheckInterval * 1000,
      );
    }
    clearInterval(this.ipSwitchCheckTimer);
    this.ipSwitchCheckTimer = setInterval(() => {
      this.checkRemoteIp();
    }, remoteIpCheckInterval);
  }

  _onPowerSuspend() {
    logger.info('[APP] powerMonitor state: suspend');
  }

  _onPowerResume() {
    logger.info('[APP] powerMonitor state: resume');
    clearTimeout(this.afterResumeTimer);
    this.afterResumeTimer = setTimeout(() => {
      this.heartbeat();
      _.forEach(this.channelMap, (tunnel, key) => {
        if (tunnel instanceof ChannelTunnel) {
          tunnel.refreshToken(true);
        }
      });
    }, 5 * 1000);
  }

  getPacScript() {
    const { shopInfo, transitList, sessionTokenVo } = this.props;
    const { channelTokens, routers } = sessionTokenVo;
    let pacRuleForLandPage = '';
    let primaryChannel = channelTokens?.find(
      (sessionChannelTokenVo) => sessionChannelTokenVo.primary,
    );
    if (primaryChannel) {
      // 首页API调用，转发到主IP的通道
      [
        'https://ipinfo.io/*',
        'https://ipapi.co/*',
        ...transitList.map((vo) => vo.endpoints + '*'),
      ].forEach((rule) => {
        pacRuleForLandPage += `if (shExpMatch(url, '${rule}')) return '${this.channelMap[
          primaryChannel?.channelId!
        ].pacRule()}';`;
      });
    }
    let pacRuleStr = `${pacRuleForLandPage}if (shExpMatch(url, 'http://127.0.0.1:${
      this.localTunnel.socks5Port
    }/*') || shExpMatch(url, 'http://szdamai.local/*') || shExpMatch(url, 'http://szdamai.tab/*') || shExpMatch(url, 'http://ggbrowser.local/*') || shExpMatch(url, 'http://ggbrowser.tab/*') || shExpMatch(url, 'https://testsafebrowsing.appspot.com/*') || shExpMatch(host, 'www.szdamai.com') || shExpMatch(host, 'app.szdamai.com') || shExpMatch(host, 'api.szdamai.com')) return '${this.localTunnel.pacRule()}';`;
    if (shopInfo.intranetEnabled) {
      pacRuleStr += `if (isInnerIPFn(host)) return '${this.fallbackTunnel.pacRule()}';`;
    } else if (primaryChannel) {
      pacRuleStr += `if (isInnerIPFn(host)) return '${this.channelMap[
        primaryChannel.channelId!
      ].pacRule()}';`;
    }
    // 根据路由规则生成 PAC 脚本
    if (false) {
      pacRuleStr += `return 'SOCKS5 127.0.0.1:7891';`;
    } else {
      routers
        ?.sort((a, b) => a.orderNo! - b.orderNo!)
        .forEach((shopRouterVo) => {
          let rule = (shopRouterVo.rule ?? '').replaceAll("'", '%27');
          // pacRuleStr += `alert('@@@@Match ' + host + ' as ${shopRouterVo.rule}' + (shExpMatch(host, '${rule}') ? ' true' : ' false'));`;
          if (shopRouterVo.channelId === 0) {
            //走本地直连
            pacRuleStr += `if (shExpMatch(host, '${rule}') || shExpMatch(url, '${rule}')) return '${this.fallbackTunnel.pacRule()}';`;
          } else if (this.channelMap[shopRouterVo.channelId!]) {
            pacRuleStr += `if (shExpMatch(host, '${rule}') || shExpMatch(url, '${rule}')) return '${this.channelMap[
              shopRouterVo.channelId!
            ].pacRule()}';`;
          }
        });
      pacRuleStr += `return '${this.fallbackTunnel.pacRule()}';`;
    }
    const utilsFunc = `
    function isInnerIPFn(ipAddress) {
      let isInnerIp = false;
      let ipNum = getIpNum(ipAddress);
      let aBegin = getIpNum('10.0.0.0');
      let aEnd = getIpNum('**************');
      let bBegin = getIpNum('**********');
      let bEnd = getIpNum('**************');
      let cBegin = getIpNum('***********');
      let cEnd = getIpNum('***************');
      let dBegin = getIpNum('*********');
      let dEnd = getIpNum('***************');
      isInnerIp =
        isInner(ipNum, aBegin, aEnd) ||
        isInner(ipNum, bBegin, bEnd) ||
        isInner(ipNum, cBegin, cEnd) ||
        isInner(ipNum, dBegin, dEnd);
      return isInnerIp;
    }
    function getIpNum(ipAddress) {
      let ip = ipAddress.split('.');
      let a = parseInt(ip[0]);
      let b = parseInt(ip[1]);
      let c = parseInt(ip[2]);
      let d = parseInt(ip[3]);
      let ipNum = a * 256 * 256 * 256 + b * 256 * 256 + c * 256 + d;
      return ipNum;
    }
    function isInner(userIp, begin, end) {
      return userIp >= begin && userIp <= end;
    }`;
    const pacScript = `function FindProxyForURL(url, host) {${utilsFunc}${pacRuleStr}}`;
    return pacScript;
  }

  findTargetProxy(url: string) {
    const pacScript = this.getPacScript();
    const f = new Function(
      'url',
      'host',
      `
function shExpMatch(str, pattern) {
    const regexPattern = pattern
        .replace(/\\./g, '\\\\.')
        .replace(/\\?/g, '.')
        .replace(/\\*/g, '.*');
    const regex = new RegExp('^' + regexPattern + '$');
    return regex.test(str);
}
${pacScript}
return FindProxyForURL(url, host)
`,
    );
    let host = '';
    try {
      host = new URL(url).hostname;
    } catch (e) {}
    return f(url, host);
  }

  close() {
    this.closed = true;
    _.forEach(this.channelMap, (tunnel) => {
      tunnel.close();
    });
    this.ajaxEventListeners.forEach((hd) => {
      this.props.ajaxEventClient?.un(hd);
    });
    clearInterval(this.heartbeatTimer);
    clearInterval(this.ipSwitchCheckTimer);
    powerMonitor.off('suspend', this._onPowerSuspend);
    powerMonitor.off('resume', this._onPowerResume);
  }

  /**
   * 心跳
   */
  async heartbeat() {
    try {
      const { teamId } = this.props.sessionTokenVo;
      const sessionStatus = await this.props.requestAgent.request(
        `/api/shop/session/${this.sessionId}/heartbeat`,
        {
          teamId,
        },
      );
      if (sessionStatus === 'CLOSE') {
        logger.info(
          `[BROWSER] session(sessionId:${this.sessionId}) status has changed to CLOSE, start closing browser`,
        );
        // 关闭会话
        const chromium = getChromium({ sessionId: this.props.sessionTokenVo.sessionId });
        if (chromium) {
          chromium.close();
          logger.reportError(new Error(i18n.t('会话心跳超时，已被关闭')), {
            context: 'SESSION',
            noNotify: true,
          });
          dialog.showMessageBox({
            type: 'error',
            title: i18n.t('会话中断'),
            message: i18n.t('会话心跳超时，已被关闭'),
          });
        }
      }
    } catch (e: any) {
      logger.error('[TUNNEL] heartbeat Error: ', e);
      if (
        e?.code === 401 ||
        e?.message === '会话已经关闭' ||
        e?.message === i18n.t('Session has been closed')
      ) {
        this.close();
        const chromium = getChromium({ sessionId: this.props.sessionTokenVo.sessionId });
        if (chromium?.getBrowser()) {
          logger.info(
            `[APP] 由于会话心跳检测到会话已经关闭，尝试关闭浏览器（sessionId: ${this.props.sessionTokenVo.sessionId}）`,
          );
          chromium.close();
        }
      }
    }
  }

  getDefaultChannel() {
    const { sessionTokenVo } = this.props;
    const { channelTokens } = sessionTokenVo;
    const primaryChannel = channelTokens?.find(
      (sessionChannelTokenVo) => sessionChannelTokenVo.primary,
    );
    let channel;
    if (primaryChannel) {
      channel = this.channelMap[primaryChannel.channelId!];
    } else {
      channel = _.find(this.channelMap, (v, k) => k !== 'local');
    }
    return channel;
  }

  async getRemoteIp() {
    const { teamId, channelTokens } = this.props.sessionTokenVo;
    let channel = this.getDefaultChannel();
    const primaryChannel = channelTokens?.find(
      (sessionChannelTokenVo) => sessionChannelTokenVo.primary,
    );
    if (channel) {
      const probeRes = await proxyProbe({
        ...channel.authInfo(),
        teamId: teamId!,
        ipVersion: primaryChannel?.targetIp?.ipv6 ? 'IPv6' : 'IPv4',
        requestAgent: this.props.requestAgent,
      });
      if (probeRes.success && probeRes.ip) {
        this.lastRemoteIp = probeRes.ip;
      }
      return probeRes;
    }
    return null;
  }

  getLastRemoteIp() {
    return this.lastRemoteIp;
  }

  /**
   * 检查出口IP
   */
  async checkRemoteIp() {
    const { teamId, channelTokens } = this.props.sessionTokenVo;
    let channel = this.getDefaultChannel();
    const primaryChannel = channelTokens?.find(
      (sessionChannelTokenVo) => sessionChannelTokenVo.primary,
    );
    if (channel) {
      const res = await proxyProbe({
        ...channel.authInfo(),
        teamId: teamId!,
        ipVersion: primaryChannel?.targetIp?.ipv6 ? 'IPv6' : 'IPv4',
        requestAgent: this.props.requestAgent,
      });
      if (res.success && res.ip) {
        if (!this.lastRemoteIp) {
          this.lastRemoteIp = res.ip;
        }
        // update remote ip
        channel.updateRemoteIp(res.ip);
        this.reportSessionRemoteIp(res.ip);
        if (this.props.shopInfo.ipSwitchStrategy !== 'Off') {
          this.checkIsRemoteIpSwitched(res.ip);
        }
      }
    }
  }

  checkIsRemoteIpSwitched(newRemoteIp: string) {
    if (this.lastRemoteIp !== newRemoteIp) {
      logger.info(`[TUNNEL] remote IP change from ${this.lastRemoteIp} to ${newRemoteIp}`);
      this.lastRemoteIp = newRemoteIp;
      if (this.props.shopInfo.ipSwitchStrategy === 'Abort') {
        // 中断会话
        const chromium = getChromium({ sessionId: this.props.sessionTokenVo.sessionId });
        if (chromium) {
          chromium.close();
        }
        if (!this.ipSwitchDialogVisibility) {
          this.ipSwitchDialogVisibility = true;
          dialog
            .showMessageBox({
              type: 'error',
              title: i18n.t('会话中断'),
              message: i18n.t('IP发生切换，会话被强行中断'),
            })
            .then(() => {
              this.ipSwitchDialogVisibility = false;
            });
        }
      } else if (
        this.props.shopInfo.ipSwitchStrategy === 'Alert' &&
        !this.ipSwitchDialogVisibility
      ) {
        this.ipSwitchDialogVisibility = true;
        // 告警
        dialog
          .showMessageBox({
            type: 'error',
            title: i18n.t('会话告警'),
            message: i18n.t('提醒您注意：IP已发生切换'),
            buttons: [i18n.t('我知道了'), i18n.t('结束会话')],
            defaultId: 0,
            cancelId: 0,
          })
          .then((res) => {
            this.ipSwitchDialogVisibility = false;
            if (res.response === 1) {
              // 中断会话
              const chromium = getChromium({ sessionId: this.props.sessionTokenVo.sessionId });
              if (chromium) {
                chromium.close();
              }
            }
          });
      }
    }
  }

  reportSessionRemoteIp(remoteIp: string) {
    if (regs.ipv4.test(remoteIp) || regs.ipv6.test(remoteIp)) {
      this.props.requestAgent.request(
        `/shop/ip-monitor/report?sessionId=${this.props.sessionTokenVo.sessionId}&ip=${remoteIp}`,
        { teamId: this.props.sessionTokenVo.teamId },
      );
    }
  }

  async updateRemoteIp(ip: string) {
    let channel = this.getDefaultChannel();
    if (channel instanceof ChannelTunnel) {
      channel.impl.updateRemoteIp(ip);
    }
    this.reportSessionRemoteIp(ip);
  }

  setWebserverPort(port: number) {
    const localSocksTunnel = this.channelMap['local'] as LocalSocksTunnel;
    if (localSocksTunnel) {
      localSocksTunnel.setWebserverPort(port);
    }
  }

  async switchChannelTransit(
    channelId: number | string,
    filterOption: {
      tunnelType: string;
      groupId: number;
      nodeId?: number;
      proxyId?: number;
    },
  ) {
    const { tunnelType, groupId, nodeId, proxyId } = filterOption;
    const tunnel = this.channelMap[channelId] as ChannelTunnel;
    if (tunnel) {
      const targetEndpointVo = tunnel.getAvailableEndpoints().find((vo) => {
        if (vo.tunnelType !== tunnelType) return false;
        if (vo.groupId !== groupId) return false;
        if (nodeId && vo.nodeId !== nodeId) return false;
        if (proxyId && vo.proxyId !== proxyId) return false;
        return true;
      });
      if (targetEndpointVo) {
        await tunnel.switchTransit(targetEndpointVo);
      }
    }
  }

  async refreshLoginDeviceTransitPing(channelId: number) {
    const tunnel = this.channelMap[channelId] as ChannelTunnel;
    if (tunnel) {
      return await tunnel.refreshLoginDeviceTransitPing();
    }
    return false;
  }

  async refreshIpTransitPing(channelId: number) {
    const tunnel = this.channelMap[channelId] as ChannelTunnel;
    if (tunnel) {
      return await tunnel.refreshIpTransitPing();
    }
    return false;
  }

  getChannelList() {
    const { channelTokens } = this.props.sessionTokenVo;
    return _.map(channelTokens, (channel, idx) => {
      let channelId = channel.channelId;
      if (['lanSystem', 'lanProxy'].includes(channel.channelScene!)) {
        //@ts-ignore
        channelId = 'lanProxy';
      }
      const tunnel = this.channelMap[channelId!] as ChannelTunnel;
      return tunnel
        ? {
            ...tunnel.getSessionChannelTokenVo(),
            currentEndpointVo: tunnel.getCurrentEndpointVo(),
            currentEndpoint: tunnel.getEndpointUrl(),
            transitPing: tunnel.getTransitPing(),
          }
        : undefined;
    })
      .filter((v) => !!v && !v.official)
      .sort((a) => (a!.primary ? -1 : 1));
  }

  getRouterRules() {
    const { routers } = this.props.sessionTokenVo;
    return routers ?? [];
  }

  getAutoChangeTransitProp() {
    const { channelTokens } = this.props.sessionTokenVo;
    let primaryChannel = channelTokens?.find(
      (sessionChannelTokenVo) => sessionChannelTokenVo.primary,
    );
    if (process.env.OEM_NAME === 'gg') {
      return {
        checked: false,
        disabled: true,
      };
    }
    if (primaryChannel && (primaryChannel.endpoints?.length ?? 0) > 1) {
      let checked = primaryChannel?.targetIp?.domestic === false;
      return {
        checked,
        disabled: false,
      };
    }
    return {
      checked: false,
      disabled: true,
    };
  }

  async autoSwitchPrimaryChannelTransit(force = false) {
    const { channelTokens } = this.props.sessionTokenVo;
    let primaryChannel = channelTokens?.find(
      (sessionChannelTokenVo) => sessionChannelTokenVo.primary,
    );
    if (primaryChannel) {
      const tunnel = this.channelMap[primaryChannel.channelId!] as ChannelTunnel;
      if (tunnel) {
        if (force) {
          await tunnel.doAutoSwitchTransit();
        } else {
          await tunnel.autoSwitchTransit();
        }
      }
    }
  }

  async listenerTransitOfflineEvent() {
    const { ajaxEventClient } = this.props;
    if (!ajaxEventClient) return;
    const hd = await ajaxEventClient.on('ops-tunnel-offline', this.sessionId, (data) => {
      logger.info('[SESSION] receive ops-tunnel-offline event', data);
      const { transitIds = [] } = data || {};
      _.forEach(this.channelMap, (tunnel, channelId) => {
        if (/^\d+$/.test(channelId)) {
          (tunnel as ChannelTunnel).refreshToken(true);
        }
      });
    });
    this.ajaxEventListeners.push(hd);
  }
}
