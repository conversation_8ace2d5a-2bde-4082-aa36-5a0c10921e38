import { ITunnel } from '@e/utils/tunnels/index';
import logger from '@e/services/logger';
import { RequestAgent } from '@e/services/request';
import { isInnerIPFn, TunnelSocksConfig } from '@e/utils/utils';
import db from '@e/components/db';
import {
  createPipeTunnel,
  createSocksTunnel,
  FrontendSocksTunnel,
} from '@e/utils/tunnels/direct/frontend';
import { readSystemProxy } from '@e/utils/proxy_util';

export const SOCKET_TIMEOUT = 60 * 1000;

export type ChannelInfo = {
  teamId: number;
  channelSessionId?: number;
};

/**
 * 直连第三方协议目标地址的tunnel
 */
export abstract class OriginDirectTunnel extends ITunnel {
  ping: number = -1; //ping值
  channelInfo: ChannelInfo;
  props!: TunnelSocksConfig;
  upTraffic: number = 0;
  downTraffic: number = 0;
  trafficReportTimer: any = 0;
  frontendProxyUrl?: string;
  frontendTunnel?: FrontendSocksTunnel;

  constructor(
    sessionTokenVo: API.SessionTokenVo,
    sessionChannelTokenVo: API.SessionChannelTokenVo & { teamId: number },
    props: TunnelSocksConfig,
    requestAgent: RequestAgent,
  ) {
    super({
      sessionTokenVo,
      sessionChannelTokenVo,
      teamId: sessionChannelTokenVo.teamId,
      requestAgent,
    });
    this.channelInfo = {
      teamId: sessionChannelTokenVo.teamId,
      channelSessionId: sessionChannelTokenVo.channelSessionId,
    };
    this.props = props!;
    this.props.requestAgent = requestAgent;
    if (sessionChannelTokenVo.channelSessionId) {
      this.trafficReportTimer = setInterval(() => {
        this.reportTraffic();
      }, 60 * 1000);
    }
  }

  setFrontendProxyUrl(url?: string) {
    // 只有海外IP才能使用前置代理
    if (this.baseProps.sessionChannelTokenVo?.targetIp?.domestic === false) {
      this.frontendProxyUrl = url;
    }
  }

  async tryCreateFrontendTunnel() {
    if (this.frontendProxyUrl) {
      return createPipeTunnel(this.frontendProxyUrl, this.props.host!, this.props.port!);
    }
    return undefined;
  }

  async testFrontendProxy() {
    if (!this.frontendProxyUrl) return;
    // 前置代理连通性测试
    const frontendProxy = createSocksTunnel(this.frontendProxyUrl);
    try {
      await frontendProxy?.ping(undefined, 6 * 1000);
    } catch (e) {
      throw new Error('前置代理连通性测试超时（6s）');
    } finally {
      frontendProxy?.close();
    }
  }

  getEndpointUrl(): string {
    return `${this.props.host}:${this.props.port}`;
  }

  reportTraffic() {
    const { sessionChannelTokenVo, teamId, requestAgent } = this.baseProps;
    if (!requestAgent || !sessionChannelTokenVo?.channelSessionId || !teamId) return;
    if (this.upTraffic === 0 && this.downTraffic === 0) return;
    if (teamId) {
      // 汇报流量
      requestAgent
        .request(
          `/api/traffic/byChannelSession/${sessionChannelTokenVo.channelSessionId}?upTraffic=${this.upTraffic}&downTraffic=${this.downTraffic}`,
          { method: 'POST', teamId },
        )
        .catch((e) => {
          if (e.message === 'not a team member') {
            clearInterval(this.trafficReportTimer);
          }
          logger.error(
            `[TUNNEL] report traffic failed(${JSON.stringify(
              sessionChannelTokenVo.channelSessionId,
            )})`,
            e,
          );
        });
      logger.verbose(
        `[TUNNEL] Upload: ${(this.upTraffic / 1024).toFixed(2)}KB, Download:${(
          this.downTraffic / 1024
        ).toFixed(2)}KB`,
      );
    }
    this.upTraffic = 0;
    this.downTraffic = 0;
  }

  close() {
    if (this.closed) return;
    super.close();
    this.reportTraffic();
    clearInterval(this.trafficReportTimer);
  }
}
