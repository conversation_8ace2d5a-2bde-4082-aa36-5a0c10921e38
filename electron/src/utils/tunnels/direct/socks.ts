import { SocksClient, SocksClientOptions, SocksProxy } from 'socks';
import net, { Server, Socket } from 'net';
import { <PERSON>uff<PERSON> } from 'buffer';
import { getHostname } from '@e/utils/tunnels/index';
import logger from '@e/services/logger';
import { RequestAgent } from '@e/services/request';
import { TunnelSocksConfig } from '@e/utils/utils';
import { OriginDirectTunnel, SOCKET_TIMEOUT } from '@e/utils/tunnels/direct/base';
import { SocksProxyAgent } from 'socks-proxy-agent';
import { PROBE_TIME_OUT } from '@e/utils/proxy_util';

/**
 * 直连用户原始的 socks5 协议的通道
 */
export class OriginalSocksTunnel extends OriginDirectTunnel {
  socks5Server?: Server;
  socks5Port: number = 0;

  proxy: SocksProxy;

  constructor(
    sessionTokenVo: API.SessionTokenVo,
    sessionChannelTokenVo: API.SessionChannelTokenVo & { teamId: number },
    props: TunnelSocksConfig,
    requestAgent: RequestAgent,
  ) {
    super(sessionTokenVo, sessionChannelTokenVo, props, requestAgent);
    this.proxy = {
      host: this.props.host!,
      port: this.props.port!,
      userId: this.props.username ?? undefined,
      password: this.props.password ?? undefined,
      type: 5,
    };
  }

  pacRule(): string {
    if (!this.socks5Port) {
      throw 'tunnel not ready!';
    }
    return `SOCKS5 127.0.0.1:${this.socks5Port}`;
  }

  async init(): Promise<any> {
    this.frontendTunnel = await this.tryCreateFrontendTunnel();
    if (this.frontendTunnel) {
      try {
        let frontInfo = await this.frontendTunnel.open();
        this.proxy.host = frontInfo.host;
        this.proxy.port = frontInfo.port;
      } catch (e) {}
    }
    return new Promise((resolve, reject) => {
      this.socks5Server = net.createServer((socket) => {
        let remoteSocks: any = null;
        socket.setTimeout(SOCKET_TIMEOUT);
        socket.setNoDelay(true);
        socket.once('data', (data) => {
          if (!data || data[0] !== 0x05)
            return socket.destroy(new Error(`${data[0]} is not supported`));
          socket.write(Buffer.from([5, 0]), (err) => {
            if (err) {
              socket.destroy();
            }
            socket.once('data', async (data) => {
              if (data.length < 7 || data[1] !== 0x01) return socket.destroy(); // 只支持 CONNECT
              try {
                const ATYP = data[3]; // 目标服务器地址类型
                if (ATYP !== 1 && ATYP !== 3) {
                  return socket.destroy();
                }
                let remoteAddr;
                let remotePort = data.slice(data.length - 2).readUInt16BE(0); //最后两位为端口值
                let copyBuf = Buffer.allocUnsafe(data.length);
                data.copy(copyBuf);
                if (ATYP === 1) {
                  // 0x01 IP V4地址
                  remoteAddr = getHostname(data.slice(4, 8));
                } else {
                  //0x03 域名地址
                  // @ts-ignore
                  let len = parseInt(data[4], 10);
                  remoteAddr = data.slice(5, 5 + len).toString('utf8');
                }
                remoteSocks = await this.pipeSocks(socket, remoteAddr, remotePort, copyBuf);
              } catch (e) {
                logger.error('[TUNNEL] socket trans to websocket Error: ', e);
              }
            });
          });
        });

        //监听错误
        socket.on('error', (err: any) => {
          if (err.code !== 'ECONNRESET') {
            logger.error(
              `[TUNNEL] socket（SOCKS5-${this.channelInfo.channelSessionId}） Error: `,
              err.message,
            );
          }
          socket.destroyed || socket.destroy();
          remoteSocks && (remoteSocks.destroyed || remoteSocks.destroy());
        });

        socket.on('close', () => {
          socket.destroyed || socket.destroy();
          remoteSocks && (remoteSocks.destroyed || remoteSocks.destroy());
        });
      });

      this.socks5Server.listen(0, this.listenHost, () => {
        const address = this.socks5Server!.address();
        if (address && typeof address === 'object') {
          this.socks5Port = address.port;
          logger.verbose(
            `[TUNNEL] OriginDirect SOCKS5 server listening on port ${this.socks5Port}`,
          );
          resolve(true);
        } else {
          logger.error('[TUNNEL] parse server address failed');
          reject(new Error('parse server address failed'));
        }
      });
    });
  }

  async pipeSocks(server: Socket, remoteAddr: string, remotePort: number, copyBuf: Buffer) {
    const socksOpts: SocksClientOptions = {
      proxy: this.proxy,
      destination: { host: remoteAddr, port: remotePort },
      command: 'connect',
      timeout: SOCKET_TIMEOUT,
    };
    try {
      const { socket } = await SocksClient.createConnection(socksOpts);
      socket.setNoDelay(true);
      this.logForLinkConnect(`${remoteAddr}:${remotePort}`);
      copyBuf[1] = 0x00;
      if (server.writable) {
        server.write(copyBuf);
      }
      server.pipe(socket);
      socket.pipe(server);
      server.on('data', (buf) => {
        // console.log('@@@@===>', buf.length);
        this.upTraffic += buf.length;
      });
      server.on('close', (buf) => {
        server.destroyed || server.destroy();
        socket.destroyed || socket.destroy();
      });
      socket.on('data', (buf) => {
        // console.log('@@@@<===', buf.length);
        this.downTraffic += buf.length;
      });
      socket.on('error', (err) => {
        logger.error(`[TUNNEL] socket（SOCKS5-${this.channelInfo.channelSessionId}） Error: `, err);
        server.destroyed || server.destroy();
        socket.destroyed || socket.destroy();
      });
      socket.on('close', () => {
        server.destroyed || server.destroy();
        socket.destroyed || socket.destroy();
      });
      return socket;
    } catch (e: any) {
      logger.error(`[TUNNEL] socks5 createConnection fail ${e.message}`);
      server.destroy();
    }
  }

  close() {
    super.close();
    this.socks5Server?.close();
    this.frontendTunnel?.close();
    this.socks5Port = 0;
  }

  request(url: string, timeout?: number) {
    const agent = new SocksProxyAgent(
      {
        hostname: this.listenHost,
        port: this.socks5Port,
        type: 5,
      },
      {
        timeout: timeout ?? PROBE_TIME_OUT * 1000,
      },
    );
    return super.request(url, agent, timeout);
  }

  authInfo(): {
    password: string;
    port: number;
    proxyType: string;
    host: string;
    username: string;
  } {
    return {
      proxyType: 'socks5',
      host: this.listenHost,
      port: this.socks5Port,
      username: '',
      password: '',
    };
  }
}
