import net, { Server } from 'net';
import { <PERSON>uff<PERSON> } from 'buffer';
import logger from '@e/services/logger';
import { RequestAgent } from '@e/services/request';
import { TunnelSocksConfig } from '@e/utils/utils';
import { ChannelInfo, OriginDirectTunnel, SOCKET_TIMEOUT } from '@e/utils/tunnels/direct/base';
import { PROBE_TIME_OUT } from '@e/utils/proxy_util';
import { HttpProxyAgent } from 'http-proxy-agent';
import { HttpsProxyAgent } from 'https-proxy-agent';

//@ts-ignore
const HTTPParser = (process.binding('http_parser').HTTPParser =
  require('http-parser-js').HTTPParser);
const CR = 0xd,
  LF = 0xa,
  BUF_CR = Buffer.from([0xd]),
  BUF_CR_LF_CR_LF = Buffer.from([0xd, 0xa, 0xd, 0xa]),
  BUF_LF_LF = Buffer.from([0xa, 0xa]),
  BUF_PROXY_CONNECTION_CLOSE = Buffer.from('Proxy-Connection: close');
const STATE_NONE = 0,
  STATE_FOUND_LF = 1,
  STATE_FOUND_LF_CR = 2;

/**
 * 直接用户原始的 http proxy 的通道
 */
export class OriginalHttpTunnel extends OriginDirectTunnel {
  httpServer?: Server;
  httpPort = 0;
  upTraffic: number = 0;
  downTraffic: number = 0;
  proxy: { host: string; port: number; username?: string; password?: string };

  constructor(
    sessionTokenVo: API.SessionTokenVo,
    channelInfo: ChannelInfo,
    props: TunnelSocksConfig,
    requestAgent: RequestAgent,
  ) {
    super(sessionTokenVo, channelInfo, props, requestAgent);
    this.proxy = {
      host: props.host!,
      port: props.port!,
      username: this.props.username,
      password: this.props.password,
    };
  }

  async init(): Promise<any> {
    this.frontendTunnel = await this.tryCreateFrontendTunnel();
    if (this.frontendTunnel) {
      try {
        let frontInfo = await this.frontendTunnel.open();
        this.proxy.host = frontInfo.host;
        this.proxy.port = frontInfo.port;
      } catch (e) {}
    }
    return new Promise((resolve, reject) => {
      let _authInfo = 'Proxy-Authorization:';
      if (this.proxy.username || this.proxy.password) {
        _authInfo =
          'Proxy-Authorization: Basic ' +
          Buffer.from(
            (this.proxy.username || 'anonymous') + ':' + (this.proxy.password || 'anonymous'),
          ).toString('base64');
      }
      const authInfo = Buffer.from(_authInfo);
      this.httpServer = net
        .createServer({ allowHalfOpen: true }, (socket) => {
          //@ts-ignore
          let realCon = net.connect({
            port: this.proxy.port,
            host: this.proxy.host,
            allowHalfOpen: true,
            rejectUnauthorized: false,
            timeout: SOCKET_TIMEOUT,
          });
          realCon
            .on('data', (buf: Buffer) => {
              socket.write(buf);
              this.downTraffic += buf.length;
            })
            .on('end', function () {
              socket.end();
            })
            .on('close', function () {
              socket.end();
            })
            .on('error', function (err: any) {
              console.error('[TUNNEL] HTTP CONNECT ' + err);
              socket.destroy();
            });

          let parser = new HTTPParser(HTTPParser.REQUEST);
          parser[HTTPParser.kOnHeadersComplete] = function (
            versionMajor: any,
            versionMinor: any,
            headers: any,
            method: any,
            url: any,
            statusCode: any,
            statusMessage: any,
            upgrade: any,
            shouldKeepAlive: any,
          ) {
            parser.__is_headers_complete = true;
            parser.__upgrade = upgrade;
            parser.__method = method;
          };

          let state = STATE_NONE;

          socket
            .on('data', (buf) => {
              this.upTraffic += buf.length;
              if (!parser) {
                realCon.write(buf);
                return;
              }
              if (this.needLogForLinkConnect) {
                const [header] = buf.toString().split('\n');
                if (header.startsWith('CONNECT')) {
                  this.logForLinkConnect(/^CONNECT\s(\S+)/.exec(header)?.[1] ?? '');
                }
              }

              let buf_ary = [],
                unsavedStart = 0,
                buf_len = buf.length;

              for (let i = 0; i < buf_len; i++) {
                //find first LF
                if (state === STATE_NONE) {
                  if (buf[i] === LF) {
                    state = STATE_FOUND_LF;
                  }
                  continue;
                }

                //find second CR LF or LF
                if (buf[i] === LF) {
                  parser.__is_headers_complete = false;
                  parser.execute(buf.slice(unsavedStart, i + 1));

                  if (parser.__is_headers_complete) {
                    buf_ary.push(buf.slice(unsavedStart, buf[i - 1] === CR ? i - 1 : i));
                    buf_ary.push(authInfo);
                    buf_ary.push(state === STATE_FOUND_LF_CR ? BUF_CR_LF_CR_LF : BUF_LF_LF);
                    if (parser.__method === 5 /*CONNECT*/ || parser.__upgrade) {
                      parser.close();
                      parser = null;

                      buf_ary.push(buf.slice(i + 1));
                      realCon.write(Buffer.concat(buf_ary));

                      state = STATE_NONE;
                      return;
                    }

                    unsavedStart = i + 1;
                    state = STATE_NONE;
                  } else {
                    state = STATE_FOUND_LF;
                  }
                } else if (buf[i] === CR && state === STATE_FOUND_LF) {
                  state = STATE_FOUND_LF_CR;
                } else {
                  state = STATE_NONE;
                }
              }

              if (unsavedStart < buf_len) {
                buf = buf.slice(unsavedStart, buf_len);
                parser.execute(buf);
                buf_ary.push(buf);
              }

              realCon.write(Buffer.concat(buf_ary));
            })
            .on('end', cleanup)
            .on('close', cleanup)
            .on('error', function (err) {
              console.error('[TUNNEL] ' + err);
            });

          function cleanup() {
            parser?.close();
            realCon.end();
          }
        })
        .on('error', function (err) {
          console.error('[TUNNEL] ' + err);
        })
        .listen(0, this.listenHost, () => {
          const address = this.httpServer!.address();
          if (address && typeof address === 'object') {
            this.httpPort = address.port;
            logger.verbose(`[TUNNEL] OriginDirect HTTP server listening on port ${this.httpPort}`);
            resolve(true);
          } else {
            logger.error('[TUNNEL] parse server address failed');
            reject(new Error('parse server address failed'));
          }
        });
    });
  }

  pacRule(): string {
    if (!this.httpPort) {
      throw 'tunnel not ready!';
    }
    return `PROXY 127.0.0.1:${this.httpPort}`;
  }

  request(url: string, timeout?: number) {
    let agent: HttpProxyAgent<any> | HttpsProxyAgent<any> = new HttpProxyAgent(
      `http://${this.listenHost}:${this.httpPort}`,
      {
        timeout: timeout ?? PROBE_TIME_OUT * 1000,
      },
    );
    if (url.startsWith('https')) {
      agent = new HttpsProxyAgent(`http://${this.listenHost}:${this.httpPort}`, {
        timeout: timeout ?? PROBE_TIME_OUT * 1000,
      });
    }
    return super.request(url, agent, timeout);
  }

  authInfo(): {
    password: string;
    port: number;
    proxyType: string;
    host: string;
    username: string;
  } {
    return {
      proxyType: 'http',
      host: this.listenHost,
      port: this.httpPort,
      username: '',
      password: '',
    };
  }

  async close(): Promise<any> {
    super.close();
    this.httpServer?.close();
    this.frontendTunnel?.close();
    this.httpPort = 0;
  }
}
