import net, { Server } from 'net';
import { Client, ConnectConfig } from 'ssh2';
import { <PERSON><PERSON>er } from 'buffer';
import { getHostname } from '@e/utils/tunnels/index';
import logger from '@e/services/logger';
import { RequestAgent } from '@e/services/request';
import { getAFreePort, TunnelSocksConfig } from '@e/utils/utils';
import { ChannelInfo, OriginDirectTunnel, SOCKET_TIMEOUT } from '@e/utils/tunnels/direct/base';
import { PROBE_TIME_OUT } from '@e/utils/proxy_util';
import { SocksProxyAgent } from 'socks-proxy-agent';

export class OriginalSshTunnel extends OriginDirectTunnel {
  socks5Server?: Server;
  socks5Port: number = 0;
  sshConfig: ConnectConfig;
  upTraffic: number = 0;
  downTraffic: number = 0;
  client?: Client;
  hasReady: boolean = false;
  readyTimer: any = 0;
  reConnectTimer: any = 0;

  constructor(
    sessionTokenVo: API.SessionTokenVo,
    channelInfo: ChannelInfo,
    props: TunnelSocksConfig,
    requestAgent: RequestAgent,
  ) {
    super(sessionTokenVo, channelInfo, props, requestAgent);
    this.sshConfig = {
      host: this.props.host,
      port: this.props.port,
      username: this.props.username,
    };
    // @ts-ignore
    if (this.props.sshKey) {
      // @ts-ignore
      this.sshConfig.privateKey = this.props.sshKey;
      // @ts-ignore
      if (this.props.passphrase) {
        // @ts-ignore
        this.sshConfig.passphrase = this.props.passphrase;
      }
    }
    if (this.props.password) {
      this.sshConfig.password = this.props.password;
    }
  }

  async init(): Promise<any> {
    this.frontendTunnel = await this.tryCreateFrontendTunnel();
    if (this.frontendTunnel) {
      try {
        let frontInfo = await this.frontendTunnel.open();
        this.sshConfig.host = frontInfo.host;
        this.sshConfig.port = frontInfo.port;
      } catch (e) {}
    }
    return new Promise((resolve, reject) => {
      this.socks5Server = net.createServer((socket) => {
        socket.setTimeout(SOCKET_TIMEOUT);
        socket.setNoDelay(true);
        socket.once('data', (data) => {
          if (!data || data[0] !== 0x05)
            return socket.destroy(new Error(`${data[0]} is not supported`));
          if (!this.hasReady) {
            return socket.destroy(new Error(`SSH tunnel is not ready`));
          }
          socket.write(Buffer.from([5, 0]), (err) => {
            if (err) {
              socket.destroy();
            }
            socket.once('data', (data) => {
              this.upTraffic += data.length;
              if (data.length < 7 || data[1] !== 0x01 || !this.hasReady) return socket.destroy(); // 只支持 CONNECT
              try {
                const ATYP = data[3]; // 目标服务器地址类型
                if (ATYP !== 1 && ATYP !== 3) {
                  return socket.destroy();
                }
                let remoteAddr: string;
                let remotePort = data.slice(data.length - 2).readUInt16BE(0); //最后两位为端口值
                let copyBuf = Buffer.allocUnsafe(data.length);
                data.copy(copyBuf);
                if (ATYP === 1) {
                  // 0x01 IP V4地址
                  remoteAddr = getHostname(data.slice(4, 8));
                } else {
                  //0x03 域名地址
                  // @ts-ignore
                  let len = parseInt(data[4], 10);
                  remoteAddr = data.slice(5, 5 + len).toString('utf8');
                }
                this.client?.forwardOut(
                  socket.localAddress!,
                  socket.localPort!,
                  remoteAddr,
                  remotePort,
                  (err, stream) => {
                    if (err) {
                      socket.writable && socket.end(Buffer.from([0x05, 0x02]));
                      return;
                    }
                    if (socket.writable) {
                      copyBuf[1] = 0x00;
                      socket.write(Buffer.from(copyBuf));
                    }
                    stream
                      .pipe(socket)
                      .pipe(stream)
                      .on('data', (buf: Buffer) => {
                        this.downTraffic += buf.length;
                      });
                  },
                );
                this.logForLinkConnect(`${remoteAddr}:${remotePort}`);
              } catch (e: any) {
                logger.error('[TUNNEL] SSH forward Error: ', e.message);
              }
            });
          });
        });

        //监听错误
        socket.on('error', (err: any) => {
          if (err.code !== 'ECONNRESET') {
            logger.error(
              `[TUNNEL] socket（SSH-${this.channelInfo.channelSessionId}） Error: `,
              err,
            );
          }
          socket.destroyed || socket.destroy();
        });

        socket.on('close', () => {
          socket.destroyed || socket.destroy();
        });
      });

      this.socks5Server.listen(0, this.listenHost, async () => {
        const address = this.socks5Server!.address();
        if (address && typeof address === 'object') {
          this.socks5Port = address.port;
          logger.verbose(`[TUNNEL] OriginDirect SSH server listening on port ${this.socks5Port}`);
          try {
            if (this.client) return;
            const localPort = await getAFreePort();
            this.client = new Client();
            this.client
              .on('ready', () => {
                logger.info(`[TUNNEL] SSH client ready`);
                this.hasReady = true;
                clearTimeout(this.readyTimer);
                resolve(true);
              })
              .on('end', () => {
                logger.info(`[TUNNEL] SSH client end`);
                clearTimeout(this.readyTimer);
                resolve(true);
                this._tryReconnect();
              })
              .on('close', () => {
                logger.info(`[TUNNEL] SSH client close`);
                if (this.client) {
                  this._tryReconnect();
                }
              })
              .on('connect', () => {
                logger.info(`[TUNNEL] SSH client connect`);
              })
              .on('error', (err) => {
                logger.error(`[TUNNEL] SSH client error`, err.message);
                clearTimeout(this.readyTimer);
                this._tryReconnect();
                reject(err);
              })
              .connect({
                ...this.sshConfig,
                keepaliveInterval: 10 * 1000,
                readyTimeout: 5 * 1000,
                timeout: SOCKET_TIMEOUT,
                localPort: localPort,
              });
            logger.info(`[TUNNEL] SSH client connecting via local port - ${localPort}`);
          } catch (e) {
            logger.error('[TUNNEL] New SSH client Error: ', e);
            reject(e);
          }
        } else {
          logger.error('[TUNNEL] parse server address failed');
          reject(new Error('parse server address failed'));
        }
      });
    });
  }

  _tryReconnect(timeout = 10 * 1000) {
    if (!this.client) return;
    clearTimeout(this.reConnectTimer);
    this.reConnectTimer = setTimeout(() => {
      logger.info('[TUNNEL] SSH reconnecting...');
      try {
        this.client?.connect({
          ...this.sshConfig,
          keepaliveInterval: 60 * 1000,
          timeout: SOCKET_TIMEOUT,
        });
      } catch (e) {
        logger.error('[TUNNEL] SSH reconnect error', e);
        this._tryReconnect();
      }
    }, timeout);
  }

  pacRule(): string {
    if (!this.socks5Port) {
      throw 'tunnel not ready!';
    }
    return `SOCKS5 127.0.0.1:${this.socks5Port}`;
  }

  request(url: string, timeout?: number) {
    const agent = new SocksProxyAgent(
      {
        hostname: this.listenHost,
        port: this.socks5Port,
        type: 5,
      },
      {
        timeout: timeout ?? PROBE_TIME_OUT * 1000,
      },
    );
    return super.request(url, agent, timeout);
  }

  authInfo(): {
    password: string;
    port: number;
    proxyType: string;
    host: string;
    username: string;
  } {
    return {
      proxyType: 'socks5',
      host: this.listenHost,
      port: this.socks5Port,
      username: '',
      password: '',
    };
  }

  close() {
    super.close();
    this.socks5Server?.close();
    this.frontendTunnel?.close();
    this.client?.destroy();
    this.socks5Port = 0;
    this.client = undefined;
    this.hasReady = false;
    clearTimeout(this.readyTimer);
    clearTimeout(this.reConnectTimer);
  }
}
