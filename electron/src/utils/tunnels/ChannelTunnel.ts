import _ from 'lodash';
import axios from 'axios';
import { ITunnel } from '@e/utils/tunnels/index';
import { HuaYoungTunnel } from '@e/utils/tunnels/HuaYoungTunnel';
import {
  createLanProxyTunnel,
  createOriginDirectTunnel,
  OriginalHttpTunnel,
  OriginalSocksTunnel,
  OriginalSshTunnel,
} from '@e/utils/tunnels/direct/OriginDirectTunnel';
import logger from '@e/services/logger';
import { rawRequest, RequestAgent } from '@e/services/request';
import { client2TransitDetector } from '@e/utils/ipc/ping';
import { PROBE_TIME_OUT, proxyProbe } from '@e/utils/proxy_util';
import { regs } from '@e/utils/utils';
import { getChromium } from '@e/utils/window';
import { hyDecrypt, hyDecryptVo } from '@e/utils/crypto';
import { createSocksTunnel, getLocalFrontendProxyUrl } from '@e/utils/tunnels/direct/frontend';
import { baseBackendTaskIns } from '@e/components/backendTask';

export interface TunnelProps {
  teamId: number;
  sessionId: number;
  sessionTokenVo: API.SessionTokenVo;
  sessionChannelTokenVo: API.SessionChannelTokenVo;
  transitList: API.TransitWithLocationVo[];
  requestAgent: RequestAgent;
}

export interface HuaYoungTunnelProps extends TunnelProps {
  token: string;
  autoSwitchTransit: () => void;
}

/**
 * 走channel ip的通道
 */
export class ChannelTunnel extends ITunnel {
  props: TunnelProps;
  hyTunnel?: HuaYoungTunnel;
  originTunnel?: OriginalSshTunnel | OriginalHttpTunnel | OriginalSocksTunnel;
  impl!: HuaYoungTunnel | OriginalSshTunnel | OriginalHttpTunnel | OriginalSocksTunnel; //有可能是 HuaYoungTunnel 或者 OriginDirectTunnel
  loginDeviceTransitDtoList: API.LoginDeviceTransitDto[];
  ipTransitDtoList: API.IpTransitDto[];
  currentEndpoint: Partial<API.TunnelEndpointVo>;
  token: string;
  refreshTokenTimer: any = 0;
  isSwitchingTransit: boolean = false;
  autoSwitchEndpointsRecord: Partial<API.TunnelEndpointVo>[] = [];
  cleanAutoSwitchEndpointsRecordCount = 0;
  availableEndpoints: API.TunnelEndpointVo[];
  lastSwitchTransitTimestamp = 0;
  closed = false;
  localFrontendProxyUrl = '';

  constructor(props: TunnelProps) {
    super({
      sessionTokenVo: props.sessionTokenVo,
      sessionChannelTokenVo: props.sessionChannelTokenVo,
      teamId: props.teamId,
      requestAgent: props.requestAgent,
    });
    this.props = props;
    this.loginDeviceTransitDtoList = [];
    this.ipTransitDtoList = [];
    this.currentEndpoint = {
      tunnelType: 'direct',
      groupId: 0,
      nodeId: 0,
    };
    this.token = props.sessionChannelTokenVo.token!;
    this.availableEndpoints = props.sessionChannelTokenVo.endpoints || [];
    // 一小时刷新一次 token （token有效期24h）
    this.refreshTokenTimer = setInterval(async () => {
      this.refreshToken();
    }, 60 * 60 * 1000);
  }

  getSessionChannelTokenVo() {
    let _availableEndpoints = [...this.getAvailableEndpoints()];
    const localFrontendEndpoint = _availableEndpoints.find(
      (vo) => vo.tunnelType === 'localFrontend',
    );
    if (localFrontendEndpoint) {
      if (!this.localFrontendProxyUrl) {
        _availableEndpoints = _availableEndpoints.filter((vo) => vo.tunnelType !== 'localFrontend');
      } else {
        localFrontendEndpoint.endpoint = this.localFrontendProxyUrl;
      }
    }
    return {
      ...this.props.sessionChannelTokenVo,
      token: this.token,
      endpoints: _availableEndpoints,
    };
  }

  /**
   * 刷新 token, 以免过期
   */
  async refreshToken(autoSwitchIfCurrentOffline = false) {
    if (!this.props.sessionChannelTokenVo.channelSessionId) return;
    try {
      await baseBackendTaskIns.doReportClientIp();
      const sessionChannelTokenVo: API.SessionChannelTokenVo =
        await this.props.requestAgent.request(
          `/api/shop/session/channelToken?channelSessionId=${this.props.sessionChannelTokenVo.channelSessionId}`,
          {
            teamId: this.props.teamId,
          },
        );
      if (sessionChannelTokenVo.proxyConfig) {
        sessionChannelTokenVo.proxyConfig = hyDecryptVo(sessionChannelTokenVo.proxyConfig);
      }
      sessionChannelTokenVo.token = hyDecrypt(sessionChannelTokenVo.token!);
      this.props.sessionChannelTokenVo = sessionChannelTokenVo;
      this.token = sessionChannelTokenVo.token;
      logger.verbose(
        `[TUNNEL] Token refreshed （sessionId: ${this.props.sessionId}, channelId: ${this.props.sessionChannelTokenVo.channelId}）`,
        sessionChannelTokenVo.token,
      );
      if (_.isArray(sessionChannelTokenVo.endpoints)) {
        this.availableEndpoints = sessionChannelTokenVo.endpoints;
      }
      if (this.impl instanceof HuaYoungTunnel) {
        this.impl.updateToken(this.token);
        const hyEndpoints =
          sessionChannelTokenVo.endpoints?.filter(
            (vo) =>
              vo.tunnelType === 'transit' || vo.tunnelType === 'jump' || vo.pipeType === 'Tunnel',
          ) ?? [];
        if (hyEndpoints.length > 0) {
          this.impl.updateEndpoints(hyEndpoints);
        }
      }
      // 如果当前接入点不可用，自动切换到其他接入点
      if (
        autoSwitchIfCurrentOffline &&
        !this.availableEndpoints.some(
          (ep) =>
            this.currentEndpoint.tunnelType === ep.tunnelType &&
            this.currentEndpoint.nodeId === ep.nodeId &&
            this.currentEndpoint.proxyId === ep.proxyId,
        )
      ) {
        logger.info(
          `[TUNNEL] current endpoint unavailable, auto switch transit （sessionId: ${this.props.sessionId}, channelId: ${this.props.sessionChannelTokenVo.channelId}）`,
        );
        await this.doAutoSwitchTransit(true);
      }
    } catch (err: any) {
      if (err.message && err.message.includes('会话已经关闭')) {
        this.close();
      }
      logger.error(
        `[TUNNEL] Token refresh failed （sessionId: ${this.props.sessionId}, channelId: ${this.props.sessionChannelTokenVo.channelId}）`,
        err,
      );
    }
  }

  async setIpEndpointWeight() {
    if (!this.props.sessionChannelTokenVo.targetIp?.ipId) {
      return;
    }
    const payload = {
      ipId: this.props.sessionChannelTokenVo.targetIp?.ipId,
      groupId: this.currentEndpoint.groupId,
      tunnelType: this.currentEndpoint.tunnelType,
      weight: 20,
    };
    try {
      await this.props.requestAgent.request(`/api/shop/session/ipWeight`, {
        method: 'POST',
        teamId: this.props.teamId,
        params: payload,
      });
    } catch (e) {
      logger.error(`[TUNNEL] Set Ip Weight Failed （${JSON.stringify(payload)}）`, e);
    }
  }

  reportProxyPort() {
    if (!this.props.sessionChannelTokenVo.primary) {
      return;
    }
    const { type, port } = this.getProxyInfo();
    this.props.requestAgent.request(`/api/shop/session/${this.props.sessionId}/remoteProxyPort`, {
      method: 'PUT',
      teamId: this.props.teamId,
      params: {
        remoteProxyType: type,
        remoteProxyPort: port,
      },
    });
  }

  reportTunnelInfo(tunnelEndpointVo: API.TunnelEndpointVo) {
    if (!this.props.sessionChannelTokenVo.primary) {
      return;
    }
    this.props.requestAgent.request(`/api/shop/session/reportTunnelInfo`, {
      method: 'POST',
      teamId: this.props.teamId,
      data: {
        channelSessionId: this.props.sessionChannelTokenVo.channelSessionId,
        nodeId: tunnelEndpointVo.nodeId,
        proxyId: tunnelEndpointVo.proxyId,
        tunnelType: tunnelEndpointVo.tunnelType,
      },
    });
  }

  _getIpId() {
    const { sessionChannelTokenVo } = this.props;
    return sessionChannelTokenVo.targetIp?.ipId ?? 0;
  }

  _getIppIpId() {
    const { sessionChannelTokenVo } = this.props;
    return sessionChannelTokenVo.targetIp?.ippIpId ?? 0;
  }

  /**
   * 检查本地前置代理是否是直连模式
   * @param proxyUrl 前置代理
   * @param checkApi 探测出口 IP 的 URL
   */
  async isDiffRemoteIp(proxyUrl: string, checkApi: string) {
    const url = checkApi;
    const timeout = 10 * 1000;
    let success = false;
    let proxyRemoteIp = '';
    const isDiffRemoteIp = await new Promise<boolean>((resolve) => {
      let isDiff = false;
      Promise.allSettled([
        new Promise((r) => {
          axios
            .request({
              url,
              timeout,
            })
            .then((res) => {
              r(res.data!);
            })
            .catch((e) => {
              logger.error(`[API] Request ${url} Failed - Direct`, e);
              r('');
            });
        }),
        new Promise((r) => {
          const frontendProxy = createSocksTunnel(proxyUrl)!;
          frontendProxy
            .request(url, timeout)
            .then((res) => {
              r(res);
            })
            .catch((e) => {
              logger.error(`[API] Request ${url} Failed - By LocalFrontend Proxy`, e);
              r('');
            })
            .finally(() => {
              frontendProxy.close();
            });
        }),
      ]).then(([directRes, proxyRes]) => {
        if (directRes.status === 'fulfilled' && proxyRes.status === 'fulfilled') {
          success = true;
          isDiff = directRes.value !== proxyRes.value;
          proxyRemoteIp = proxyRes.value as string;
        }
        resolve(isDiff);
      });
    });
    if (success && !isDiffRemoteIp) {
      logger.info(
        `[TUNNEL] 通过本地前置代理（${proxyUrl}）探测出的出口 IP 与直连探测出的 IP 一致（${proxyRemoteIp}）（sessionId: ${this.props.sessionId}）`,
      );
    }
    return {
      isDiffRemoteIp,
      success,
    };
  }

  async getLocalFrontendProxyUrl() {
    let proxyUrl = await getLocalFrontendProxyUrl();
    if (proxyUrl) {
      for (const checkApi of ['https://ipinfo.io/ip', 'https://api.ip.sb/ip']) {
        const { isDiffRemoteIp, success } = await this.isDiffRemoteIp(proxyUrl, checkApi);
        if (success) {
          return isDiffRemoteIp ? proxyUrl : '';
        }
      }
    }
    return '';
  }

  async requestSwitchTransit(tunnelEndpointVo: API.TunnelEndpointVo) {
    const url = `/api/shop/session/switchTunnel`;
    const payload = {
      channelSessionId: this.props.sessionChannelTokenVo.channelSessionId,
      tunnelType: tunnelEndpointVo.tunnelType,
      nodeId: tunnelEndpointVo.nodeId,
      proxyId: tunnelEndpointVo.proxyId,
    };
    try {
      await this.props.requestAgent.request(url, {
        method: 'POST',
        teamId: this.props.teamId,
        data: payload,
      });
      logger.info(`[API] Request ${url} Success`, JSON.stringify(payload));
    } catch (e: any) {
      logger.error(`[API] Request ${url} Failed`, JSON.stringify(payload), e);
      if (e.code === 131) {
        // 会话不存在或已关闭
        this.close();
      }
      throw e;
    }
  }

  async init() {
    const { sessionChannelTokenVo } = this.props;
    this.hyTunnel = undefined;
    this.originTunnel = undefined;
    const ipId = this._getIpId();
    let pickedTransitId = -1;
    let _availableEndpoints = [...this.availableEndpoints];
    if (_availableEndpoints.some((vo) => vo.tunnelType === 'localFrontend')) {
      this.localFrontendProxyUrl = await this.getLocalFrontendProxyUrl();
      if (!this.localFrontendProxyUrl) {
        _availableEndpoints = _availableEndpoints.filter((vo) => vo.tunnelType !== 'localFrontend');
      }
      if (_availableEndpoints.length === 0) {
        throw new Error('系统偏好中未开启海外IP加速，无法建立网络连接');
      }
    }
    if (_availableEndpoints.length === 0) {
      throw new Error('没有可用的网络连接方式，请检查IP连接方式设置');
    }
    for (const endpointVo of _availableEndpoints) {
      const { tunnelType, groupId, nodeId, proxyId } = endpointVo;
      if (
        ['lanDirect', 'lanSystem', 'lanProxy', 'sessionProxy'].includes(
          sessionChannelTokenVo.channelScene!,
        )
      ) {
        // 系统代理/临时IP
        this.originTunnel = createLanProxyTunnel(this.props);
        try {
          await this.originTunnel.init();
        } catch (e) {
          this.originTunnel.close();
          throw e;
        }
        this.impl = this.originTunnel;
        logger.info(
          `[TUNNEL] use LanProxy （${tunnelType}@${groupId}:${nodeId}，sessionId: ${this.props.sessionId}）`,
        );
        return;
      } else {
        try {
          await this.createProxyIpTunnel(endpointVo);
          pickedTransitId = nodeId!;
          break;
        } catch (e) {
          logger.info(
            `[TUNNEL] connect endpoint failed (${tunnelType}@${groupId}:${nodeId}${
              proxyId ? `:${proxyId}` : ''
            }，sessionId: ${this.props.sessionId})`,
          );
        }
      }
    }
    if (pickedTransitId === -1) {
      const fallbackEndpoint = this.availableEndpoints[0];
      logger.info(
        `[TUNNEL] all endpoint connect failed, fallback to the first one (${
          fallbackEndpoint.tunnelType
        }@${fallbackEndpoint.groupId}:${fallbackEndpoint.nodeId}${
          fallbackEndpoint.proxyId ? `:${fallbackEndpoint.proxyId}` : ''
        }，sessionId: ${this.props.sessionId})`,
      );
      await this.createProxyIpTunnel(fallbackEndpoint, true);
    }
    this.autoSwitchEndpointsRecord.push(this.currentEndpoint);
  }

  async createProxyIpTunnel(tunnelEndpointVo: API.TunnelEndpointVo, force = false) {
    const { sessionChannelTokenVo } = this.props;
    const { groupId, nodeId, tunnelType, pipeType } = tunnelEndpointVo;

    await this.requestSwitchTransit(tunnelEndpointVo);
    this.currentEndpoint = tunnelEndpointVo;
    this.impl?.close();
    if (tunnelType === 'transit' || tunnelType === 'jump' || pipeType === 'Tunnel') {
      // 花漾通道协议
      this.hyTunnel = new HuaYoungTunnel({
        ...this.props,
        token: this.token,
        autoSwitchTransit: () => {
          this.autoSwitchTransit();
        },
      });
      try {
        await this.hyTunnel.init({
          defaultTransitId: nodeId!,
          rejectOnWebsocketInitFailed: !force,
        });
      } catch (e) {
        if (!force) {
          this.hyTunnel.close();
          throw e;
        }
      }
      this.impl = this.hyTunnel;
      logger.info(
        `[TUNNEL] use HuaYoung（${this.currentEndpoint.tunnelType}@${this.currentEndpoint.groupId}:${this.currentEndpoint.nodeId}）endpoint （sessionId: ${this.props.sessionId}）`,
      );
    } else {
      // 第三方协议
      const socksConfig = sessionChannelTokenVo.proxyConfig;
      this.originTunnel = createOriginDirectTunnel(
        socksConfig!,
        this.props.teamId,
        this.props.sessionTokenVo,
        sessionChannelTokenVo,
        this.props.requestAgent,
      );
      if (tunnelType === 'clash') {
        // clash 前置代理
        this.originTunnel.setFrontendProxyUrl(tunnelEndpointVo.endpoint);
      } else if (tunnelType === 'localFrontend') {
        // 本地前置代理
        this.localFrontendProxyUrl = await this.getLocalFrontendProxyUrl();
        if (this.localFrontendProxyUrl) {
          this.originTunnel.setFrontendProxyUrl(this.localFrontendProxyUrl);
        } else {
          if (!force) {
            this.originTunnel.close();
            throw new Error('偏好设置中未开启海外IP加速');
          }
        }
      }
      try {
        if (!force) {
          // 前置代理连通性测试
          await this.originTunnel.testFrontendProxy();
        }
        await this.originTunnel.init();
      } catch (e) {
        if (!force) {
          this.originTunnel.close();
          throw e;
        }
      }
      this.impl = this.originTunnel;
      logger.info(
        `[TUNNEL] use Origin（${this.currentEndpoint.tunnelType}@${this.currentEndpoint.groupId}:${
          this.currentEndpoint.nodeId
        }${this.currentEndpoint.proxyId ? `:${this.currentEndpoint.proxyId}` : ''}-${
          this.originTunnel.authInfo().proxyType
        }）endpoint （sessionId: ${this.props.sessionId}）`,
      );
    }
    this.reportTunnelInfo(tunnelEndpointVo);
    this.reportProxyPort();
  }

  getEndpointUrl(): string | undefined {
    return this.impl.getEndpointUrl();
  }

  getAvailableEndpoints() {
    return this.availableEndpoints;
  }

  getCurrentEndpointVo() {
    return this.currentEndpoint;
  }

  getTransitPing() {
    return {
      loginDeviceTransitDtoList: this.loginDeviceTransitDtoList,
      ipTransitDtoList: this.ipTransitDtoList,
    };
  }

  /**
   * 刷新客户端到接入点的延迟
   */
  async refreshLoginDeviceTransitPing() {
    const res: any = await client2TransitDetector.ping();
    this.loginDeviceTransitDtoList = res.map((transit: any) => {
      const dto = this.loginDeviceTransitDtoList.find((dto) => dto.transitId === transit.transitId);
      return {
        transitId: transit.transitId,
        ...dto,
        ..._.pick(transit, ['status', 'testingTime']),
        remoteIp: transit.ip,
        probeError: transit.error,
        probeCode: transit.code,
        loading: false,
      };
    });
    return this.loginDeviceTransitDtoList;
  }

  /**
   * 测试直连/接入点
   * @param transitId
   */
  async pingIpTransit(transitId: number): Promise<any> {
    const ipId = this._getIpId();
    const ippIpId = this._getIppIpId();
    const { proxyConfig } = this.props.sessionChannelTokenVo;
    let responseData;
    if (transitId === 0) {
      // 客户端到代理IP测试
      if (proxyConfig) {
        // 第三方协议
        // @ts-ignore
        const probeResult = await proxyProbe({
          ...proxyConfig,
          teamId: this.props.teamId,
          requestAgent: this.props.requestAgent,
        });
        const { success, testingTime, ip, error: errorText, code } = probeResult;
        if (!success) {
          responseData = {
            error: errorText,
            success: false,
            testingTime: -1,
            status: 'Unavailable',
            code,
          };
        } else {
          responseData = {
            testingTime,
            ip,
            status: 'Available',
            success: true,
          };
        }
      } else {
        // IPGO 直连
        try {
          const url = new URL(
            '/transitMyIp',
            this.availableEndpoints.find((ep) => ep.nodeId === transitId)?.endpoint,
          ).href;
          const startTime = new Date().getTime();
          const ipStr = await rawRequest(url, { parseJson: false, timeout: PROBE_TIME_OUT * 1000 });
          const testingTime = new Date().getTime() - startTime;
          if (regs.ipv4.test(ipStr) || regs.ipv6.test(ipStr)) {
            responseData = {
              loading: false,
              testingTime,
              status: 'Available',
              ip: ipStr,
            };
          } else {
            responseData = {
              loading: false,
              error: ipStr || '未获取到出口IP',
              status: 'Unavailable',
            };
          }
        } catch (e: any) {
          responseData = {
            loading: false,
            error: e.message || '不可用',
            status: 'Unavailable',
          };
        }
      }
      // 汇报测试结果
      if (ipId) {
        try {
          await this.props.requestAgent.request(`/api/ip/${ipId}/probe/directResult`, {
            method: 'PUT',
            data: {
              success: responseData.status === 'Available',
              error: responseData.error,
              remoteIp: responseData.ip,
              testingTime: responseData.testingTime,
            },
            teamId: this.props.teamId,
          });
        } catch (e) {}
      }
      return responseData;
    }
    try {
      // 接入点到代理IP测试
      const proxyProbeWithRemoteIp = await this.props.requestAgent.request(
        ipId ? `/api/ip/${ipId}/probe` : `/api/poolip/${ippIpId}/probe`,
        {
          params: {
            transitId,
          },
          teamId: this.props.teamId,
        },
      );
      if (proxyProbeWithRemoteIp.reachable && proxyProbeWithRemoteIp.success) {
        const { testingTime, code } = proxyProbeWithRemoteIp;
        responseData = {
          testingTime,
          code,
          loading: false,
          status: 'Available',
          success: true,
        };
      } else {
        const output = proxyProbeWithRemoteIp.output
          ? `${proxyProbeWithRemoteIp.output}`
          : proxyProbeWithRemoteIp.error;
        responseData = {
          loading: false,
          error: output,
          code: proxyProbeWithRemoteIp.code,
          status: 'Unavailable',
          success: false,
        };
      }
    } catch (e: any) {
      const output = e?.data?.output ? `${e?.data?.output}` : e.data?.error || e.message;
      responseData = {
        loading: false,
        error: output,
        status: 'Unavailable',
        success: false,
      };
    }
    return responseData;
  }

  /**
   * 刷新客户端直连IP和接入点到IP的延迟
   */
  async refreshIpTransitPing(transitIds: number[] = []) {
    const promiseArr: Promise<any>[] = [];
    [0].concat(this.props.transitList.map((t) => t.id!)).forEach((transitId) => {
      if (transitIds.length === 0 || transitIds.includes(transitId)) {
        promiseArr.push(
          this.pingIpTransit(transitId).then((res) => {
            const dto = this.ipTransitDtoList.find((dto) => dto.transitId === transitId);
            return {
              transitId,
              ...dto,
              ..._.pick(res, ['status', 'testingTime']),
              remoteIp: res.ip,
              probeError: res.error,
              probeCode: res.code,
              loading: false,
            };
          }),
        );
      }
    });
    const pingResults: API.IpTransitDto[] = await Promise.all(promiseArr);
    pingResults.forEach((pingResult) => {
      const findIdx = this.ipTransitDtoList.findIndex(
        (dto) => dto.transitId === pingResult.transitId,
      );
      if (findIdx === -1) {
        this.ipTransitDtoList.push(pingResult);
      } else {
        this.ipTransitDtoList[findIdx] = {
          ...this.ipTransitDtoList[findIdx],
          ...pingResult,
        };
      }
    });
    return this.ipTransitDtoList;
  }

  /**
   * 当是 mode==HuaYoung 时，用来切换线路
   * @param targetEndpointVo
   * @param manual
   */
  async switchTransit(targetEndpointVo: API.TunnelEndpointVo, manual = true) {
    try {
      this.isSwitchingTransit = true;
      await this.createProxyIpTunnel(targetEndpointVo);
      this.lastSwitchTransitTimestamp = Date.now();
      getChromium({ sessionId: this.props.sessionId })?.triggerPacScriptChanged();
      setTimeout(() => {
        getChromium({ sessionId: this.props.sessionId })?.triggerNetErrorPageReload();
      }, 1000);
    } catch (e) {
      throw e;
    } finally {
      this.isSwitchingTransit = false;
    }
  }

  async doAutoSwitchTransit(skipRefreshToken = false, retryCount = 0) {
    if (this.closed) {
      return;
    }

    // 防止无限递归，最大重试3次
    if (retryCount >= 3) {
      logger.error(
        '[TUNNEL] doAutoSwitchTransit: Max retry attempts reached, stopping auto switch',
      );
      return;
    }
    // 当前接入方式降权，尝试其他接入方式
    await this.setIpEndpointWeight();
    if (!skipRefreshToken) {
      await this.refreshToken();
    }
    let _availableEndpoints = [...this.availableEndpoints];
    if (_availableEndpoints.some((vo) => vo.tunnelType === 'localFrontend')) {
      if (!this.localFrontendProxyUrl) {
        _availableEndpoints = _availableEndpoints.filter((vo) => vo.tunnelType !== 'localFrontend');
      }
    }
    const nextEndpointVo = _availableEndpoints.find(
      (ep) =>
        ![...this.autoSwitchEndpointsRecord].some(
          (vo) =>
            vo.tunnelType === ep.tunnelType && vo.nodeId === ep.nodeId && vo.proxyId === ep.proxyId,
        ),
    );
    if (nextEndpointVo) {
      this.autoSwitchEndpointsRecord.push(nextEndpointVo);
      try {
        logger.info(
          `[TUNNEL] switching transit (${this.currentEndpoint.tunnelType}@${
            this.currentEndpoint.groupId
          }:${this.currentEndpoint.nodeId}${
            this.currentEndpoint.proxyId ? `:${this.currentEndpoint.proxyId}` : ''
          } -> ${nextEndpointVo.tunnelType}@${nextEndpointVo.groupId}:${nextEndpointVo.nodeId}${
            nextEndpointVo.proxyId ? `:${nextEndpointVo.proxyId}` : ''
          })`,
        );
        await this.switchTransit(nextEndpointVo, false);
        this.cleanAutoSwitchEndpointsRecordCount = 0;
      } catch (e: any) {
        logger.error(
          `[TUNNEL] switch transit failed (${this.currentEndpoint.tunnelType}@${
            this.currentEndpoint.groupId
          }:${this.currentEndpoint.nodeId}${
            this.currentEndpoint.proxyId ? `:${this.currentEndpoint.proxyId}` : ''
          } -> ${nextEndpointVo.tunnelType}@${nextEndpointVo.groupId}:${nextEndpointVo.nodeId}${
            nextEndpointVo.proxyId ? `:${nextEndpointVo.proxyId}` : ''
          })`,
          e.message,
        );

        // 添加延迟避免过于频繁的重试，使用指数退避策略
        const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);
        logger.warn(
          `[TUNNEL] doAutoSwitchTransit: Retrying in ${delay}ms (attempt ${retryCount + 1}/3)`,
        );
        await new Promise((resolve) => setTimeout(resolve, delay));
        await this.doAutoSwitchTransit(skipRefreshToken, retryCount + 1);
      }
      // 所有接入方式都尝试过一遍后，清空记录
      if (
        this.cleanAutoSwitchEndpointsRecordCount < 3 &&
        _availableEndpoints.every((ep) =>
          [...this.autoSwitchEndpointsRecord].some(
            (vo) =>
              vo.tunnelType === ep.tunnelType &&
              vo.nodeId === ep.nodeId &&
              vo.proxyId === ep.proxyId,
          ),
        )
      ) {
        this.cleanAutoSwitchEndpointsRecordCount++;
        this.autoSwitchEndpointsRecord = [this.currentEndpoint];
      }
    }
  }

  async autoSwitchTransit() {
    if (this.isSwitchingTransit) {
      return;
    }
    if (Date.now() - this.lastSwitchTransitTimestamp < 60 * 1000) {
      return;
    }
    await this.doAutoSwitchTransit();
  }

  async removeTransitByIds(transitIds: number[]) {
    if (!_.isArray(transitIds)) return;
    await this.refreshToken();
    logger.info(`[TUNNEL] try remove transits (${transitIds.join(',')})`);
    const _endpoints = [...this.availableEndpoints].filter((v) => !transitIds.includes(v.nodeId!));
    if (_endpoints.length === 0) {
      logger.info('[TUNNEL] no other available transit, skip remove transit');
      return;
    }
    this.availableEndpoints = _endpoints;
    if (
      this.currentEndpoint &&
      !this.availableEndpoints.some((v) => v.nodeId === this.currentEndpoint.nodeId)
    ) {
      // 当前接入点被移除，切换到其他接入点
      for (const endpoint of this.availableEndpoints) {
        try {
          await this.switchTransit(endpoint);
          break;
        } catch (e) {
          logger.info(
            `[TUNNEL] connect endpoint failed (${endpoint.tunnelType}@${endpoint.groupId}:${
              endpoint.nodeId
            }${endpoint.proxyId ? `:${endpoint.proxyId}` : ''}，sessionId: ${
              this.props.sessionId
            })`,
          );
        }
      }
    }
  }

  pacRule(): string {
    return this.impl.pacRule();
  }

  getImpl() {
    return this.impl;
  }

  close() {
    this.closed = true;
    clearInterval(this.refreshTokenTimer);
    return this.impl.close();
  }

  authInfo(): {
    password: string;
    port: number;
    proxyType: string;
    host: string;
    username: string;
  } {
    return this.impl.authInfo();
  }
}
