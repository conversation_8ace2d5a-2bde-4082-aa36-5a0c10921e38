import dns from 'dns';
import net, { Server, Socket } from 'net';
import logger from '@e/services/logger';

import { getHostname, ITunnel } from '@e/utils/tunnels/index';

export class LocalSocksTunnel extends ITunnel {
  socks5Port: number;
  server?: Server;
  webServerPortal: number;

  constructor() {
    // @ts-ignore
    super({});
    this.socks5Port = 0;
    this.webServerPortal = 80;
  }

  pacRule(): string {
    return `SOCKS5 127.0.0.1:${this.socks5Port}`;
  }

  async init() {
    return new Promise(async (resolve, reject) => {
      try {
        const socks5Server = net.createServer((socket) => {
          let remoteSocks: any = null;
          socket.setNoDelay(true);
          socket.once('data', (data) => {
            if (!data || data[0] !== 0x05)
              return socket.destroy(new Error(`${data[0]} is not supported`));
            socket.write(Buffer.from([5, 0]), (err) => {
              if (err) {
                socket.destroy();
              }
              socket.once('data', async (data) => {
                if (data.length < 7 || data[1] !== 0x01) return socket.destroy(); // 只支持 CONNECT
                try {
                  const ATYP = data[3]; // 目标服务器地址类型
                  if (ATYP !== 1 && ATYP !== 3) {
                    return socket.destroy();
                  }
                  let remoteAddr;
                  let remotePort = data.slice(data.length - 2).readUInt16BE(0); //最后两位为端口值
                  let copyBuf = Buffer.allocUnsafe(data.length);
                  data.copy(copyBuf);
                  if (ATYP === 1) {
                    // 0x01 IP V4地址
                    remoteAddr = getHostname(data.slice(4, 8));
                  } else {
                    //0x03 域名地址
                    // @ts-ignore
                    let len = parseInt(data[4], 10);
                    remoteAddr = data.slice(5, 5 + len).toString('utf8');
                  }
                  remoteSocks = await this._connect(remoteAddr, remotePort, copyBuf, socket);
                } catch (e) {
                  logger.error('[TUNNEL] socket trans to websocket Error: ', e);
                }
              });
            });
          });

          //监听错误
          socket.on('error', (err: any) => {
            if (err.code !== 'ECONNRESET') {
              logger.error('[TUNNEL] socket（Local） Error: ', err);
            }
            socket.destroyed || socket.destroy();
            remoteSocks && (remoteSocks.destroyed || remoteSocks.destroy());
          });

          socket.on('close', () => {
            socket.destroyed || socket.destroy();
            remoteSocks && (remoteSocks.destroyed || remoteSocks.destroy());
          });
        });

        socks5Server.listen(0, this.listenHost, () => {
          const address = socks5Server.address();
          if (address && typeof address === 'object') {
            this.socks5Port = address.port;
            logger.verbose(`[TUNNEL] SOCKS server(local) listening on port ${this.socks5Port}`);
            resolve(this.socks5Port);
          } else {
            reject(new Error('[TUNNEL] parse server address failed'));
          }
        });

        this.server = socks5Server;
      } catch (e) {
        reject(e);
      }
    });
  }

  _connect(host: string, port: number, data: Buffer, sock: Socket) {
    if (['testsafebrowsing.appspot.com'].includes(host)) {
      sock.destroy();
      return;
    }
    const socket = new net.Socket();
    let _port = port;
    const isBrowserPage = [
      'szdamai.local',
      'szdamai.tab',
      'ggbrowser.local',
      'ggbrowser.tab',
    ].includes(host);
    if (isBrowserPage) {
      _port = this.webServerPortal;
    }
    return new Promise((resolve) => {
      if (isBrowserPage) {
        resolve('127.0.0.1');
      } else {
        dns.lookup(host, (err, address) => {
          if (!err) {
            resolve(address);
          } else {
            resolve(host);
          }
        });
      }
    }).then((address) => {
      if (typeof address !== 'string') {
        socket.destroy();
        return;
      }
      try {
        socket.connect(_port, address, () => {
          data[1] = 0x00;
          if (sock.writable) {
            sock.write(data);
            sock.pipe(socket);
            socket.pipe(sock);
          }
        });
        socket.on('error', (err) => {
          logger.error('[TUNNEL] socket（Local） Error: ', host, err.message);
          socket.destroy();
          sock.destroy();
        });
        socket.on('close', () => {
          sock.destroyed || sock.destroy();
          socket.destroyed || socket.destroy();
        });
        return socket;
      } catch (e) {
        sock.destroy();
        logger.error('[TUNNEL] socket（Local） connect Error: ', e);
      }
    });
  }

  setWebserverPort(port: number) {
    this.webServerPortal = port;
  }

  close() {
    // 关闭 socks5 服务
    this.server && this.server.close();
  }
}
