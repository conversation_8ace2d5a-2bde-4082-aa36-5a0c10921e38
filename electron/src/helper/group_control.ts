import { globalShortcut } from 'electron';
import { ActionResponse, GCHelper, hy_actions } from '@e/helper/index';
import { getChromium, getWindowSyncToolboxWin } from '@e/utils/window';
import { Chromium } from '@e/utils/chromium';
import { dispatchMsg } from '@e/utils/ipc';
import db from '@e/components/db';
import logger from "@e/services/logger";

export class GroupController {
  helper: GCHelper;

  //是否正在群控
  processing: boolean = false;
  master?: Chromium;
  follows: Chromium[] = [];
  browserWindowSizeMap: Record<number, { width?: number; height?: number }> = {};
  skipBrowserWindowSizeCheck: boolean = false;
  paused = false;
  // 用于存储每次群控的输入文本历史
  sendKeysLogs: {
    startTime: number;
    endTime?: number;
    records: { sessionId: number; shopName: string; logs: string[] }[];
  }[] = [];
  lastSizeChange = 0;

  //当参与群控的任何一个会话窗口的大小发生变化时，都会调用该回调函数
  private _windowResizeCallback?: (sessionId: number) => void;
  set windowResizeCallback(callback: (sessionId: number) => void) {
    this._windowResizeCallback = callback;
  }

  constructor(helper: GCHelper) {
    this.helper = helper;
    this.helper.listen(hy_actions.EVENT_GC_MASTER_CLOSED, (data: any) => {
      this.onStopControl();
      dispatchMsg(hy_actions.EVENT_GC_DETAIL_UPDATE);
    });
    this.helper.listen(hy_actions.EVENT_GC_WIN_SIZE_CHG, (data: any) => {
      if (Date.now() - this.lastSizeChange > 8000) {
        this._windowResizeCallback?.(0);
        this.lastSizeChange = Date.now();
      }
    });
    this.handleShortcut = this.handleShortcut.bind(this);
    this.changePauseState = this.changePauseState.bind(this);
    this.handleMasterClosed = this.handleMasterClosed.bind(this);
  }

  isProcessing(): boolean {
    return this.processing;
  }

  get detail() {
    return {
      processing: this.processing,
      master: this.master?.sessionId,
      follows: this.follows.map((f) => f.sessionId),
      paused: this.paused,
    };
  }

  get isPaused() {
    return this.paused;
  }

  handleShortcut() {
    this.paused = !this.paused;
    this.changePauseState(this.paused);
  }

  changePauseState(paused: boolean) {
    const sysPres = db.getSysPres();
    this.changeMasterColor(paused ? sysPres.winSyncPauseColor : sysPres.winSyncRunningColor);
    dispatchMsg(hy_actions.EVENT_GC_PAUSED_STATE_UPDATE, { paused }, getWindowSyncToolboxWin());
    if (paused) {
      return this.helper.sendAction({
        name: hy_actions.ACTION_GC_PAUSE,
      });
    } else {
      return this.helper.sendAction({
        name: hy_actions.ACTION_GC_RESUME,
      });
    }
  }

  async sendKeys(sessions: any, interval = 0) {
    let data = [];
    for (let sessionId in sessions) {
      //@ts-ignore
      let chromium = getChromium({ sessionId: sessionId });
      let text = sessions[sessionId];
      if (chromium && !!text) {
        data.push({
          title: chromium.browserTitle,
          text: sessions[sessionId],
        });
        const sendKeysLog = this.sendKeysLogs[this.sendKeysLogs.length - 1];
        if (sendKeysLog) {
          const record = sendKeysLog.records.find((r) => String(r.sessionId) === sessionId);
          if (record) {
            record.logs.push(text);
          }
        }
      }
    }
    if (data.length > 0) {
      // const { winSyncEvtDelay = 100 } = db.getDb().get('sysPres').value();
      if (interval > 0) {
        for (const datum of data) {
          this.helper.sendAction({
            name: hy_actions.ACTION_WIN_SEND_KEYS,
            interval: 0,
            sessions: [datum],
          })
          await new Promise((resolve) => setTimeout(resolve, interval));
        }
      } else {
        this.helper.sendAction({
          name: hy_actions.ACTION_WIN_SEND_KEYS,
          interval: 0,
          sessions: data,
        });
      }
    }
  }

  handleMasterClosed() {
    this.stopGroupControl();
  }

  /**
   * 开始群控
   * @param master
   * @param follows
   * @param renderMasterMark 是否在主控窗口周围画个红框
   */
  async startGroupControl(master: number, follows: number[], renderMasterMark: boolean) {
    if (!this.processing) {
      this.lastSizeChange = Date.now();
      this.master = this.findChromium(master);
      this.follows = [];
      for (let follow of follows) {
        this.follows.push(this.findChromium(follow)!);
      }
      this.processing = true;
      let controlStr = `'${this.master!.browserTitle}',${this.follows
        .map((f) => "'" + f.browserTitle + "'")
        .join(',')}`;
      console.log(controlStr);
      let resp = this.sendStartGroupControl(
        this.master!.browserTitle,
        this.follows.map((f) => f.browserTitle),
        renderMasterMark,
      );
      this.helper.sendAction({
        name: hy_actions.ACTION_GC_RESUME,
      });
      try {
        await resp.get();
        const sysPres = db.getSysPres();
        globalShortcut.register(sysPres.winSyncPauseKey, this.handleShortcut);
        this.sendKeysLogs.push({
          startTime: Date.now(),
          records: [this.master, ...this.follows].map((item) => {
            return {
              sessionId: item?.sessionId ?? 0,
              shopName: item?.shopInfo?.name ?? '',
              logs: [],
            };
          }),
        });
      } catch (e: any) {
        this.onStopControl();
        throw new Error(e.message || String(e));
      }
    }
  }

  /**
   * 停止群控
   */
  async stopGroupControl() {
    this.browserWindowSizeMap = {};
    this.onStopControl();
    let resp = this.helper.send(hy_actions.ACTION_GC_STOP);
    await resp.get();
  }

  /**
   * 同步标签页
   */
  async syncTabs() {
    const masterTabsCount = this.master?.tabUrls.length ?? 1;
    this.follows.forEach(async (f) => {
      f.wsDispatcher.dispatcher(
        JSON.stringify({
          action: 'set-tabs',
          data: this.master?.tabUrls ?? [],
        }),
      );
    });
  }

  /**
   * 更改主控窗口边框颜色
   * @param newColor #FF0000 格式的颜色值，包含’#‘号
   */
  async changeMasterColor(newColor: string) {
    newColor = (newColor || '#FF0000').toUpperCase();
    if (/#[A-F0-9]{6}/.test(newColor)) {
      return this.helper.sendAction({
        name: hy_actions.ACTION_GC_CG_COLOR,
        color: newColor,
      });
    }
  }

  /**
   * 向群控添加一个新的窗口
   * @param sessionId
   */
  async addFollowWindow(sessionId: number) {
    if (this.processing) {
      let follow = this.findChromium(sessionId);
      if (follow) {
        let response = await this.helper.sendAction({
          name: hy_actions.ACTION_GC_ADD_FOLLOW,
          follow: follow!.browserTitle,
        });
        let ret = await response.get();
        logger.log('addFollowWindow, ret = ' + JSON.stringify(ret));
        this.follows.push(follow);
        return response;
      }
    }
  }

  /**
   * 从群控中移除一个窗口
   * @param sessionId
   */
  async removeFollowWindow(sessionId: number) {
    if (this.processing) {
      let follow = this.findChromium(sessionId);
      if (follow) {
        let response = this.helper.sendAction({
          name: hy_actions.ACTION_GC_REMOVE_FOLLOW,
          follow: follow!.browserTitle,
        });
        let ret = await response.get();
        logger.log('removeFollowWindow, ret = ' + JSON.stringify(ret));
        this.follows = this.follows.filter((item) => item !== follow);
        return response;
      }
    }
  }

  private sendStartGroupControl(
    master: string,
    follows: string[],
    renderMasterMark: boolean,
  ): ActionResponse {
    const sysPres = db.getSysPres();
    let color = (sysPres.winSyncRunningColor || '#FF0000').toUpperCase(); //主控边框颜色，可以不传，但不能格式传错
    return this.helper.sendAction({
      name: hy_actions.ACTION_GC_START,
      master,
      follows,
      render_master_mark: renderMasterMark,
      color,
    });
  }

  private findChromium(sessionId: number): Chromium | undefined {
    let chromium = getChromium({ sessionId: sessionId });
    if (chromium) {
      return chromium;
    } else {
      throw new Error(`找不到sessionId为${sessionId}的会话窗口`);
    }
  }

  private onStopControl() {
    const sysPres = db.getSysPres();
    globalShortcut.unregister(sysPres.winSyncPauseKey);
    this.processing = false;
    this.master = undefined;
    this.follows = [];
    this.paused = false;
    const sendKeysLog = this.sendKeysLogs[this.sendKeysLogs.length - 1];
    if (sendKeysLog && !sendKeysLog.endTime) {
      sendKeysLog.endTime = Date.now();
    }
    dispatchMsg('browser.sync.list.update');
    if (getWindowSyncToolboxWin()) {
      dispatchMsg(hy_actions.EVENT_GC_DETAIL_UPDATE, {}, getWindowSyncToolboxWin());
    }
  }

  getSendKeysLogs() {
    return this.sendKeysLogs;
  }
}
