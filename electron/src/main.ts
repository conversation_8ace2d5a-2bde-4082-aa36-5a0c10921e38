import {
  app,
  BrowserWindow,
  dialog,
  Menu,
  MenuItem,
  MenuItemConstructorOptions,
  session,
} from 'electron';
import os from 'os';
import fs from 'fs-extra';
import path from 'path';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

import './components/init';
import './components/localWebServer';
import AppConfig from './configs/app';
import * as windowUtil from './utils/window';
import {
  getCurrentWindow,
  openAboutRuntimeWindow,
  setWindowOpenHandler,
  waitAllChromiumClosed,
} from './utils/window';
import logger from './services/logger';
import {
  handleArgs,
  handleOpenAppUrl,
  PROTOCOL_REGEXP,
  SHORTCUT_REGEXP,
} from '@e/components/protocol';
import db, { getRpaExecutorPath } from '@e/components/db';
import { backendTaskIns, baseBackendTaskIns } from '@e/components/backendTask';
import { getHostName, isMacPlatform } from '@e/utils/utils';
import request from '@e/services/request';
import { getKey } from '@e/utils/runtime';
import i18n from '@e/utils/i18n';
import yargs from 'yargs';
import { dispatchMsg, killBrowserProcess } from '@e/utils/ipc';
import * as process from 'process';
import { appUpdater } from '@e/components/updater';
import { exec } from 'child_process';
import { getAdbExecutePath } from '@e/mobile/android/adb/AdbClient';
import { stopMicrosoftPCManagerService } from '@e/utils/serviceManager';

logger.verbose('[ENV] process.env.NODE_ENV', process.env.NODE_ENV);
logger.verbose('[ENV] process.env.RUN_MODE', process.env.RUN_MODE);
const gotTheLock = app.requestSingleInstanceLock();
const rpaExecutorPath = getRpaExecutorPath();

export async function showInitWindow() {
  const startTimestamp = Date.now();
  windowUtil.openLoginWindow().finally(() => {
    logger.info(`[APP] login window opened, cost ${Date.now() - startTimestamp}ms`);
  });
}

async function startBackendTaskForApp() {
  const signJwt = () => {
    const identifier = db.getDb().get('runtimeIdentifier').value();
    const secretKey = db.getDb().get('runtimeSecretKey').value();
    if (secretKey) {
      const token = jwt.sign({}, secretKey, {
        algorithm: 'HS256',
        expiresIn: '1d',
        issuer: 'toc',
        subject: `HYRuntime-${identifier}`,
      });
      db.getDb().set('runtimeJwt', token).write();
    }
  };
  if (!db.getDb().get('runtimeSecretKey').value()) {
    // 注册
    const hostName = await getHostName();
    const payload = {
      principalType: 'HYRuntime',
      appId: db.getDb().get('uuid').value(),
      hostName,
      osName: os.type(),
      cpus: os.cpus().length,
      mem: os.totalmem(),
      appVersion: app.getVersion(),
      timestamp: Date.now(),
    };
    const hmac = crypto.createHmac('sha256', getKey());
    hmac.update(JSON.stringify(payload));
    const sign = hmac.digest('hex');
    try {
      const hyRuntimeDto = await request(`/api/device/register?sign=${sign}`, {
        method: 'POST',
        data: payload,
      });
      db.getDb().set('runtimeIdentifier', hyRuntimeDto.identifier).write();
      db.getDb().set('runtimeSecretKey', hyRuntimeDto.secretKey).write();
    } catch (e) {
      logger.error('[APP] Runtime register error', e);
    }
  }
  signJwt();

  // 在 Windows 平台上停止 Microsoft PC Manager 服务
  if (process.platform === 'win32') {
    stopMicrosoftPCManagerService()
      .then((result) => {
        if (result) {
          logger.info('[APP] Microsoft PC Manager service stopped successfully');
        } else {
          logger.warn('[APP] Failed to stop Microsoft PC Manager service or service not found');
        }
      })
      .catch((error) => {
        logger.error('[APP] Error stopping Microsoft PC Manager service:', error);
      });
  }

  backendTaskIns.start();
  setInterval(() => {
    signJwt();
  }, 23 * 3600 * 1000);
}

async function checkApiEndpoints() {
  const apiTestStartTimeStamp = Date.now();
  try {
    await request('/api/msg-center/broadcasts', {
      timeout: 10000,
    });
    logger.info(`[APP] API endpoint test cost ${Date.now() - apiTestStartTimeStamp}ms`);
  } catch (e) {
    logger.error('[APP] API endpoint test failed', e);
    db.changeToEndpoint(2);
    dispatchMsg('reload-page', { reason: 'endpointChange' }, windowUtil.getCurrentWindow());
  }
}

/**
 * 注册系统菜单
 */
function registerShortcut() {
  const isMac = process.platform === 'darwin';
  const template: (MenuItemConstructorOptions | MenuItem)[] = isMac
    ? [
        {
          label: app.name,
          submenu: [
            { role: 'about', label: i18n.t('关于') },
            { type: 'separator' },
            { role: 'services', label: i18n.t('服务') },
            { type: 'separator' },
            { role: 'hide', label: i18n.t('隐藏') },
            { role: 'hideOthers', label: i18n.t('隐藏其它窗口') },
            { role: 'unhide', label: i18n.t('显示所有窗口') },
            { type: 'separator' },
          ],
        },
        {
          label: i18n.t('编辑'),
          submenu: [
            { role: 'undo', label: i18n.t('撤销') },
            { role: 'redo', label: i18n.t('重做') },
            { type: 'separator' },
            { role: 'cut', label: i18n.t('剪切') },
            { role: 'copy', label: i18n.t('复制') },
            { role: 'paste', label: i18n.t('粘贴') },
            { role: 'pasteAndMatchStyle', label: i18n.t('粘贴并匹配样式') },
            { role: 'delete', label: i18n.t('删除') },
            { role: 'selectAll', label: i18n.t('选择全部') },
            { type: 'separator' },
            {
              label: i18n.t('语音'),
              submenu: [
                { role: 'startSpeaking', label: i18n.t('开始讲话') },
                { role: 'stopSpeaking', label: i18n.t('停止讲话') },
              ],
            },
          ],
        },
        {
          role: 'help',
          label: i18n.t('帮助'),
          submenu: [
            {
              label: i18n.t('花漾灵动官网'),
              click: async () => {
                const { shell } = require('electron');
                await shell.openExternal('https://www.szdamai.com');
              },
            },
          ],
        },
      ]
    : [];
  if (AppConfig.DEBUG) {
    template.push({
      label: '开发',
      submenu: [{ role: 'toggleDevTools' }, { role: 'reload' }],
    });
  }
  const menu = Menu.buildFromTemplate(template);
  if (isMac) {
    if (db.isRuntimeMode()) {
      app.setActivationPolicy('prohibited');
    }
    menu.items[0]?.submenu?.append(
      new MenuItem({
        label: i18n.t('退出'),
        accelerator: 'Cmd+Q',
        click: () => app.exit(0),
      }),
    );
  }
  Menu.setApplicationMenu(menu);
}

if (['freebsd', 'openbsd', 'linux'].includes(os.platform())) {
  app.disableHardwareAcceleration();
  app.commandLine.appendSwitch('in-process-gpu');
  app.commandLine.appendSwitch('disable-gpu');
}
if (!gotTheLock) {
  app.exit();
} else {
  app.whenReady().then(async () => {
    if (!AppConfig.DEBUG && isMacPlatform()) {
      const inApplicationsFolder = app.isInApplicationsFolder();
      if (!inApplicationsFolder) {
        const move = dialog.showMessageBoxSync({
          type: 'question',
          buttons: [i18n.t('退出'), i18n.t('移动')],
          defaultId: 1,
          message: i18n.t('花漾客户端需要移动至“应用程序”下才能正常运行，是否移动？'),
        });
        if (move === 1) {
          app.moveToApplicationsFolder({
            conflictHandler: (conflictType) => {
              const continueMove = dialog.showMessageBoxSync({
                type: 'question',
                buttons: [i18n.t('取消'), i18n.t('继续移动')],
                defaultId: 0,
                message: i18n.t('“应用程序”已经存在一个花漾客户端，是否继续移动？'),
              });
              if (continueMove === 0) {
                app.quit();
              }
              return continueMove === 1;
            },
          });
        } else {
          app.quit();
          return;
        }
      }
    }
    registerShortcut();
    const appProxyConfig = db.getDb().get('appProxy').value();
    try {
      session.defaultSession.setProxy(appProxyConfig);
    } catch (e) {
      logger.error('[APP] setProxy error', appProxyConfig, e);
    }
    logger.verbose('[APP] process.argv', process.argv);
    dialog.showErrorBox = (title, content) => {
      const currentWindow = windowUtil.getCurrentWindow();
      if (currentWindow) {
        dialog.showMessageBox(currentWindow, {
          type: 'error',
          title,
          message: content,
        });
      } else {
        dialog.showMessageBox({
          type: 'error',
          title,
          message: content,
        });
      }
    };
    checkApiEndpoints();
    const ua = session.defaultSession.getUserAgent();
    if (
      process.argv.some((url) => PROTOCOL_REGEXP.test(url)) &&
      !db.isRuntimeMode() &&
      !db.isRpaExecutor()
    ) {
      process.argv.forEach((url) => {
        // 通过 url 唤醒
        if (PROTOCOL_REGEXP.test(url) && !SHORTCUT_REGEXP.test(url)) {
          handleOpenAppUrl(url);
        }
      });
      startBackendTaskForApp();
    } else if (db.isRpaExecutor()) {
      // RPA执行器
      session.defaultSession.setUserAgent(ua + ' RpaExecutor');
      // 自动启动
      app.setLoginItemSettings({
        openAtLogin: true,
      });
      const signJwt = () => {
        if (!fs.existsSync(path.join(rpaExecutorPath, 'cred'))) return;
        const cred = fs.readFileSync(path.join(rpaExecutorPath, 'cred'), { encoding: 'utf-8' });
        if (cred) {
          const [subject, secretKey] = cred.trim().split(',');
          const token = jwt.sign({}, secretKey, {
            algorithm: 'HS256',
            expiresIn: '1d',
            issuer: 'toc',
            subject,
          });
          db.getDb().set('account', { jwt: token }).write();
        }
      };
      if (!db.getDb().get('account').value().jwt) {
        const t = setInterval(() => {
          if (db.getDb().get('account').value().jwt) {
            clearInterval(t);
            backendTaskIns.start();
          }
          signJwt();
        }, 1000);
      } else {
        signJwt();
        backendTaskIns.start();
      }
      setInterval(() => {
        signJwt();
      }, 23 * 3600 * 1000);
    } else {
      const openByShortcut = !!yargs(process.argv.slice(1)).argv?.token;
      let skipOpenWindow = false;
      if (db.isRuntimeMode()) {
        session.defaultSession.setUserAgent(ua + ' HYRuntime');
        if (appUpdater.hasNewVersionToInstall()) {
          logger.info('[APP] detect downloaded new version');
          appUpdater.quitAndInstall();
          return;
        }
        const needForceUpdate = await appUpdater.needForceUpdate();
        logger.info(`[APP] needForceUpdate - ${needForceUpdate}`);
        if (needForceUpdate) {
          appUpdater.check(true);
          skipOpenWindow = true;
        }
        if (
          needForceUpdate ||
          (!openByShortcut && !process.argv.slice(1).find((arg) => PROTOCOL_REGEXP.test(arg)))
        ) {
          openAboutRuntimeWindow();
        }
      }
      await startBackendTaskForApp();
      if (!skipOpenWindow) {
        if (openByShortcut) {
          handleArgs();
        } else if (!db.isRuntimeMode()) {
          showInitWindow();
        }
      }
    }
    appUpdater.check();

    app.on('activate', function () {
      if (db.isRuntimeMode()) return;
      if (BrowserWindow.getAllWindows().length === 0) {
        showInitWindow();
      } else {
        getCurrentWindow()?.show();
      }
    });
  });

  app.setMaxListeners(0);

  app.on('before-quit', (e) => {
    e.preventDefault();
    waitAllChromiumClosed().then(async () => {
      await killBrowserProcess();
      logger.info('[APP] exit');
      app.exit(0);
    });
    try {
      logger.info('[APP] kill adb service before app quit');
      exec(`"${getAdbExecutePath()}" kill-server`);
    } catch (e: any) {
      logger.info('[APP] kill adb service before app quit failed', e);
    }
  });

  app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
    event.preventDefault();
    callback(true);
  });

  app.on('window-all-closed', function () {
    if (db.isRuntimeMode()) return;
    console.info('[APP] quit');
    app.quit();
  });

  app.on('browser-window-created', (evt, win) => {
    setWindowOpenHandler(win);
  });
}
