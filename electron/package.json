{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "花漾灵动客户端程序", "author": "深圳市云上悦动科技有限公司 <<EMAIL>>", "homepage": "https://www.szdamai.com/", "main": "main.js", "dependencies": {"@koa/cors": "^4.0.0", "@koa/router": "^10.0.0", "acorn": "^8.8.2", "ali-oss": "^6.17.0", "archiver": "^5.3.1", "axios": "^0.27.2", "better-sqlite3": "^8.4.0", "check-disk-space": "^3.4.0", "cheerio": "^1.0.0-rc.12", "chokidar": "^3.5.3", "donkey-puppeteer-core": "^21.7.2", "electron-auto-launch": "^5.0.7", "electron-log": "^4.3.5", "electron-updater": "^5.2.1", "eml-format": "^0.6.1", "eml-parser": "^2.0.3", "escodegen": "^2.0.0", "estraverse": "^5.3.0", "form-data": "^4.0.0", "fast-folder-size": "^2.2.0", "fs-extra": "^10.0.0", "http-parser-js": "^0.5.6", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^5.0.1", "hy_excelize": "^4.4.10", "imapflow": "^1.0.148", "jimp": "^0.22.12", "js-cookie": "^3.0.1", "js-yaml": "^4.1.0", "jsonwebtoken": "^8.5.1", "koa": "^2.13.1", "koa-bodyparser": "^4.3.0", "koa-static": "^5.0.0", "lodash": "^4.17.11", "lowdb": "^1.0.0", "moment-timezone": "^0.5.33", "ms-wmic": "^1.0.4", "node-machine-id": "^1.1.12", "nodejs-file-downloader": "^4.7.4", "nodemailer": "^6.9.1", "papaparse": "^5.3.2", "ps-list": "7.2.0", "puppeteer-extra": "^3.3.4", "socket.io-client": "^4.5.2", "socks": "^2.7.1", "socks-proxy-agent": "^7.0.0", "ssh2": "^1.15.0", "ua-parser-js": "^0.7.28", "vm2": "^3.9.10", "wildcard-match": "^5.1.2", "winattr": "^3.0.0", "ws": "^7.5.2", "yargs": "^17.7.2", "detect-file-encoding-and-language": "^2.4.0", "@dead50f7/adbkit": "^2.11.4", "appium": "^2.11.3", "appium-adb": "12.5.2", "appium-uiautomator2-driver": "^3.7.7", "webdriverio": "^8.38.0", "lru-cache": "^10.2.2", "node-pop3": "^0.9.0", "fast-xml-parser": "^5.0.8", "@langchain/openai": "^0.0.10", "langchain": "^0.1.0", "web-streams-polyfill": "^4.1.0", "turndown": "^7.2.0", "pureimage": "^0.4.18"}, "devDependencies": {"patch-package": "^6.4.2"}, "scripts": {"postinstall": "patch-package"}, "version": "1.0.8"}