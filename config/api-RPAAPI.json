{"openapi": "3.0.3", "info": {"title": "RPA API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "RPA云端实例 API", "description": "Rpa Cloud Instance Controller"}, {"name": "RPA流程中心task openai相关api", "description": "Rpa Task Ai Controller"}, {"name": "RPA流程中心task相关api", "description": "Rpa Task Controller"}, {"name": "RPA流程中心流程db相关api", "description": "Rpa Task Db Controller"}, {"name": "RPA流程中心相关api", "description": "Rpa Controller"}, {"name": "RPA流程中心费用相关api", "description": "Rpa Fees Controller"}, {"name": "RPA流程中心验证码节点相关api", "description": "Rap Task Captcha Controller"}, {"name": "RPA流程市场相关api", "description": "Rpa Market Controller"}, {"name": "RPA流程计划相关api", "description": "Rpa Plan Controller"}, {"name": "RPA触发器api", "description": "<PERSON><PERSON> Trigger Controller"}, {"name": "RPA计划分组相关api", "description": "Rpa Plan Group Controller"}, {"name": "RPA费用中心:支付|购买相关API", "description": "Rpa Payment Controller"}, {"name": "rpa-remote-service-controller", "description": "Rpa Remote Service Controller"}], "paths": {"/api/gpt/proxy/v1/chat/completions": {"get": {"tags": ["RpaTaskAiController"], "summary": "proxy", "operationId": "gptProxyV1ChatCompletionsGet", "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["RpaTaskAiController"], "summary": "proxy", "operationId": "gptProxyV1ChatCompletionsPut", "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "post": {"tags": ["RpaTaskAiController"], "summary": "proxy", "operationId": "gptProxyV1ChatCompletionsPost", "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["RpaTaskAiController"], "summary": "proxy", "operationId": "gptProxyV1ChatCompletionsDelete", "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "options": {"tags": ["RpaTaskAiController"], "summary": "proxy", "operationId": "gptProxyV1ChatCompletionsOptions", "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "head": {"tags": ["RpaTaskAiController"], "summary": "proxy", "operationId": "gptProxyV1ChatCompletionsHead", "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "patch": {"tags": ["RpaTaskAiController"], "summary": "proxy", "operationId": "gptProxyV1ChatCompletionsPatch", "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "trace": {"tags": ["RpaTaskAiController"], "summary": "proxy", "operationId": "gptProxyV1ChatCompletionsTrace", "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/captcha/captchaOcr": {"post": {"tags": ["RapTaskCaptchaController"], "summary": "识别验证码", "operationId": "rpaTaskCaptchaCaptchaOcrPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaptchaOcrRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CaptchaOcrResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/captcha/checkRoom/{roomName}": {"get": {"tags": ["RapTaskCaptchaController"], "summary": "检查一个验证码房间是否存在", "operationId": "rpaTaskCaptchaCheckRoomByRoomNameGet", "parameters": [{"name": "roomName", "in": "path", "description": "roomName", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/captcha/closeRoom/{roomName}": {"delete": {"tags": ["RapTaskCaptchaController"], "summary": "关闭一个验证码房间", "operationId": "rpaTaskCaptchaCloseRoomByRoomNameDelete", "parameters": [{"name": "roomName", "in": "path", "description": "roomName", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/captcha/createRoom/{roomName}": {"put": {"tags": ["RapTaskCaptchaController"], "summary": "创建一个验证码房间", "operationId": "rpaTaskCaptchaCreateRoomByRoomNamePut", "parameters": [{"name": "roomName", "in": "path", "description": "roomName", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "duration", "in": "query", "description": "duration", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/captcha/textOcr": {"post": {"tags": ["RapTaskCaptchaController"], "summary": "文本ocr", "operationId": "rpaTaskCaptchaTextOcrPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CaptchaOcrRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CaptchaOcrResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/cloud/executor/contact": {"post": {"tags": ["RpaCloudInstanceController"], "summary": "Rpa执行器与门户联络一次，创建或更新登录设备", "description": "创建登录设备", "operationId": "rpaCloudExecutorContactPost", "parameters": [{"name": "appId", "in": "query", "description": "App UUID", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hostName", "in": "query", "description": "主机名", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "osName", "in": "query", "description": "操作系统名", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "cpus", "in": "query", "description": "CPU核数", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "mem", "in": "query", "description": "内存bytes", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "appVersion", "in": "query", "description": "appVersion", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/cloud/regions": {"get": {"tags": ["RpaCloudInstanceController"], "summary": "可用区域", "operationId": "rpaCloudRegionsGet", "parameters": [{"name": "flowId", "in": "query", "description": "flowId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaAvailableRegionVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/acceptShareFlow": {"put": {"tags": ["RpaController"], "summary": "通过分享码创建一个流程", "operationId": "rpaAcceptShareFlowPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AcceptShareFlowRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaFlowVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/addGroup": {"post": {"tags": ["RpaController"], "summary": "创建一个流程分组", "operationId": "rpaAddGroupPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddFlowGroupRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowGroupVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/checkFlowShareCode": {"get": {"tags": ["RpaController"], "summary": "校验某个流程分享码", "operationId": "rpaCheckFlowShareCodeGet", "parameters": [{"name": "shareCode", "in": "query", "description": "shareCode", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CheckShareCodeResponse»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/countFlows": {"get": {"tags": ["RpaController"], "summary": "统计团队流程数量", "operationId": "rpaCountFlowsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/create": {"post": {"tags": ["RpaController"], "summary": "创建一个流程", "operationId": "rpaCreatePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRpaFlowRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/createFromHis": {"post": {"tags": ["RpaController"], "summary": "从指定的历史版本创建一个流程", "operationId": "rpaCreateFromHisPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRpaFlowFromHisRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/createShareCode": {"post": {"tags": ["RpaController"], "summary": "创建一个流程共享码", "operationId": "rpaCreateShareCodePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRpaFlowShareRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/deleteGroup/{groupId}": {"delete": {"tags": ["RpaController"], "summary": "删除一个流程分组", "operationId": "rpaDeleteGroupByGroupIdDelete", "parameters": [{"name": "groupId", "in": "path", "description": "groupId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/fetchFlowConfigTree/{configId}": {"get": {"tags": ["RpaController"], "summary": "获取某个流程的配置json用以执行rpa流程", "operationId": "rpaFetchFlowConfigTreeByConfigIdGet", "parameters": [{"name": "configId", "in": "path", "description": "注意是mongoId不是mysqlId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«long,RpaConfig»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/flow/isNameExists": {"get": {"tags": ["RpaController"], "summary": "检查某个流程定义名称是否存在", "operationId": "rpaFlowIsNameExistsGet", "parameters": [{"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "excludeId", "in": "query", "description": "不为空表示排除某个flow", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/flow/{rpaFlowId}": {"get": {"tags": ["RpaController"], "summary": "获取某个流程基本信息", "operationId": "rpaFlowByRpaFlowIdGet", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["RpaController"], "summary": "删除某个流程", "operationId": "rpaFlowByRpaFlowIdDelete", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/flow/{rpaFlowId}/checkHasSubFlows": {"get": {"tags": ["RpaController"], "summary": "检查某个流程是否有子流程", "operationId": "rpaFlowByRpaFlowIdCheckHasSubFlowsGet", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/flowConfig/{configId}": {"get": {"tags": ["RpaController"], "summary": "获取某个流程的配置json", "operationId": "rpaFlowConfigByConfigIdGet", "parameters": [{"name": "configId", "in": "path", "description": "注意是mongoId不是mysqlId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/flows": {"get": {"tags": ["RpaController"], "summary": "获取团队的流程列表", "operationId": "rpaFlowsGet", "parameters": [{"name": "name", "in": "query", "description": "允许按名称模糊查找", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "rpaFlowType", "in": "query", "description": "过滤流程创建类型，为空表示查询所有", "required": false, "style": "form", "schema": {"type": "string", "enum": ["FileCopy", "Manual", "Market", "MarketCopy", "Shared", "TkPack", "Tkshop"]}}, {"name": "rpaType", "in": "query", "description": "按手机还是浏览器流程过滤，为空表示所有", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "groupId", "in": "query", "description": "groupId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "orderBy", "in": "query", "description": "格式为[field_name asc|desc], field_name must in id,name,create_time,update_time,exec_count,last_exec_time,create_type,sort_no", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«RpaFlowVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/getAttachmentLink": {"get": {"tags": ["RpaController"], "summary": "获取一个流程附件的下载链接", "operationId": "rpaGetAttachmentLinkGet", "parameters": [{"name": "attachmentPath", "in": "query", "description": "attachmentPath", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "contentDisposition", "in": "query", "description": "contentDisposition", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "contentType", "in": "query", "description": "contentType", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/getRpaFlowByBizCode": {"get": {"tags": ["RpaController"], "summary": "根据bizCode获取某个流程基本信息", "operationId": "rpaGetRpaFlowByBizCodeGet", "parameters": [{"name": "bizCode", "in": "query", "description": "bizCode", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/groups": {"get": {"tags": ["RpaController"], "summary": "获取流程分组列表", "operationId": "rpaGroupsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaFlowGroupVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/importMarketFlow": {"put": {"tags": ["RpaController"], "summary": "从市场上导入一个流程", "operationId": "rpaImportMarketFlowPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImportMarketFlowRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/isGroupNameExists": {"get": {"tags": ["RpaController"], "summary": "检查某个分组名称是否存在", "operationId": "rpaIsGroupNameExistsGet", "parameters": [{"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "excludeId", "in": "query", "description": "分组改名时用到，检查重复时排除自己", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/publish": {"put": {"tags": ["RpaController"], "summary": "发布某个流程", "operationId": "rpaPublishPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublishFlowRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/updateFlow": {"post": {"tags": ["RpaController"], "summary": "编辑一个流程的基本信息。", "operationId": "rpaUpdateFlowPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRpaFlowRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/updateFlowGroup": {"post": {"tags": ["RpaController"], "summary": "更改一个流程的所属分组。该接口允许流程在非编辑状态时调用。", "operationId": "rpaUpdateFlowGroupPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRpaFlowGroupRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/updateGroup": {"post": {"tags": ["RpaController"], "summary": "更新一个流程分组", "operationId": "rpaUpdateGroupPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFlowGroupRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowGroupVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/updatePublishedBasic": {"post": {"tags": ["RpaController"], "summary": "当一个流程处于发布状态时更新其：名称，描述，分组信息。其它属性设置了无意义", "operationId": "rpaUpdatePublishedBasicPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRpaFlowRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/uploadAttachment": {"post": {"tags": ["RpaController"], "summary": "上传一个流程附件，返回附件的路径", "operationId": "rpaUploadAttachmentPost", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file", "flowId"], "type": "object", "properties": {"elPreview": {"type": "boolean", "description": "该附件是不是元素库预览图"}, "file": {"type": "string", "description": "file", "format": "binary"}, "flowId": {"type": "integer", "description": "flowId", "format": "int64"}}}, "encoding": {"elPreview": {"contentType": "text/plain"}, "flowId": {"contentType": "text/plain"}}}}}, "responses": {"200": {"description": "OK", "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/{rpaFlowId}/checkFlowBoundByPlan": {"get": {"tags": ["RpaController"], "summary": "检查某个流程定义是不是被计划关联了", "operationId": "rpaByRpaFlowIdCheckFlowBoundByPlanGet", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/{rpaFlowId}/draft": {"put": {"tags": ["RpaController"], "summary": "重新编辑某个流程，即将该流程变成草稿状态", "operationId": "rpaByRpaFlowIdDraftPut", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "version", "in": "query", "description": "version", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/{rpaFlowId}/findSharingResult": {"get": {"tags": ["RpaController"], "summary": "获取某个流程的共享状态", "operationId": "rpaByRpaFlowIdFindSharingResultGet", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«RpaFlowShareVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/{rpaFlowId}/findValidShareCodeByFlowId": {"get": {"tags": ["RpaController"], "summary": "查询某个流程相关的有效分享码", "operationId": "rpaByRpaFlowIdFindValidShareCodeByFlowIdGet", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaFlowShareCodeVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/{rpaFlowId}/getLatestVersion": {"get": {"tags": ["RpaController"], "summary": "获取某个流程的最新版本", "operationId": "rpaByRpaFlowIdGetLatestVersionGet", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVersionVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/{rpaFlowId}/getLinkSubFlowLatestVersion": {"get": {"tags": ["RpaController"], "summary": "获取某个流程的子流程的最新版本", "operationId": "rpaByRpaFlowIdGetLinkSubFlowLatestVersionGet", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "subFlowId", "in": "query", "description": "subFlowId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVersionVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/{rpaFlowId}/getVersionHis": {"get": {"tags": ["RpaController"], "summary": "获取某个流程的历史版本列表（无法获取分享流程的历史版本）", "operationId": "rpaByRpaFlowIdGetVersionHisGet", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaFlowVersionVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/{rpaFlowId}/isVersionExists": {"get": {"tags": ["RpaController"], "summary": "用于重新编辑流程时校验某个版本号名称是否已经存在了", "operationId": "rpaByRpaFlowIdIsVersionExistsGet", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "version", "in": "query", "description": "version", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/{rpaFlowId}/stopAFlowShare": {"put": {"tags": ["RpaController"], "summary": "停止给某个团队的流程共享，该操作会导致目标流程被删除", "operationId": "rpaByRpaFlowIdStopAFlowSharePut", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "targetFlowId", "in": "query", "description": "要被删除的目标流程Id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/{rpaFlowId}/updateFlowConfig": {"post": {"tags": ["RpaController"], "summary": "编辑一个rap流程配置json", "operationId": "rpaByRpaFlowIdUpdateFlowConfigPost", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RpaConfig"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/{rpaFlowId}/updateLinkMarketFlowVersion": {"put": {"tags": ["RpaController"], "summary": "将某个市场引用导入的流程更新到最新版本", "operationId": "rpaByRpaFlowIdUpdateLinkMarketFlowVersionPut", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "upgradePlan", "in": "query", "description": "upgradePlan", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/{rpaFlowId}/updateSharedFlowVersion": {"put": {"tags": ["RpaController"], "summary": "将某个被分享的流程更新到最新状态", "operationId": "rpaByRpaFlowIdUpdateSharedFlowVersionPut", "parameters": [{"name": "rpaFlowId", "in": "path", "description": "rpaFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "upgradePlan", "in": "query", "description": "upgradePlan", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/{rpaFlowShareCodeId}/removeShareCode": {"delete": {"tags": ["RpaController"], "summary": "使某个流程分享码失效", "operationId": "rpaByRpaFlowShareCodeIdRemoveShareCodeDelete", "parameters": [{"name": "rpaFlowShareCodeId", "in": "path", "description": "rpaFlowShareCodeId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/fees/bindVoucher/{rpaVoucherId}": {"put": {"tags": ["RpaFeesController"], "summary": "将rpa包时券绑定到设备", "operationId": "rpaFeesBindVoucherByRpaVoucherIdPut", "parameters": [{"name": "rpaVoucherId", "in": "path", "description": "rpaVoucherId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "deviceId", "in": "query", "description": "deviceId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaVoucherVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/fees/buyRpaVouchers": {"post": {"tags": ["RpaFeesController"], "summary": "购买rpa包时券", "operationId": "rpaFeesBuyRpaVouchersPost", "parameters": [{"name": "agreement", "in": "query", "description": "已经勾选阅读和同意使用协议，没什么用", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "autoRenew", "in": "query", "description": "到期是否自动续费", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "balanceAmount", "in": "query", "description": "余额抵扣金额", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}, {"name": "baseCount", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "count", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "distributionCode", "in": "query", "description": "优惠码", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "distributionInfo.code.amount", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}, {"name": "distributionInfo.code.code", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "distributionInfo.code.createTime", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "distributionInfo.code.description", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "distributionInfo.code.discountId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "distributionInfo.code.distributionType", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Deduction", "Discount", "Official"]}}, {"name": "distributionInfo.code.distributor", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "distributionInfo.code.goodsType", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}}, {"name": "distributionInfo.code.id", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "distributionInfo.code.limited", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "distributionInfo.code.name", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "distributionInfo.code.systemDefault", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "distributionInfo.code.usageCount", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "distributionInfo.code.usedCount", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "distributionInfo.code.valid", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "distributionInfo.code.validDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "distributionInfo.deductedPrice", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}, {"name": "distributionInfo.drawPrice", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}, {"name": "duration", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "immediatePay", "in": "query", "description": "是否立即支付（点稍候支付该属性传false）", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "payType", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}}, {"name": "performanceCount", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "periodUnit", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}}, {"name": "voucherAmount", "in": "query", "description": "代金券抵扣金额，不得大于代金券余额", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}, {"name": "voucherId", "in": "query", "description": "要使用的代金券id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/fees/checkRunningTaskOnVoucher/{rpaVoucherId}": {"get": {"tags": ["RpaFeesController"], "summary": "获取某个rpa包时券(绑定的设备)上正跑着的task个数", "operationId": "rpaFeesCheckRunningTaskOnVoucherByRpaVoucherIdGet", "parameters": [{"name": "rpaVoucherId", "in": "path", "description": "rpaVoucherId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/fees/deleteVouchers": {"delete": {"tags": ["RpaFeesController"], "summary": "批量删除流程任务卡", "operationId": "rpaFeesDeleteVouchersDelete", "parameters": [{"name": "rpaVoucherIds", "in": "query", "description": "rpaVoucherIds", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/fees/renewRpaVouchers": {"post": {"tags": ["RpaFeesController"], "summary": "rpa包时卡批量续费", "operationId": "rpaFeesRenewRpaVouchersPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RenewRpaVoucherRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/fees/toggleRpaVoucherAutoRenew": {"put": {"tags": ["RpaFeesController"], "summary": "批量切换rpa包时卡自动续费", "operationId": "rpaFeesToggleRpaVoucherAutoRenewPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ToggleRpaVoucherAutoRenewRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/fees/unbindVoucher/{rpaVoucherId}": {"put": {"tags": ["RpaFeesController"], "summary": "将rpa包时券解绑", "operationId": "rpaFeesUnbindVoucherByRpaVoucherIdPut", "parameters": [{"name": "rpaVoucherId", "in": "path", "description": "rpaVoucherId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaVoucherVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/fees/voucher/{rpaVoucherId}": {"get": {"tags": ["RpaFeesController"], "summary": "获取某一个rpa包时券详情", "operationId": "rpaFeesVoucherByRpaVoucherIdGet", "parameters": [{"name": "rpaVoucherId", "in": "path", "description": "rpaVoucherId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaVoucherVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/fees/voucherBindHis/{rpaVoucherId}": {"get": {"tags": ["RpaFeesController"], "summary": "获取流程任务卡绑定历史", "operationId": "rpaFeesVoucherBindHisByRpaVoucherIdGet", "parameters": [{"name": "rpaVoucherId", "in": "path", "description": "rpaVoucherId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaVoucherHisVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/fees/vouchers": {"get": {"tags": ["RpaFeesController"], "summary": "查询当前团队下rpa包时券列表", "operationId": "rpaFeesVouchersGet", "parameters": [{"name": "serialNumber", "in": "query", "description": "serialNumber", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "startTimeFrom", "in": "query", "description": "startTimeFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "startTimeTo", "in": "query", "description": "startTimeTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "orderBy", "in": "query", "description": "格式为[field_name asc|desc], create_time,goods_id,valid_end_time", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«RpaVoucherVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/market/buyMarketFlow": {"put": {"tags": ["RpaMarketController"], "summary": "购买一个市场流程", "operationId": "rpaMarketBuyMarketFlowPut", "parameters": [{"name": "marketFlowId", "in": "query", "description": "marketFlowId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "periodUnit", "in": "query", "description": "按周，月，买断购买", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«MarketFlowGainsVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/market/findBuyHis": {"get": {"tags": ["RpaMarketController"], "summary": "获取某个团队是不是已经买过一个市场流程了。只要返回不为空就说明购买还在有效期", "operationId": "rpaMarketFindBuyHisGet", "parameters": [{"name": "marketFlowId", "in": "query", "description": "marketFlowId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«MarketFlowGainsVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/market/list": {"get": {"tags": ["RpaMarketController"], "summary": "分页获取市场流程列表", "operationId": "rpaMarketListGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "category", "in": "query", "description": "流程类别，为空表示全部类别", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Demo", "Ecommerce", "Others", "Payment", "Social", "TikTok"]}}, {"name": "rpaType", "in": "query", "description": "手机还是浏览器流程，为空表示所有", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}}, {"name": "publishedOnly", "in": "query", "description": "只查找已发布的流程", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "q", "in": "query", "description": "q", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«MarketFlowVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/market/renewMarketFlow": {"put": {"tags": ["RpaMarketController"], "summary": "续费一个市场流程", "operationId": "rpaMarketRenewMarketFlowPut", "parameters": [{"name": "marketFlowId", "in": "query", "description": "marketFlowId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "periodUnit", "in": "query", "description": "按周，月，买断续费", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«MarketFlowGainsVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/market/{marketFlowId}": {"get": {"tags": ["RpaMarketController"], "summary": "获取某个流程信息", "operationId": "rpaMarketByMarketFlowIdGet", "parameters": [{"name": "marketFlowId", "in": "path", "description": "marketFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«MarketFlowVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/market/{marketFlowId}/images": {"get": {"tags": ["RpaMarketController"], "summary": "获取市场流程的图片", "operationId": "rpaMarketByMarketFlowIdImagesGet", "parameters": [{"name": "marketFlowId", "in": "path", "description": "marketFlowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«MarketFlowImageVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/calcBuyRpaVoucherPrice": {"get": {"tags": ["RpaPaymentController"], "summary": "计算购买rpa包时券价格", "operationId": "paymentCalcBuyRpaVoucherPriceGet", "parameters": [{"name": "duration", "in": "query", "description": "购买多少个周期", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "periodUnit", "in": "query", "description": "续费周期单位, 周或者月", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}}, {"name": "baseCount", "in": "query", "description": "baseCount, 4核以下买多少个", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "performanceCount", "in": "query", "description": "performanceCount, 4核及以上买多少个", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "distributionCode", "in": "query", "description": "优惠码", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CalcPriceResponse»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/calcRenewRpaVoucherPrice": {"get": {"tags": ["RpaPaymentController"], "summary": "计算续费rpa包时卡价格", "operationId": "paymentCalcRenewRpaVoucherPriceGet", "parameters": [{"name": "rpaVoucherIds", "in": "query", "description": "要续费的包时卡列表", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "weekDuration", "in": "query", "description": "按周续费的续费几周", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "monthDuration", "in": "query", "description": "按月续费的续费几月", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "distributionCode", "in": "query", "description": "优惠码", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CalcRenewRpaVoucherResponse»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/allPlanDevices": {"get": {"tags": ["RpaPlanController"], "summary": "获取团队内所有计划指定的设备列表（不包括指定'任意设备的'）", "operationId": "rpaPlanAllPlanDevicesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«LoginDeviceDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/batchToggleEnableSchedule": {"get": {"tags": ["RpaPlanController"], "summary": "批量切换是否开启自动调度", "operationId": "rpaPlanBatchToggleEnableScheduleGet", "parameters": [{"name": "planIds", "in": "query", "description": "planIds", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "enableSchedule", "in": "query", "description": "enableSchedule", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/checkNameExists": {"get": {"tags": ["RpaPlanController"], "summary": "校验某个计划名称（在分组内）是否存在", "operationId": "rpaPlanCheckNameExistsGet", "parameters": [{"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "groupId", "in": "query", "description": "groupId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/create": {"post": {"tags": ["RpaPlanController"], "summary": "创建一个流程计划", "operationId": "rpaPlanCreatePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePlanRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/deletePlans": {"delete": {"tags": ["RpaPlanController"], "summary": "批量删除流程计划", "operationId": "rpaPlanDeletePlansDelete", "parameters": [{"name": "planIds", "in": "query", "description": "planIds", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanExports»"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/exportPlans": {"get": {"tags": ["RpaPlanController"], "summary": "导出流程计划", "operationId": "rpaPlanExportPlansGet", "parameters": [{"name": "planIds", "in": "query", "description": "planIds", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanExports»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/importPlans": {"post": {"tags": ["RpaPlanController"], "summary": "导入流程计划，其中计划的flowId已经根据suggestion或者用户设置改变了", "operationId": "rpaPlanImportPlansPost", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "object"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/list": {"get": {"tags": ["RpaPlanController"], "summary": "分页获取task plan列表", "operationId": "rpaPlanListGet", "parameters": [{"name": "name", "in": "query", "description": "允许按名称模糊查找", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "creatorId", "in": "query", "description": "按创建者ID进行过滤", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "systemType", "in": "query", "description": "systemType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["normal", "tk"]}}, {"name": "clientId", "in": "query", "description": "clientId", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "orderBy", "in": "query", "description": "格式为[field_name asc|desc], id,name,create_time,update_time,creator_id,plan_type,enable_schedule", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«RpaPlanVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/list2": {"get": {"tags": ["RpaPlanController"], "summary": "获取task plan列表V2（TK子系统）", "description": "cronExpression会转换成目标时区下的星期；zonedTime是目标时区下的时间", "operationId": "rpaPlanList2Get", "parameters": [{"name": "systemType", "in": "query", "description": "systemType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["normal", "tk"]}}, {"name": "timezone", "in": "query", "description": "转换为目标时区", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "shopId", "in": "query", "description": "shopId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "flowId", "in": "query", "description": "flowId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "groupId", "in": "query", "description": "按分组过滤。不传时，查询未分组计划", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "creatorId", "in": "query", "description": "按创建者ID进行过滤", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaPlanVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/manualRunPlan": {"post": {"tags": ["RpaPlanController"], "summary": "手动执行一个流程计划，在真实执行机器上调用该接口", "operationId": "rpaPlanManualRunPlanPost", "parameters": [{"name": "token", "in": "query", "description": "token", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/manualTriggerPlan/{rpaPlanId}": {"post": {"tags": ["RpaPlanController"], "summary": "手动触发执行一个流程计划，在发起执行的机器上调用该接口", "operationId": "rpaPlanManualTriggerPlanByRpaPlanIdPost", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunPlanRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/prepareName": {"get": {"tags": ["RpaPlanController"], "summary": "根据名称获取rpaPlan可用的名称", "operationId": "rpaPlanPrepareNameGet", "parameters": [{"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/run/{rpaPlanId}": {"post": {"tags": ["RpaPlanController"], "summary": "调度执行一个流程计划，在真实执行机器上调用该接口", "operationId": "rpaPlanRunByRpaPlanIdPost", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "token", "in": "query", "description": "token", "required": true, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunPlanRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/suggestImportPlanFlows": {"get": {"tags": ["RpaPlanController"], "summary": "导入流程计划时用到，由原始计划的流程给出建议导入计划的时候应该使用什么流程", "operationId": "rpaPlanSuggestImportPlanFlowsGet", "parameters": [{"name": "originRpaFlows", "in": "query", "description": "originRpaFlows", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«long,long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}": {"get": {"tags": ["RpaPlanController"], "summary": "获取单个plan详情，其账号信息是分页返回的", "operationId": "rpaPlanByRpaPlanIdGet", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopPageNum", "in": "query", "description": "shopPageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "shopPageSize", "in": "query", "description": "shopPageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["RpaPlanController"], "summary": "删除一个流程计划", "operationId": "rpaPlanByRpaPlanIdDelete", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}/copy": {"post": {"tags": ["RpaPlanController"], "summary": "复制一个流程计划", "operationId": "rpaPlanByRpaPlanIdCopyPost", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CopyPlanRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}/findPlanSchedulers": {"get": {"tags": ["RpaPlanController"], "summary": "获取plan的调度列表", "operationId": "rpaPlanByRpaPlanIdFindPlanSchedulersGet", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}/getParamFileSignUrl": {"get": {"tags": ["RpaPlanController"], "summary": "获取plan的参数文件签名url(如果有的话)", "operationId": "rpaPlanByRpaPlanIdGetParamFileSignUrlGet", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}/his": {"get": {"tags": ["RpaPlanController"], "summary": "获取plan最近n个执行历史简易信息", "operationId": "rpaPlanByRpaPlanIdHisGet", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "n", "in": "query", "description": "n", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaSimpleHisVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}/modify": {"post": {"tags": ["RpaPlanController"], "summary": "编辑流程计划所有属性", "operationId": "rpaPlanByRpaPlanIdModifyPost", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePlanRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}/params": {"get": {"tags": ["RpaPlanController"], "summary": "获取plan的参数（已经按账号展开）", "operationId": "rpaPlanByRpaPlanIdParamsGet", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopPageNum", "in": "query", "description": "shopPageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "shopPageSize", "in": "query", "description": "shopPageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«long,Map«string,object»»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}/pause": {"post": {"tags": ["RpaPlanController"], "summary": "markPaused", "operationId": "rpaPlanByRpaPlanIdPausePost", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "paused", "in": "query", "description": "paused", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}/toggle/schedule": {"put": {"tags": ["RpaPlanController"], "summary": "切换是否开启自动调度", "operationId": "rpaPlanByRpaPlanIdToggleSchedulePut", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "enableSchedule", "in": "query", "description": "enableSchedule", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}/update/basic": {"post": {"tags": ["RpaPlanController"], "summary": "编辑流程计划基本属性", "operationId": "rpaPlanByRpaPlanIdUpdateBasicPost", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePlanBasicRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}/update/params": {"post": {"tags": ["RpaPlanController"], "summary": "编辑流程计划的变量", "operationId": "rpaPlanByRpaPlanIdUpdateParamsPost", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePlanParamsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}/update/policy": {"post": {"tags": ["RpaPlanController"], "summary": "编辑流程计划的执行策略", "operationId": "rpaPlanByRpaPlanIdUpdatePolicyPost", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePlanPolicyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}/update/shops": {"post": {"tags": ["RpaPlanController"], "summary": "编辑流程计划账号列表", "operationId": "rpaPlanByRpaPlanIdUpdateShopsPost", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePlanShopsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{rpaPlanId}/updateToLatestFlowVersion": {"put": {"tags": ["RpaPlanController"], "summary": "获取某个流程的历史版本记录，可用来回滚到某个特定版本", "operationId": "rpaPlanByRpaPlanIdUpdateToLatestFlowVersionPut", "parameters": [{"name": "rpaPlanId", "in": "path", "description": "rpaPlanId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/group": {"post": {"tags": ["RpaPlanGroupController"], "summary": "创建流程计划分组", "operationId": "rpaPlanGroupPost", "parameters": [{"name": "groupName", "in": "query", "description": "groupName", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "runOnCloud", "in": "query", "description": "runOnCloud", "required": true, "style": "form", "schema": {"type": "boolean"}}, {"name": "deviceId", "in": "query", "description": "deviceId", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "timezone", "in": "query", "description": "timezone", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanGroup»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/group/{groupId}": {"get": {"tags": ["RpaPlanGroupController"], "summary": "获取流程计划分组详情", "operationId": "rpaPlanGroupByGroupIdGet", "parameters": [{"name": "groupId", "in": "path", "description": "groupId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaPlanGroupVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["RpaPlanGroupController"], "summary": "删除分组", "operationId": "rpaPlanGroupByGroupIdDelete", "parameters": [{"name": "groupId", "in": "path", "description": "groupId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/group/{groupId}/name": {"put": {"tags": ["RpaPlanGroupController"], "summary": "修改分组名称", "operationId": "rpaPlanGroupByGroupIdNamePut", "parameters": [{"name": "groupId", "in": "path", "description": "groupId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/group/{groupId}/pause": {"put": {"tags": ["RpaPlanGroupController"], "summary": "暂停分组", "operationId": "rpaPlanGroupByGroupIdPausePut", "parameters": [{"name": "groupId", "in": "path", "description": "groupId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "paused", "in": "query", "description": "paused", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/group/{groupId}/timezone": {"put": {"tags": ["RpaPlanGroupController"], "summary": "修改分组时区", "operationId": "rpaPlanGroupByGroupIdTimezonePut", "parameters": [{"name": "groupId", "in": "path", "description": "groupId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "timezone", "in": "query", "description": "timezone", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/groups": {"get": {"tags": ["RpaPlanGroupController"], "summary": "获取流程计划分组列表", "operationId": "rpaPlanGroupsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaPlanGroupVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/plan/{planId}/group": {"put": {"tags": ["RpaPlanGroupController"], "summary": "修改计划的分组或从分组移除", "operationId": "rpaPlanByPlanIdGroupPut", "parameters": [{"name": "planId", "in": "path", "description": "planId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "groupId", "in": "query", "description": "不传入时，从分组移除", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/rpa/triggerRpaCloudWorkerCalc": {"get": {"tags": ["RpaRemoteServiceController"], "summary": "triggerRpaCloudWorkerCalc", "operationId": "remoteRpaTriggerRpaCloudWorkerCalcGet", "parameters": [{"name": "groupKey", "in": "query", "description": "groupKey", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/ai/completion": {"post": {"tags": ["RpaTaskAiController"], "summary": "调用OpenAi接口生成对话", "operationId": "rpaTaskAiCompletionPost", "parameters": [{"name": "rpaTaskId", "in": "query", "description": "rpaTaskId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OpenAiRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpenAiResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/ai/getAiConfig": {"get": {"tags": ["RpaTaskAiController"], "summary": "调用OpenAi接口生成对话", "operationId": "rpaTaskAiGetAiConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamAiConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/ai/imageUnderstanding": {"post": {"tags": ["RpaTaskAiController"], "summary": "AI图像理解", "operationId": "rpaTaskAiImageUnderstandingPost", "parameters": [{"name": "rpaTaskId", "in": "query", "description": "rpaTaskId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImageUnderstandingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpenAiResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/changePauseStatus/{rpaTaskId}": {"put": {"tags": ["RpaTaskController"], "summary": "切换流程的暂停状态", "operationId": "rpaTaskChangePauseStatusByRpaTaskIdPut", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "rpaTaskItemId", "in": "query", "description": "itemId，为空表示改变整个流程的状态", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "paused", "in": "query", "description": "paused", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/countNotEndByBizCode": {"get": {"tags": ["RpaTaskController"], "summary": "根据bizCode(+shopId)统计未结束的流程任务数量", "operationId": "rpaTaskCountNotEndByBizCodeGet", "parameters": [{"name": "bizCode", "in": "query", "description": "bizCode", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "shopId", "in": "query", "description": "shopId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/countTasks": {"get": {"tags": ["RpaTaskController"], "summary": "获取团队总task个数", "operationId": "rpaTaskCountTasksGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/detectRpaPlugins": {"get": {"tags": ["RpaTaskController"], "summary": "获取某个流程的插件列表", "operationId": "rpaTaskDetectRpaPluginsGet", "parameters": [{"name": "rpaFlowId", "in": "query", "description": "rpaFlowId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/devices": {"get": {"tags": ["RpaTaskController"], "summary": "获取当前用户登录设备", "operationId": "rpaTaskDevicesGet", "parameters": [{"name": "online", "in": "query", "description": "是否在线，不设置返回全部", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "flowId", "in": "query", "description": "flowId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopId", "in": "query", "description": "shopId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaRunDeviceVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/dispatchRpaEvent": {"put": {"tags": ["RpaTaskController"], "summary": "触发一个rpa事件链的执行", "operationId": "rpaTaskDispatchRpaEventPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DispatchRpaEventRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/dynamicSetTaskItems": {"put": {"tags": ["RpaTaskController"], "summary": "执行初始子流程时，设置最终要执行哪些分身", "operationId": "rpaTaskDynamicSetTaskItemsPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DynamicSetTaskItemRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaTaskItemVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/findItem/{rpaTaskItemId}": {"get": {"tags": ["RpaTaskController"], "summary": "获取单个RpaTaskItem的信息", "operationId": "rpaTaskFindItemByRpaTaskItemIdGet", "parameters": [{"name": "rpaTaskItemId", "in": "path", "description": "rpaTaskItemId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskItemVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/findPreviewTaskMobileInfo": {"get": {"tags": ["RpaTaskController"], "summary": "获取一个预览任务的Mobile的信息", "operationId": "rpaTaskFindPreviewTaskMobileInfoGet", "parameters": [{"name": "mobileId", "in": "query", "description": "mobileId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaMobileInfo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/findShopsByPlatform": {"get": {"tags": ["RpaTaskController"], "summary": "rpa.api使用，根据平台类型获取shop(已对当前用户授权)", "operationId": "rpaTaskFindShopsByPlatformGet", "parameters": [{"name": "platform", "in": "query", "description": "platform", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaShopInfo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/findShopsByTagName": {"get": {"tags": ["RpaTaskController"], "summary": "rpa.api使用，根据标签名获取shop(已对当前用户授权)", "operationId": "rpaTaskFindShopsByTagNameGet", "parameters": [{"name": "tagName", "in": "query", "description": "tagName", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaShopInfo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/forceEndByBizCode": {"put": {"tags": ["RpaTaskController"], "summary": "按照bizCode强制结束所有任务", "operationId": "rpaTaskForceEndByBizCodePut", "parameters": [{"name": "bizCode", "in": "query", "description": "bizCode", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "stopPostRun", "in": "query", "description": "stopPostRun", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/geneMouseTracks": {"get": {"tags": ["RpaTaskController"], "summary": "生成鼠标移动轨迹", "operationId": "rpaTaskGeneMouseTracksGet", "parameters": [{"name": "x1", "in": "query", "description": "x1", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "y1", "in": "query", "description": "y1", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "x2", "in": "query", "description": "x2", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "y2", "in": "query", "description": "y2", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«Track»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/getLatestTaskItem": {"get": {"tags": ["RpaTaskController"], "summary": "根据bizCode(+shopId)获取最后一条taskItem", "operationId": "rpaTaskGetLatestTaskItemGet", "parameters": [{"name": "bizCode", "in": "query", "description": "bizCode", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskItemVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/getStunInfo": {"get": {"tags": ["RpaTaskController"], "summary": "获取webrtc stun服务器信息", "operationId": "rpaTaskGetStunInfoGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«StunInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/heartbeat/v2/{rpaTaskId}": {"put": {"tags": ["RpaTaskController"], "summary": "任务心跳", "operationId": "rpaTaskHeartbeatV2ByRpaTaskIdPut", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RpaTaskHeartbeatRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/heartbeat/{rpaTaskId}": {"put": {"tags": ["RpaTaskController"], "summary": "任务心跳(旧版本客户端才会用到)", "operationId": "rpaTaskHeartbeatByRpaTaskIdPut", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/item/appendLog": {"put": {"tags": ["RpaTaskController"], "summary": "往某个item日志上追加内容，直接写oss的，要控制调用频率", "operationId": "rpaTaskItemAppendLogPut", "parameters": [{"name": "rpaTaskItemId", "in": "query", "description": "rpaTaskItemId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/item/{rpaTaskItemId}/files": {"get": {"tags": ["RpaTaskController"], "summary": "获取某个item的文件列表", "operationId": "rpaTaskItemByRpaTaskItemIdFilesGet", "parameters": [{"name": "rpaTaskItemId", "in": "path", "description": "rpaTaskItemId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaTaskFileVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/item/{rpaTaskItemId}/getLogFileInfo": {"get": {"tags": ["RpaTaskController"], "summary": "获取某个item的log文件信息(因为日志文件记录是标记执行的时候就生成了)", "operationId": "rpaTaskItemByRpaTaskItemIdGetLogFileInfoGet", "parameters": [{"name": "rpaTaskItemId", "in": "path", "description": "rpaTaskItemId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskFileVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/item/{rpaTaskItemId}/ranNodes": {"get": {"tags": ["RpaTaskController"], "summary": "获取某个item的执行过的nodes", "operationId": "rpaTaskItemByRpaTaskItemIdRanNodesGet", "parameters": [{"name": "rpaTaskItemId", "in": "path", "description": "rpaTaskItemId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaTaskNodeVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/item/{shopId}/passwords": {"get": {"tags": ["RpaTaskController"], "summary": "获取某个账号对应账号的密码", "operationId": "rpaTaskItemByShopIdPasswordsGet", "parameters": [{"name": "shopId", "in": "path", "description": "shopId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "domain", "in": "query", "description": "domain", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaShopPasswordVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/list": {"get": {"tags": ["RpaTaskController"], "summary": "分页获取task列表", "operationId": "rpaTaskListGet", "parameters": [{"name": "rpaFlowId", "in": "query", "description": "允许按流程过滤", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "name", "in": "query", "description": "允许按名称模糊查找", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "rpaPlanId", "in": "query", "description": "允许按计划查找", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "description": "按状态过滤", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Cancelled", "CreateFailed", "Ended", "Ended_All_Failed", "Ended_Partial_Failed", "Ignored", "NotStart", "Running", "ScheduleCancelled", "Scheduled", "Scheduling", "UnusualEnded"]}}, {"name": "createTimeFrom", "in": "query", "description": "开始时间起", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeTo", "in": "query", "description": "开始时间止", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "deviceId", "in": "query", "description": "执行设置的deviceId", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "shopId", "in": "query", "description": "优先级比mobileId高，有shopId时mobileId无意义", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "mobileId", "in": "query", "description": "mobileId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "orderBy", "in": "query", "description": "格式为[field_name asc|desc], create_time,execute_end_time,name,flow_id,creator_id,executor_id,status,device_name", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«RpaTaskVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/manualTriggerTask": {"post": {"tags": ["RpaTaskController"], "summary": "执行一个流程task，在发起执行的机器上调用该接口", "operationId": "rpaTaskManualTriggerTaskPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewRpaTaskRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/markItemCancelled": {"post": {"tags": ["RpaTaskController"], "summary": "标记某个item取消执行", "operationId": "rpaTaskMarkItemCancelledPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarkRpaItemCancelledRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskItemVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/markItemEnd": {"post": {"tags": ["RpaTaskController"], "summary": "标记某个item执行结束", "operationId": "rpaTaskMarkItemEndPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarkRpaItemEndRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskItemVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/notifyAllItemsEnd": {"post": {"tags": ["RpaTaskController"], "summary": "通知客户端所有的账号items都已经完成了", "operationId": "rpaTaskNotifyAllItemsEndPost", "parameters": [{"name": "rpaTaskId", "in": "query", "description": "rpaTaskId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/notifyAllShopsEnd": {"post": {"tags": ["RpaTaskController"], "summary": "为了避免歧义，请改用 /notifyAllItemsEnd，本接口保留只为了兼容旧客户端", "operationId": "rpaTaskNotifyAllShopsEndPost", "parameters": [{"name": "rpaTaskId", "in": "query", "description": "rpaTaskId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/notifyEmails": {"post": {"tags": ["RpaTaskController"], "summary": "获取用于用户自定义邮箱服务器发邮件的邮箱列表", "operationId": "rpaTaskNotifyEmailsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendNotifyUsers"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/notifyFileAdded": {"put": {"tags": ["RpaTaskController"], "summary": "通知服务器记录上传了一个文件", "operationId": "rpaTaskNotifyFileAddedPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyFileAddedRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskFileVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/notifyOssFileAdded": {"put": {"tags": ["RpaTaskController"], "summary": "文件操作后通知服务器记录文件的添加", "operationId": "rpaTaskNotifyOssFileAddedPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyOssFileAddedRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/parseToShopIds": {"get": {"tags": ["RpaTaskController"], "summary": "将相应的店铺id解析出来", "operationId": "rpaTaskParseToShopIdsGet", "parameters": [{"name": "rpaType", "in": "query", "description": "rpaType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}}, {"name": "by", "in": "query", "description": "by", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "items", "in": "query", "description": "items", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/reportOpenShopSession": {"post": {"tags": ["RpaTaskController"], "summary": "汇报rpa打开了一个会话", "operationId": "rpaTaskReportOpenShopSessionPost", "parameters": [{"name": "rpaTaskItemId", "in": "query", "description": "rpaTaskItemId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sessionId", "in": "query", "description": "sessionId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopSessionId", "in": "query", "description": "shopSessionId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/runItem": {"post": {"tags": ["RpaTaskController"], "summary": "开始执行一个流程item", "operationId": "rpaTaskRunItemPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RunTaskItemRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskItemVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/runTask": {"post": {"tags": ["RpaTaskController"], "summary": "创建一个流程task，在真实执行机器上调用该接口", "operationId": "rpaTaskRunTaskPost", "parameters": [{"name": "token", "in": "query", "description": "token", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/sendNotify": {"post": {"tags": ["RpaTaskController"], "summary": "发送消息节点", "operationId": "rpaTaskSendNotifyPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendNotifyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«SendNotifyResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/signDiskFile": {"get": {"tags": ["RpaTaskController"], "summary": "为某个网盘文件生成签名读取链接", "operationId": "rpaTaskSignDiskFileGet", "parameters": [{"name": "file", "in": "query", "description": "file", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/signFile/{rpaTaskItemId}/rpaTaskFileId": {"get": {"tags": ["RpaTaskController"], "summary": "为某个rpa task file生成签名读取链接", "operationId": "rpaTaskSignFileByRpaTaskItemIdRpaTaskFileIdGet", "parameters": [{"name": "rpaTaskItemId", "in": "path", "description": "rpaTaskItemId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "rpaTaskFileId", "in": "path", "description": "rpaTaskFileId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/silentTriggerFlowByBizCode": {"post": {"tags": ["RpaTaskController"], "summary": "直接通过bizCode执行一个流程，但不生成rpa_task记录，用于执行一些小动作如打开手机后直接切换到某个tk账号", "operationId": "rpaTaskSilentTriggerFlowByBizCodePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TriggerFlowByBizCodeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/syncFiles": {"put": {"tags": ["RpaTaskController"], "summary": "在网盘和rpa工作目录之间同步文件。", "operationId": "rpaTaskSyncFilesPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncFilesRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskFileId}/updateFileSize": {"put": {"tags": ["RpaTaskController"], "summary": "通知服务器更新某个文件的大小（如日志文件大小变化了）", "operationId": "rpaTaskByRpaTaskFileIdUpdateFileSizePut", "parameters": [{"name": "rpaTaskFileId", "in": "path", "description": "rpaTaskFileId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "filesize", "in": "query", "description": "filesize", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskFileVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskId}": {"get": {"tags": ["RpaTaskController"], "summary": "获取一个task的基本信息", "operationId": "rpaTaskByRpaTaskIdGet", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskId}/cancelTaskScheduling": {"post": {"tags": ["RpaTaskController"], "summary": "将某个正在等待调度的rpa task取消调度（大多因为rpa云端执行器）", "operationId": "rpaTaskByRpaTaskIdCancelTaskSchedulingPost", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskId}/environments": {"get": {"tags": ["RpaTaskController"], "summary": "获取一个task的环境变量", "operationId": "rpaTaskByRpaTaskIdEnvironmentsGet", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskReport»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskId}/forceEnd": {"put": {"tags": ["RpaTaskController"], "summary": "强制结束一个任务", "operationId": "rpaTaskByRpaTaskIdForceEndPut", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "stopPostRun", "in": "query", "description": "stopPostRun", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskId}/getTaskPredefineParams": {"get": {"tags": ["RpaTaskController"], "summary": "获取一个task的输入变量值", "operationId": "rpaTaskByRpaTaskIdGetTaskPredefineParamsGet", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«long,Map«string,object»»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskId}/items": {"get": {"tags": ["RpaTaskController"], "summary": "获取一个task的item列表", "operationId": "rpaTaskByRpaTaskIdItemsGet", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaTaskItemVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskId}/lockFile": {"put": {"tags": ["RpaTaskController"], "summary": "切换任务文件的lock状态", "operationId": "rpaTaskByRpaTaskIdLockFilePut", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "fileLocked", "in": "query", "description": "fileLocked", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskId}/markTaskEnd": {"put": {"tags": ["RpaTaskController"], "summary": "任务跑完后要调用该接口", "operationId": "rpaTaskByRpaTaskIdMarkTaskEndPut", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskId}/removeTaskFiles": {"delete": {"tags": ["RpaTaskController"], "summary": "删除任务文件 (会清除整个task的文件)", "operationId": "rpaTaskByRpaTaskIdRemoveTaskFilesDelete", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskId}/shopsInfo": {"get": {"tags": ["RpaTaskController"], "summary": "获取一个task的所有的分身或者是手机信息(根据rpaType的不同而定)", "operationId": "rpaTaskByRpaTaskIdShopsInfoGet", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«AbstractRpaShopInfo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskId}/{rpaTaskItemId}/params": {"get": {"tags": ["RpaTaskController"], "summary": "获取一个taskItem的变量值定义", "operationId": "rpaTaskByRpaTaskIdByRpaTaskItemIdParamsGet", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "rpaTaskItemId", "in": "path", "description": "rpaTaskItemId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«string,object»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskId}/{taskItemId}/removeTaskItemFiles": {"delete": {"tags": ["RpaTaskController"], "summary": "删除任务item文件，可指定具体文件", "operationId": "rpaTaskByRpaTaskIdByTaskItemIdRemoveTaskItemFilesDelete", "parameters": [{"name": "rpaTaskId", "in": "path", "description": "rpaTaskId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "taskItemId", "in": "path", "description": "taskItemId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "fileIds", "in": "query", "description": "以逗号分隔开的文件id: 1,2,3 。如果不传会删除该item下所有的文件", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskItemId}/findRpaSessions": {"get": {"tags": ["RpaTaskController"], "summary": "获取一个task item打开了哪些会话", "operationId": "rpaTaskByRpaTaskItemIdFindRpaSessionsGet", "parameters": [{"name": "rpaTaskItemId", "in": "path", "description": "rpaTaskItemId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{rpaTaskItemId}/signature": {"get": {"tags": ["RpaTaskController"], "summary": "为会rpa生成上传sts token", "operationId": "rpaTaskByRpaTaskItemIdSignatureGet", "parameters": [{"name": "rpaTaskItemId", "in": "path", "description": "rpaTaskItemId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«StsPostSignature»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/{shopId}/findPreviewTaskShopInfo": {"get": {"tags": ["RpaTaskController"], "summary": "获取一个预览任务的分身的信息", "operationId": "rpaTaskByShopIdFindPreviewTaskShopInfoGet", "parameters": [{"name": "shopId", "in": "path", "description": "shopId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaShopInfo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/db/appendToSet/{key}": {"put": {"tags": ["RpaTaskDbController"], "summary": "appendToSet", "operationId": "rpaTaskDbAppendToSetByKeyPut", "parameters": [{"name": "key", "in": "path", "description": "key", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "scope", "in": "query", "description": "scope", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "rpaFlowId", "in": "query", "description": "rpaFlowId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/db/del/{key}": {"delete": {"tags": ["RpaTaskDbController"], "summary": "删除一个Key", "operationId": "rpaTaskDbDelByKeyDelete", "parameters": [{"name": "key", "in": "path", "description": "key", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "scope", "in": "query", "description": "scope", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "rpaFlowId", "in": "query", "description": "rpaFlowId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/db/get/{key}": {"get": {"tags": ["RpaTaskDbController"], "summary": "获取一个Key", "operationId": "rpaTaskDbGetByKeyGet", "parameters": [{"name": "key", "in": "path", "description": "key", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "scope", "in": "query", "description": "scope", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "rpaFlowId", "in": "query", "description": "rpaFlowId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/db/list": {"get": {"tags": ["RpaTaskDbController"], "summary": "获取已有key列表", "operationId": "rpaTaskDbListGet", "parameters": [{"name": "scope", "in": "query", "description": "scope", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "rpaFlowId", "in": "query", "description": "rpaFlowId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/task/db/put/{key}": {"put": {"tags": ["RpaTaskDbController"], "summary": "写入一个Key", "operationId": "rpaTaskDbPutByKeyPut", "parameters": [{"name": "key", "in": "path", "description": "key", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "scope", "in": "query", "description": "scope", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "rpaFlowId", "in": "query", "description": "rpaFlowId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/trigger/email-receiver": {"get": {"tags": ["RpaTriggerController"], "summary": "获取邮件触发器邮件接收地址", "operationId": "rpaTriggerEmailReceiverGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/trigger/emailToken": {"post": {"tags": ["RpaTriggerController"], "summary": "创建邮件触发器Token", "operationId": "rpaTriggerEmailTokenPost", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/trigger/fileEvent": {"post": {"tags": ["RpaTriggerController"], "summary": "触发一个文件触发器事件", "operationId": "rpaTriggerFileEventPost", "parameters": [{"name": "triggerId", "in": "query", "description": "triggerId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "eventType", "in": "query", "description": "eventType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Created", "Deleted", "Modified"]}}, {"name": "file", "in": "query", "description": "file", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFileTriggerEventVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/trigger/fileTriggers": {"get": {"tags": ["RpaTriggerController"], "summary": "获取当前设备的文件触发器", "operationId": "rpaTriggerFileTriggersGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaFileTriggerVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/trigger/hook": {"get": {"tags": ["RpaTriggerController"], "summary": "触发Http触发器", "operationId": "rpaTriggerHookGet", "parameters": [{"name": "token", "in": "query", "description": "token", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "clientToken", "in": "query", "description": "clientToken", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "format", "in": "query", "description": "format", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "deviceId", "in": "query", "description": "deviceId", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaHttpTriggerResponseVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "post": {"tags": ["RpaTriggerController"], "summary": "触发Http触发器", "operationId": "rpaTriggerHookPost", "parameters": [{"name": "token", "in": "query", "description": "token", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "clientToken", "in": "query", "description": "clientToken", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "format", "in": "query", "description": "format", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "deviceId", "in": "query", "description": "deviceId", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaHttpTriggerResponseVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/trigger/hook-event": {"get": {"tags": ["RpaTriggerController"], "summary": "查询Rpa Http触发事件", "operationId": "rpaTriggerHookEventGet", "parameters": [{"name": "requestId", "in": "query", "description": "requestId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaHttpTriggerResponseVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/trigger/httpToken": {"post": {"tags": ["RpaTriggerController"], "summary": "创建Http触发器Token", "operationId": "rpaTriggerHttpTokenPost", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/rpa/trigger/messageTriggers": {"get": {"tags": ["RpaTriggerController"], "summary": "获取当前团队的消息触发器", "operationId": "rpaTriggerMessageTriggersGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaMessageTriggerVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AbstractRpaShopInfo": {"title": "AbstractRpaShopInfo", "type": "object", "properties": {"description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "itemId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "AcceptShareFlowRequest": {"title": "AcceptShareFlowRequest", "type": "object", "properties": {"acceptFlows": {"type": "object", "additionalProperties": {"type": "string"}, "description": "接受哪些流程并指定它们的名字"}, "shareCode": {"type": "string"}}}, "AddFlowGroupRequest": {"title": "AddFlowGroupRequest", "type": "object", "properties": {"description": {"type": "string"}, "name": {"type": "string"}, "sortNumber": {"type": "integer", "format": "int32"}}}, "CalcPriceResponse": {"title": "CalcPriceResponse", "type": "object", "properties": {"discount": {"type": "number", "description": "折扣,[0-1]", "format": "double"}, "discountAmount": {"type": "number", "description": "打折减掉的金额(如果是打折的话)", "format": "bigdecimal"}, "items": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ItemPriceInfo"}, "description": "记录每个item的价格及折扣或赠送信息，<key=goodsId, value=PriceInfo>"}, "payablePrice": {"type": "number", "description": "订单应付价(减掉了打折等信息)", "format": "bigdecimal"}, "presentAmount": {"type": "number", "description": "赠送金额，目前只出现在购买花瓣", "format": "bigdecimal"}, "totalCost": {"type": "number", "description": "订单总成本", "format": "bigdecimal"}, "totalPrice": {"type": "number", "description": "订单总价(原价)", "format": "bigdecimal"}}}, "CalcRenewRpaVoucherResponse": {"title": "CalcRenewRpaVoucherResponse", "type": "object", "properties": {"discountAmount": {"type": "number", "description": "打折减掉的金额(如果是打折的话)", "format": "bigdecimal"}, "distributionInfo": {"$ref": "#/components/schemas/DistributionInfo"}, "monthItems": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ItemPriceInfo"}, "description": "记录按月续费的价格及折扣，<key=ipId || rpaVoucherId, value=PriceInfo>"}, "payablePrice": {"type": "number", "description": "订单应付价(减掉了打折等信息)", "format": "bigdecimal"}, "totalCost": {"type": "number", "description": "订单总成本", "format": "bigdecimal"}, "totalPrice": {"type": "number", "description": "订单总价(原价)", "format": "bigdecimal"}, "weekItems": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ItemPriceInfo"}, "description": "记录按周续费的价格及折扣，<key=ipId || rpaVoucherId, value=PriceInfo>"}}}, "CaptchaOcrRequest": {"title": "CaptchaOcrRequest", "type": "object", "properties": {"codeType": {"type": "string"}, "image": {"type": "string"}, "image1": {"type": "string"}, "param1": {"type": "string"}, "provider": {"type": "string"}, "taskId": {"type": "integer", "format": "int64"}}}, "CaptchaOcrResponse": {"title": "CaptchaOcrResponse", "type": "object", "properties": {"message": {"type": "string"}, "provider": {"type": "string"}, "success": {"type": "boolean"}, "text": {"type": "string"}}}, "CheckShareCodeResponse": {"title": "CheckShareCodeResponse", "type": "object", "properties": {"allowPushUpdate": {"type": "boolean"}, "allowRead": {"type": "boolean"}, "creatorId": {"type": "integer", "format": "int64"}, "creatorName": {"type": "string"}, "flows": {"type": "array", "items": {"$ref": "#/components/schemas/ShareFlowCheckInfo"}}, "includeGroups": {"type": "boolean"}, "shareCode": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "teamName": {"type": "string"}}}, "CopyPlanRequest": {"title": "CopyPlanRequest", "type": "object", "properties": {"cronExpression": {"type": "string", "description": "重复日期，只包含星期信息的表达式"}, "enableSchedule": {"type": "boolean"}, "expressions": {"type": "array", "description": "如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...]", "items": {"type": "string"}}, "name": {"type": "string", "description": "复制后的计划名称"}, "params": {"type": "object", "additionalProperties": {"type": "object"}, "description": "每个店铺自己的变量"}, "shopIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "CreateEmailRpaTriggerVo": {"title": "CreateEmailRpaTriggerVo", "type": "object", "properties": {"content": {"type": "string"}, "sender": {"type": "string"}, "token": {"type": "string"}}}, "CreateFileRpaTriggerVo": {"title": "CreateFileRpaTriggerVo", "type": "object", "properties": {"deviceId": {"type": "string"}, "dir": {"type": "string"}, "fileExt": {"type": "string"}, "includeSub": {"type": "boolean"}, "watchCreate": {"type": "boolean"}, "watchDelete": {"type": "boolean"}, "watchModify": {"type": "boolean"}}}, "CreateHttpRpaTriggerVo": {"title": "CreateHttpRpaTriggerVo", "type": "object", "properties": {"clientInterval": {"type": "integer", "description": "触发间隔（秒）：>0时，在这个时间段内触发当做同一次触发", "format": "int32"}, "httpMethod": {"type": "string", "description": "请求的Http方法，支持GET|POST,默认不限制"}, "token": {"type": "string"}}}, "CreateMessageRpaTriggerVo": {"title": "CreateMessageRpaTriggerVo", "type": "object", "properties": {"messageId": {"type": "string"}}}, "CreateOrderResponse": {"title": "CreateOrderResponse", "type": "object", "properties": {"bankAccount": {"type": "string"}, "bankAccountName": {"type": "string"}, "bankName": {"type": "string"}, "bankRemark": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "deductedPrice": {"type": "number", "description": "扣减金额", "format": "bigdecimal"}, "orderId": {"type": "integer", "format": "int64"}, "payOutContent": {"type": "string", "description": "支付输出的内容"}, "payOutType": {"type": "string", "description": "支付输出的内容类型"}, "payStatus": {"type": "string", "description": "如果不需要现金支付，该订单状态会直接变成已支付", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}, "payType": {"type": "string", "description": "支付方式", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "realPrice": {"type": "number", "description": "需要现金支付的money", "format": "bigdecimal"}, "salesReduction": {"type": "number", "format": "bigdecimal"}, "serialNumber": {"type": "string"}}}, "CreatePlanRequest": {"title": "CreatePlanRequest", "type": "object", "properties": {"allowOthersEdit": {"type": "boolean", "description": "是否允许他人编辑此计划", "example": false}, "caseBrowserOpened": {"type": "string", "description": "当账号的浏览器已打开时的策略：stop | reopen | reuse"}, "closeBrowserOnEnd": {"type": "string", "description": "流程结束后是否关闭账号浏览器：close | keep"}, "concurrent": {"type": "integer", "description": "账号并发数量", "format": "int32"}, "concurrentDelay": {"type": "integer", "description": "并发等待时间间隔", "format": "int32"}, "cronExpression": {"type": "string", "description": "重复日期，只包含星期信息的表达式"}, "description": {"type": "string"}, "deviceId": {"type": "string", "description": "login_device表里的device_id"}, "duration": {"type": "integer", "format": "int32"}, "emailTrigger": {"description": "邮件触发器", "$ref": "#/components/schemas/CreateEmailRpaTriggerVo"}, "enableSchedule": {"type": "boolean"}, "expressions": {"type": "array", "description": "如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...]", "items": {"type": "string"}}, "fileTrigger": {"description": "文件触发器", "$ref": "#/components/schemas/CreateFileRpaTriggerVo"}, "flowId": {"type": "integer", "format": "int64"}, "forceRecord": {"type": "boolean"}, "groupId": {"type": "integer", "description": "计划分组ID", "format": "int64"}, "headless": {"type": "boolean", "description": "是否以无头模式运行", "example": false}, "httpTrigger": {"description": "Http触发器", "$ref": "#/components/schemas/CreateHttpRpaTriggerVo"}, "layoutConfig": {"type": "string", "description": "窗口布局配置,json格式"}, "maxMinutes": {"type": "integer", "description": "该计划最大执行时长，单位分钟。相应的流程如果执行超过这个值会被强制结束。为空或为0表示不限制", "format": "int32"}, "messageTrigger": {"description": "事件触发器", "$ref": "#/components/schemas/CreateMessageRpaTriggerVo"}, "name": {"type": "string"}, "othersRunPolicy": {"type": "string", "description": "他人执行此流程的策略。 为空表示只能创建者可以执行，非空可选值为：creator | others | creator_others"}, "paramFilePath": {"type": "string", "description": "如果流程输入变量来自网盘，保存文件路径，以 team_disk:// 或 user_disk:// 开头"}, "params": {"type": "object", "additionalProperties": {"type": "object"}, "description": "每个店铺自己的变量"}, "planEventDelay": {"type": "integer", "format": "int32"}, "planEventName": {"type": "string"}, "planType": {"type": "string", "description": "计划类型", "enum": ["Auto", "Loop", "Manual", "<PERSON><PERSON>"]}, "popupTaskLog": {"type": "boolean", "description": "是否自动弹出流程日志窗口，为空表示false", "example": false}, "provider": {"type": "string", "description": "云端执行的云厂商", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "region": {"type": "string", "description": "云端执行的区域"}, "runOnCloud": {"type": "boolean", "description": "是否云端执行", "example": false}, "sharingParams": {"type": "object", "description": "所有店铺共用的变量"}, "shopIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "showMouseTrack": {"type": "boolean", "description": "是否显示鼠标轨迹", "example": false}, "sidePanel": {"type": "string", "description": "执行时侧边栏显示策略。hide | show，为空表示继承流程配置"}, "snapshot": {"type": "string", "enum": ["Node", "Not", "OnFail"]}, "snapshotScope": {"type": "string", "enum": ["fullPage", "pdf", "viewport", "window"]}, "startEventDelay": {"type": "integer", "format": "int32"}, "startEventName": {"type": "string"}, "systemType": {"type": "string", "enum": ["normal", "tk"]}, "taskNameEl": {"type": "string", "description": "任务命名规则"}, "triggerPlanEvent": {"type": "boolean", "description": "计划结束后是否触发一个计划事件", "example": false}, "triggerStartEvent": {"type": "boolean", "description": "计划开始后是否触发一个计划事件", "example": false}}, "description": "创建一个流程计划"}, "CreateRpaFlowFromHisRequest": {"title": "CreateRpaFlowFromHisRequest", "type": "object", "properties": {"console": {"type": "boolean", "description": "是否控制台流程", "example": false}, "description": {"type": "string"}, "groupIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "hisVersionId": {"type": "integer", "description": "历史版本id", "format": "int64"}, "name": {"type": "string"}, "platforms": {"type": "array", "items": {"type": "string"}}, "shopId": {"type": "integer", "description": "编辑用到账号id", "format": "int64"}, "version": {"type": "string"}}}, "CreateRpaFlowRequest": {"title": "CreateRpaFlowRequest", "type": "object", "properties": {"console": {"type": "boolean", "description": "是否控制台流程", "example": false}, "description": {"type": "string"}, "fromFile": {"type": "boolean", "description": "是不是从文件恢复过来的", "example": false}, "groupIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "name": {"type": "string"}, "platforms": {"type": "array", "items": {"type": "string"}}, "rpaType": {"type": "string", "description": "流程类型，分手机和browser", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "shopId": {"type": "integer", "description": "编辑用到账号id", "format": "int64"}, "version": {"type": "string"}}}, "CreateRpaFlowShareRequest": {"title": "CreateRpaFlowShareRequest", "type": "object", "properties": {"allowPushUpdate": {"type": "boolean", "description": "是否允许分享方主动推送更新，默认false", "example": false}, "allowRead": {"type": "boolean", "description": "是否可读，默认false", "example": false}, "groupNames": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "description": "具体要带上的分组信息，{1111: [分组1, 分组2], 2222: [分组2]}"}, "includeGroups": {"type": "boolean", "description": "是否包含流程的分组信息，默认false", "example": false}, "rpaFlowIds": {"type": "array", "description": "分享哪些流程", "items": {"type": "integer", "format": "int64"}}, "validMinutes": {"type": "integer", "description": "有效期（分钟）", "format": "int32"}}}, "DiscountsVo": {"title": "DiscountsVo", "type": "object", "properties": {"amount": {"type": "integer", "description": "赠送数量或折扣百分比或阶梯折扣百分比", "format": "int32"}, "discountCode": {"type": "string", "description": "打折code"}, "discountType": {"type": "string", "description": "打折还是赠送", "enum": ["Discount", "LadderPrice", "Present"]}, "periodUnit": {"type": "string", "description": "周期或数量单位", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "remarks": {"type": "string", "description": "备注"}, "threshold": {"type": "integer", "description": "期数或数量", "format": "int32"}}}, "DispatchRpaEventRequest": {"title": "DispatchRpaEventRequest", "type": "object", "properties": {"eventName": {"type": "string"}, "params": {"type": "object"}, "rpaTaskId": {"type": "integer", "format": "int64"}, "rpaTaskItemIds": {"type": "array", "description": "如果不为空，表示只触发指定的item", "items": {"type": "integer", "format": "int64"}}}}, "DistributionCodeDto": {"title": "DistributionCodeDto", "type": "object", "properties": {"amount": {"type": "number", "format": "bigdecimal"}, "code": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "discountId": {"type": "integer", "format": "int64"}, "distributionType": {"type": "string", "enum": ["Deduction", "Discount", "Official"]}, "distributor": {"type": "integer", "format": "int64"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "limited": {"type": "boolean"}, "name": {"type": "string"}, "systemDefault": {"type": "boolean"}, "usageCount": {"type": "integer", "format": "int32"}, "usedCount": {"type": "integer", "format": "int32"}, "valid": {"type": "boolean"}, "validDays": {"type": "integer", "format": "int32"}}}, "DistributionInfo": {"title": "DistributionInfo", "type": "object", "properties": {"code": {"$ref": "#/components/schemas/DistributionCodeDto"}, "deductedPrice": {"type": "number", "format": "bigdecimal"}, "drawPrice": {"type": "number", "format": "bigdecimal"}}}, "DomShadow": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "object", "properties": {"description": {"type": "string"}, "iframe": {"type": "string", "description": "被映射的元素位于iframe下"}, "name": {"type": "string"}, "selector": {"type": "string", "description": "映射节点的selector或者xpath"}, "sid": {"type": "string"}, "type": {"type": "string", "description": "目前支持：input | textarea | button"}}}, "DynamicSetTaskItemRequest": {"title": "DynamicSetTaskItemRequest", "type": "object", "properties": {"rpaTaskId": {"type": "integer", "format": "int64"}, "shopIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "Element": {"title": "Element", "type": "object", "properties": {"backupSelectors": {"type": "array", "items": {"type": "string"}}, "description": {"type": "string"}, "documentTitle": {"type": "string", "description": "页面标题"}, "href": {"type": "string", "description": "网页链接"}, "id": {"type": "string", "description": "一个随机字符串"}, "iframe": {"type": "string"}, "majorInspect": {"type": "string", "description": "优先使用何种方式定位元素，可选值: selector|xpath|none"}, "minorInspect": {"type": "string", "description": "然后使用何种方式定位元素，可选值: selector|xpath|none"}, "name": {"type": "string"}, "parent": {"type": "string", "description": "父元素ID"}, "preview": {"type": "string", "description": "预览图，指向一个附件的key，如: attachment://record_bucket/11111/2222-22220-2222-2222.png"}, "selector": {"type": "string"}, "type": {"type": "string", "description": "元素类型"}, "xpath": {"type": "string"}}}, "ImageUnderstandingRequest": {"title": "ImageUnderstandingRequest", "type": "object", "properties": {"height": {"type": "integer", "format": "int32"}, "image": {"type": "string", "description": "base64格式的图片"}, "prompts": {"type": "array", "items": {"type": "string"}}, "provider": {"type": "string"}, "width": {"type": "integer", "format": "int32"}}}, "ImportMarketFlowRequest": {"title": "ImportMarketFlowRequest", "type": "object", "properties": {"console": {"type": "boolean", "description": "是否控制台流程", "example": false}, "copy": {"type": "boolean"}, "description": {"type": "string"}, "draft": {"type": "boolean"}, "followMarket": {"type": "boolean", "description": "市场流程发生更新时是否自动更新。当copy=false时才有意义", "example": false}, "groupIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "marketFlowId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "platforms": {"type": "array", "items": {"type": "string"}}, "shopId": {"type": "integer", "description": "编辑用到账号id", "format": "int64"}, "version": {"type": "string"}}}, "IpPoolDto": {"title": "IpPoolDto", "type": "object", "properties": {"allocateStrategy": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ByLoad", "ByOrder", "ByRandom"]}, "capacity": {"type": "integer", "format": "int32"}, "connectTransits": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creator": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "domestic": {"type": "boolean"}, "enableWhitelist": {"type": "boolean"}, "exclusive": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "lastApiTime": {"type": "string", "format": "date-time"}, "lifetime": {"type": "integer", "format": "int32"}, "locationId": {"type": "integer", "format": "int64"}, "minApiInterval": {"type": "integer", "format": "int32"}, "minApiNum": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "produceFromTransit": {"type": "boolean"}, "produceStrategy": {"type": "string", "enum": ["ProduceOnEmpty", "ProduceOnHand", "ProduceOnRequest"]}, "produced": {"type": "integer", "format": "int32"}, "provider": {"type": "string"}, "releaseStrategy": {"type": "string", "enum": ["Discard", "Reuse"]}, "teamId": {"type": "integer", "format": "int64"}, "transitType": {"type": "string", "enum": ["Auto", "Direct", "Transit"]}, "transits": {"type": "string"}, "tunnelTypes": {"type": "string"}}}, "ItemPriceInfo": {"title": "ItemPriceInfo", "type": "object", "properties": {"costPrice": {"type": "number", "format": "bigdecimal"}, "currentValidEndTime": {"type": "string", "description": "当前过期时间", "format": "date-time"}, "discount": {"$ref": "#/components/schemas/DiscountsVo"}, "discountAmount": {"type": "number", "description": "打折减掉的金额，如果是打折的话", "format": "bigdecimal"}, "goodsId": {"type": "integer", "format": "int64"}, "payablePrice": {"type": "number", "description": "应付价格", "format": "bigdecimal"}, "presentAmount": {"type": "number", "description": "赠送数量，如果是赠送的话。目前只出现在购买花瓣", "format": "bigdecimal"}, "price": {"type": "number", "description": "item总价", "format": "bigdecimal"}, "validEndTime": {"type": "string", "description": "续费后到期时间", "format": "date-time"}}}, "ItemShopInfo": {"title": "ItemShopInfo", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "LocalTime": {"title": "LocalTime", "type": "object", "properties": {"hour": {"type": "integer", "format": "int32"}, "minute": {"type": "integer", "format": "int32"}, "nano": {"type": "integer", "format": "int32"}, "second": {"type": "integer", "format": "int32"}}}, "LoginDeviceDto": {"title": "LoginDeviceDto", "type": "object", "properties": {"appId": {"type": "string"}, "appVersion": {"type": "string"}, "clientIp": {"type": "string"}, "clientLocation": {"type": "integer", "format": "int64"}, "cpus": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "deviceId": {"type": "string"}, "deviceType": {"type": "string", "enum": ["App", "Browser", "Extension", "HYRuntime", "RpaExecutor"]}, "domestic": {"type": "boolean"}, "hostName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "ipDataId": {"type": "integer", "format": "int64"}, "lastActiveTime": {"type": "string", "format": "date-time"}, "lastCity": {"type": "string"}, "lastLogTime": {"type": "string", "format": "date-time"}, "lastRemoteIp": {"type": "string"}, "lastUserId": {"type": "integer", "format": "int64"}, "logUrl": {"type": "string"}, "mem": {"type": "integer", "format": "int64"}, "online": {"type": "boolean"}, "osName": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "userAgent": {"type": "string"}, "version": {"type": "string"}}}, "MarkRpaItemCancelledRequest": {"title": "MarkRpaItemCancelledRequest", "type": "object", "properties": {"errorMsg": {"type": "string"}, "rpaTaskItemIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "MarkRpaItemEndRequest": {"title": "MarkRpaItemEndRequest", "type": "object", "properties": {"error": {"type": "string"}, "force": {"type": "boolean"}, "rpaStatus": {"type": "string", "enum": ["Cancelled", "CreateFailed", "Ended", "Ended_All_Failed", "Ended_Partial_Failed", "Ignored", "NotStart", "Running", "ScheduleCancelled", "Scheduled", "Scheduling", "UnusualEnded"]}, "rpaTaskItemId": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "MarketFlowGainsVo": {"title": "MarketFlowGainsVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "credit": {"type": "number", "format": "bigdecimal"}, "duration": {"type": "integer", "format": "int32"}, "expired": {"type": "boolean"}, "expiredTime": {"type": "string", "format": "date-time"}, "extra": {"type": "string"}, "feesType": {"type": "string", "enum": ["Fee", "Free", "LimitedFree"]}, "id": {"type": "integer", "format": "int64"}, "marketFlowId": {"type": "string"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "teamId": {"type": "integer", "format": "int64"}}}, "MarketFlowImageVo": {"title": "MarketFlowImageVo", "type": "object", "properties": {"description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "url": {"type": "string"}, "videoUrl": {"type": "string", "description": "不为空表示该张图片有可播放的视频"}}}, "MarketFlowPlatformVo": {"title": "MarketFlowPlatformVo", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "marketFlowId": {"type": "integer", "format": "int64"}, "platformName": {"type": "string"}}}, "MarketFlowVo": {"title": "MarketFlowVo", "type": "object", "properties": {"allowRead": {"type": "boolean"}, "buyoutPrice": {"type": "integer", "description": "买断价格，为空表示不支持该购买方式", "format": "int32"}, "category": {"type": "string", "enum": ["Demo", "Ecommerce", "Others", "Payment", "Social", "TikTok"]}, "configId": {"type": "string"}, "cornerMark": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "feesType": {"type": "string", "description": "收费类型", "enum": ["Fee", "Free", "LimitedFree"]}, "icon": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "longDescription": {"type": "string"}, "monthPrice": {"type": "integer", "description": "月价格，为空表示不支持该购买方式", "format": "int32"}, "name": {"type": "string"}, "platforms": {"type": "array", "description": "适用平台列表", "items": {"$ref": "#/components/schemas/MarketFlowPlatformVo"}}, "provider": {"type": "string"}, "publishTime": {"type": "string", "format": "date-time"}, "rpaType": {"type": "string", "description": "流程类型，分手机和browser", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "score": {"type": "integer", "format": "int32"}, "sortNo": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Deleted", "Normal", "OffLine"]}, "updateTime": {"type": "string", "format": "date-time"}, "url": {"type": "string"}, "userCount": {"type": "integer", "description": "用户数", "format": "int32"}, "version": {"type": "string"}, "weekPrice": {"type": "integer", "description": "周价格，为空表示不支持该购买方式", "format": "int32"}}}, "NewRpaTaskRequest": {"title": "NewRpaTaskRequest", "type": "object", "properties": {"caseBrowserOpened": {"type": "string", "description": "当账号的浏览器已打开时的策略：stop | reopen | reuse"}, "closeBrowserOnEnd": {"type": "string", "description": "流程结束后是否关闭账号浏览器：close | keep"}, "cloudInstanceId": {"type": "integer", "format": "int64"}, "concurrent": {"type": "integer", "format": "int32"}, "concurrentDelay": {"type": "integer", "description": "并发等待时间间隔", "format": "int32"}, "description": {"type": "string"}, "deviceId": {"type": "string"}, "environments": {"type": "object", "description": "任务里可以通过 rpa.getEnv(key) 来获取传递的值"}, "forceRecord": {"type": "boolean"}, "formId": {"type": "string"}, "headless": {"type": "boolean", "description": "是否以无头模式运行", "example": false}, "layoutConfig": {"type": "string", "description": "窗口布局配置,json格式"}, "manualRun": {"type": "boolean", "description": "是否手动执行", "example": false}, "name": {"type": "string"}, "params": {"type": "object", "additionalProperties": {"type": "object"}, "description": "创建一个task时用来指定变量值。如果指定的key不在流程定义里会被忽略"}, "popupTaskLog": {"type": "boolean", "description": "是否自动弹出流程日志窗口，为空表示false", "example": false}, "provider": {"type": "string", "description": "云端执行的云厂商", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "region": {"type": "string", "description": "云端执行的区域"}, "rpaFlowId": {"type": "integer", "format": "int64"}, "rpaFlowVersion": {"type": "string", "description": "手动直接执行一个流程的时候（不包含计划）支持直接执行指定版本"}, "runOnCloud": {"type": "boolean", "description": "是否云端执行", "example": false}, "scheduleId": {"type": "integer", "format": "int64"}, "scheduleJobId": {"type": "string", "description": "被哪个quartz调度任务触发"}, "shopIds": {"type": "array", "description": "有哪些账户参与该流程Task执行，如果是手机流程，指有哪些 mobile 参与该流程的执行", "items": {"type": "integer", "format": "int64"}}, "showMouseTrack": {"type": "boolean", "description": "是否显示鼠标轨迹", "example": false}, "sidePanel": {"type": "string", "description": "执行时侧边栏显示策略。hide | show，为空表示继承流程配置"}, "snapshot": {"type": "string", "enum": ["Node", "Not", "OnFail"]}, "sscToken": {"type": "string", "description": "快捷方式的Token"}}}, "NotifyFileAddedRequest": {"title": "NotifyFileAddedRequest", "type": "object", "properties": {"filename": {"type": "string"}, "filesize": {"type": "integer", "format": "int64"}, "rpaFileType": {"type": "string", "enum": ["Data", "Log", "Record", "Screenshot", "Unknown", "Upload"]}, "rpaTaskItemId": {"type": "integer", "format": "int64"}}}, "NotifyOssFileAddedRequest": {"title": "NotifyOssFileAddedRequest", "type": "object", "properties": {"fileType": {"type": "string", "description": "仅rpa工作目录有意义", "enum": ["Data", "Log", "Record", "Screenshot", "Unknown", "Upload"]}, "files": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}}, "rpaTaskItemId": {"type": "integer", "format": "int64"}}}, "OpenAiRequest": {"title": "OpenAiRequest", "type": "object", "properties": {"accessToken": {"type": "string"}, "aiProvider": {"type": "string"}, "customerModelId": {"type": "string"}, "origin": {"type": "boolean"}, "prompts": {"type": "array", "items": {"type": "string"}}}}, "OpenAiResponse": {"title": "OpenAiResponse", "type": "object", "properties": {"response": {"type": "string"}}}, "PageResult«MarketFlowVo»": {"title": "PageResult«MarketFlowVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/MarketFlowVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«RpaFlowShareVo»": {"title": "PageResult«RpaFlowShareVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/RpaFlowShareVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«RpaFlowVo»": {"title": "PageResult«RpaFlowVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/RpaFlowVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«RpaPlanVo»": {"title": "PageResult«RpaPlanVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/RpaPlanVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«RpaTaskVo»": {"title": "PageResult«RpaTaskVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/RpaTaskVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«RpaVoucherVo»": {"title": "PageResult«RpaVoucherVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/RpaVoucherVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PublishFlowRequest": {"title": "PublishFlowRequest", "type": "object", "properties": {"description": {"type": "string", "description": "此版本发版说明"}, "pushToSharedFlows": {"type": "boolean", "description": "同步升级共享给其它团队的流程定义与流程计划", "example": false}, "rpaFlowId": {"type": "integer", "format": "int64"}, "upgradePlan": {"type": "boolean", "description": "同步升级团队内计划的流程版本", "example": false}, "upgradeShop": {"type": "boolean", "description": "同步升级在当前团队内包含此流程定义的浏览器分身", "example": false}}}, "RenewRpaVoucherRequest": {"title": "RenewRpaVoucherRequest", "type": "object", "properties": {"agreement": {"type": "boolean", "description": "已经勾选阅读和同意使用协议，没什么用", "example": false}, "balanceAmount": {"type": "number", "description": "余额抵扣金额", "format": "bigdecimal"}, "distributionCode": {"type": "string", "description": "优惠码"}, "distributionInfo": {"$ref": "#/components/schemas/DistributionInfo"}, "immediatePay": {"type": "boolean", "description": "是否立即支付（点稍候支付该属性传false）", "example": false}, "monthDuration": {"type": "integer", "description": "其中按月续费的续几月", "format": "int32"}, "payType": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "rpaVoucherIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "voucherAmount": {"type": "number", "description": "代金券抵扣金额，不得大于代金券余额", "format": "bigdecimal"}, "voucherId": {"type": "integer", "description": "要使用的代金券id", "format": "int64"}, "weekDuration": {"type": "integer", "description": "其中按周续费的续几周", "format": "int32"}}}, "RpaAvailableRegionVo": {"title": "RpaAvailableRegionVo", "type": "object", "properties": {"city": {"type": "string"}, "country": {"type": "string"}, "price": {"type": "number", "format": "bigdecimal"}, "provider": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "region": {"type": "string"}}}, "RpaConfig": {"title": "RpaConfig", "type": "object", "properties": {"appVersion": {"type": "string", "description": "该流程保存时的客户端版本号"}, "archive": {"type": "boolean", "description": "是否归档", "example": false}, "attachments": {"type": "array", "description": "附件列表", "items": {"$ref": "#/components/schemas/流程附件"}}, "browserExitPolicy": {"type": "string", "description": "当监听到浏览器退出时的动作。为空也表示 ExitTask", "enum": ["ExitTask", "Ignore"]}, "browserPolicy": {"type": "string", "description": "打开分身浏览器的策略。兼容历史数据，为空或true都表示自动打开", "enum": ["auto", "manually"]}, "dialogHandling": {"type": "string", "description": "遇到alert等对话框时处理方式，为空表示取消，否则点确定。如果dialog是prompt，该字符串会当成值传递给prompt"}, "elements": {"type": "array", "description": "元素库列表", "items": {"$ref": "#/components/schemas/Element"}}, "events": {"type": "array", "items": {"$ref": "#/components/schemas/RpaEvent"}}, "exitOnFail": {"type": "string"}, "extra": {"type": "object"}, "id": {"type": "string", "description": "id"}, "imageForbiddenSize": {"type": "integer", "format": "int32"}, "itemPolicy": {"type": "string", "description": "分身策略", "enum": ["dynamic", "manually"]}, "loadImage": {"type": "boolean"}, "loadVideo": {"type": "boolean"}, "minorVersion": {"type": "number", "description": "执行该流程所需要的最低客户端版本号，大版本号，如客户端是6.7.0.xxxx，那该值就是6.7。为空或0表示可以执行在任何版本的客户端", "format": "double"}, "nodeInterval": {"type": "number", "format": "double"}, "nodeSim": {"type": "boolean", "description": "节点是否开启拟人操作", "example": false}, "nodeTimeout": {"type": "integer", "format": "int32"}, "nodes": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/RpaNode"}}, "paramGroups": {"type": "array", "items": {"$ref": "#/components/schemas/RpaParamGroup"}}, "params": {"type": "array", "description": "用户自定义变量", "items": {"$ref": "#/components/schemas/RpaParam"}}, "plugins": {"type": "array", "description": "插件列表", "items": {"type": "string"}}, "readme": {"type": "string", "description": "流程说明"}, "redirectNodeErr": {"type": "string", "description": "将节点的错误日志重定向到哪里？debug | info | err | none:不输出，默认err"}, "scripts": {"type": "array", "description": "代码库列表", "items": {"$ref": "#/components/schemas/Script"}}, "shadows": {"type": "array", "description": "元素映射", "items": {"$ref": "#/components/schemas/DomShadow"}}, "sidePanel": {"type": "string", "description": "执行时侧边栏显示策略。hide | show，为空表示hide。"}, "subFlows": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/RpaSubConfig"}, "description": "子流程定义，key即为子流程的sid"}, "timeout": {"type": "integer", "format": "int32"}, "type": {"type": "string", "description": "子流程类型"}, "windowMinimizedPolicy": {"type": "string", "description": "分身最小化策略。alert | ignore，为空表示 alert"}, "windowPosition": {"type": "string"}, "windowSize": {"type": "string"}}}, "RpaEvent": {"title": "RpaEvent", "type": "object", "properties": {"description": {"type": "string"}, "header": {"type": "string", "description": "要执行的事件链的第一个节点的nid"}, "name": {"type": "string"}, "params": {"type": "object"}}}, "RpaFileTriggerEventVo": {"title": "RpaFileTriggerEventVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "extraParams": {"type": "object"}, "fileEventType": {"type": "string", "enum": ["Created", "Deleted", "Modified"]}, "id": {"type": "integer", "format": "int64"}, "planId": {"type": "integer", "format": "int64"}, "rpaTaskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "triggerFile": {"type": "string"}, "triggerId": {"type": "integer", "format": "int64"}}}, "RpaFileTriggerVo": {"title": "RpaFileTriggerVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deviceId": {"type": "string"}, "dir": {"type": "string"}, "diskType": {"type": "string", "enum": ["LocalDisk", "TeamDisk", "UserDisk"]}, "fileExt": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "includeSub": {"type": "boolean"}, "planId": {"type": "integer", "format": "int64"}, "realPath": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "triggerType": {"type": "string", "enum": ["Email", "File", "Http", "Message"]}, "valid": {"type": "boolean"}, "watchCreate": {"type": "boolean"}, "watchDelete": {"type": "boolean"}, "watchModify": {"type": "boolean"}}}, "RpaFlowGroupVo": {"title": "RpaFlowGroupVo", "type": "object", "properties": {"description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "sortNumber": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "RpaFlowShareCodeVo": {"title": "RpaFlowShareCodeVo", "type": "object", "properties": {"allowPushUpdate": {"type": "boolean"}, "allowRead": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "expiredTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "includeGroups": {"type": "boolean"}, "shareCode": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "validMinutes": {"type": "integer", "format": "int32"}}}, "RpaFlowShareVo": {"title": "RpaFlowShareVo", "type": "object", "properties": {"allowPushUpdate": {"type": "boolean"}, "allowRead": {"type": "boolean"}, "creatorId": {"type": "integer", "format": "int64"}, "creatorName": {"type": "string"}, "shareCode": {"type": "string"}, "shareTime": {"type": "string"}, "targetFlowId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "teamName": {"type": "string"}, "version": {"type": "string", "description": "被分享流程当前的版本"}}}, "RpaFlowVersionVo": {"title": "RpaFlowVersionVo", "type": "object", "properties": {"configId": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "numberVersion": {"type": "integer", "format": "int32"}, "version": {"type": "string"}}}, "RpaFlowVo": {"title": "RpaFlowVo", "type": "object", "properties": {"allowPushUpdate": {"type": "boolean"}, "allowRead": {"type": "boolean", "description": "是否可读。针对分享流程和市场流程", "example": false}, "bizCode": {"type": "string"}, "configId": {"type": "string"}, "console": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createType": {"type": "string", "enum": ["FileCopy", "Manual", "Market", "MarketCopy", "Shared", "TkPack", "Tkshop"]}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "dirty": {"type": "boolean"}, "expireTime": {"type": "string", "description": "过期时间，仅针对引用市场流程", "format": "date-time"}, "expired": {"type": "boolean", "description": "是否已过期，仅针对引用市场流程，根据 expireTime 计算得出来的", "example": false}, "extra": {"type": "object"}, "flowShareCode": {"type": "string"}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/RpaFlowGroupVo"}}, "id": {"type": "integer", "format": "int64"}, "marketId": {"type": "integer", "description": "对应的市场模板ID", "format": "int64"}, "marketLatestVersion": {"type": "string", "description": "如果是市场流程，显示市场流程的最新版本"}, "name": {"type": "string"}, "nameBrief": {"type": "string"}, "numberVersion": {"type": "integer", "description": "数字版本号，会从1开始累加", "format": "int32"}, "platforms": {"type": "array", "items": {"$ref": "#/components/schemas/RpaPlatformVo"}}, "publishTime": {"type": "string", "format": "date-time"}, "rpaType": {"type": "string", "description": "流程类型，分手机和browser", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "sessionInner": {"type": "boolean"}, "shareFromTeamId": {"type": "integer", "format": "int64"}, "shareFromTeamName": {"type": "string"}, "shareLatestVersion": {"type": "string", "description": "如果是分享过来的流程，显示被分享的流程最新的版本"}, "sharedFlowId": {"type": "integer", "description": "不为空表示是他人分享的流程", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "sortNo": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Draft", "Published"]}, "supportConcurrent": {"type": "boolean"}, "teamId": {"type": "integer", "description": "团队ID;", "format": "int64"}, "teamName": {"type": "string"}, "tkFlowId": {"type": "integer", "format": "int64"}, "updateTime": {"type": "string", "format": "date-time"}, "version": {"type": "string"}}}, "RpaHttpTriggerEventVo": {"title": "RpaHttpTriggerEventVo", "type": "object", "properties": {"clientToken": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "extraParams": {"type": "object"}, "httpMethod": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "planId": {"type": "integer", "format": "int64"}, "remoteIp": {"type": "string"}, "requestId": {"type": "string"}, "rpaTaskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "token": {"type": "string"}, "triggerId": {"type": "integer", "format": "int64"}, "userAgent": {"type": "string"}}}, "RpaHttpTriggerResponseVo": {"title": "RpaHttpTriggerResponseVo", "type": "object", "properties": {"event": {"$ref": "#/components/schemas/RpaHttpTriggerEventVo"}, "requestId": {"type": "string"}, "task": {"$ref": "#/components/schemas/RpaSimpleHisVo"}}}, "RpaMessageTriggerVo": {"title": "RpaMessageTriggerVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "messageId": {"type": "string"}, "planId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "triggerType": {"type": "string", "enum": ["Email", "File", "Http", "Message"]}, "valid": {"type": "boolean"}}}, "RpaMobileInfo": {"title": "RpaMobileInfo", "type": "object", "properties": {"connectType": {"type": "string", "enum": ["ARMCLOUD", "Baidu", "QCloud", "USB", "WIFI"]}, "description": {"type": "string"}, "deviceId": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "itemId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "parentMobileId": {"type": "integer", "description": "从哪台手机联营而来", "format": "int64"}, "status": {"type": "string", "enum": ["EXPIRED", "OFFLINE", "ONLINE", "RESETING"]}, "udid": {"type": "string"}}}, "RpaNode": {"title": "RpaNode", "type": "object", "properties": {"description": {"type": "string"}, "disabled": {"type": "boolean", "description": "该节点是否禁用，禁用节点不参与执行", "example": false}, "exitOnFail": {"type": "string"}, "iframe": {"type": "string"}, "interval": {"type": "integer", "description": "当前节点执行前的间隔时间", "format": "int32"}, "logLevel": {"type": "string"}, "logMsg": {"type": "string"}, "name": {"type": "string"}, "next": {"type": "string", "description": "下一节点的nid"}, "props": {"type": "object", "description": "其它的非公用的属性"}, "redirectNodeErr": {"type": "string", "description": "将节点的错误日志重定向到哪里？inherit:继续自流程 | debug | info | err | none:不输出。默认inherit"}, "script": {"type": "string"}, "sim": {"type": "boolean"}, "timeout": {"type": "integer", "description": "当前节点执行超时时间，单位秒，0表示不超时，空表示继承全局配置", "format": "int32"}, "type": {"type": "string"}}}, "RpaParam": {"title": "RpaParam", "type": "object", "properties": {"description": {"type": "string"}, "extra": {"type": "object"}, "label": {"type": "string"}, "name": {"type": "string"}, "nodeOnly": {"type": "boolean", "description": "是不只在node环境使用的变量，如果为是，浏览器脚本是读不到这个变量的。（因为有些大变量用不到但又每次传递到浏览器会导致浏览器crash，声音nodeOnly=true可以减少浏览器脚本执行时候crash）", "example": false}, "predefine": {"type": "boolean"}, "required": {"type": "boolean", "description": "是否必填字段", "example": false}, "sensitive": {"type": "boolean", "description": "是否敏感字段", "example": false}, "type": {"type": "string"}, "val": {"type": "object"}, "validVals": {"type": "array", "items": {"type": "object"}}}}, "RpaParamGroup": {"title": "RpaParamGroup", "type": "object", "properties": {"extra": {"type": "object"}, "name": {"type": "string"}, "params": {"type": "array", "items": {"$ref": "#/components/schemas/RpaParam"}}}}, "RpaPlanEntry": {"title": "RpaPlanEntry", "type": "object", "properties": {"extra": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "meta": {"description": "元信息，用来恢复mysql", "$ref": "#/components/schemas/RpaPlanVo"}, "params": {"type": "object", "description": "流程参数"}, "schedulers": {"type": "array", "description": "调度列表", "items": {"$ref": "#/components/schemas/RpaPlanSchedulerVo"}}, "shopIds": {"type": "array", "description": "允许导入的时候指定多个账号", "items": {"type": "integer", "format": "int64"}}, "triggers": {"type": "array", "description": "触发器，todo", "items": {"$ref": "#/components/schemas/RpaTriggerDto"}}}}, "RpaPlanExports": {"title": "RpaPlanExports", "type": "object", "properties": {"description": {"type": "string", "description": "描述，保留字段"}, "exportTime": {"type": "string", "description": "导出时间"}, "extra": {"type": "object"}, "plans": {"type": "array", "items": {"$ref": "#/components/schemas/RpaPlanEntry"}}}}, "RpaPlanGroup": {"title": "RpaPlanGroup", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deviceId": {"type": "string"}, "groupName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "paused": {"type": "boolean"}, "runOnCloud": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}}}, "RpaPlanGroupVo": {"title": "RpaPlanGroupVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deviceId": {"type": "string"}, "groupName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "loginDevice": {"$ref": "#/components/schemas/LoginDeviceDto"}, "paused": {"type": "boolean"}, "planCount": {"type": "integer", "format": "int32"}, "runOnCloud": {"type": "boolean"}, "shops": {"type": "array", "items": {"$ref": "#/components/schemas/ShopWithChannelsVo"}}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}}}, "RpaPlanSchedulerVo": {"title": "RpaPlanSchedulerVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "planId": {"type": "integer", "format": "int64"}, "scheduleType": {"type": "string", "enum": ["Duration", "Fixed", "Temp"]}, "teamId": {"type": "integer", "format": "int64"}, "triggerCron": {"type": "string"}, "triggerEndCron": {"type": "string"}}}, "RpaPlanVo": {"title": "RpaPlanVo", "type": "object", "properties": {"allowOthersEdit": {"type": "boolean", "description": "是否允许他人编辑此计划", "example": false}, "caseBrowserOpened": {"type": "string", "description": "当账号的浏览器已打开时的策略：stop | reopen | reuse"}, "city": {"type": "string"}, "clientId": {"type": "string", "description": "调度到指定客户端执行，为空表示选任一个"}, "closeBrowserOnEnd": {"type": "string", "description": "流程结束后是否关闭账号浏览器：close | keep"}, "cloudInstanceId": {"type": "integer", "format": "int64"}, "concurrent": {"type": "integer", "format": "int32"}, "concurrentDelay": {"type": "integer", "description": "并发等待时间间隔", "format": "int32"}, "country": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "cronExpression": {"type": "string", "description": "调度表达式，例如：[30 15 15 ? * MON,SUN,FRI] 见https://www.bejson.com/othertools/cron/"}, "description": {"type": "string"}, "deviceInfo": {"type": "object"}, "duration": {"type": "integer", "description": "间隔时间（只有循环任务才有意义）", "format": "int32"}, "enableSchedule": {"type": "boolean"}, "flowBizCode": {"type": "string"}, "flowConfigId": {"type": "string"}, "flowId": {"type": "integer", "format": "int64"}, "flowName": {"type": "string"}, "flowVersion": {"type": "string", "description": "流程对应的版本号"}, "forceRecord": {"type": "boolean"}, "groupId": {"type": "integer", "format": "int64"}, "headless": {"type": "boolean", "description": "是否以无头模式运行", "example": false}, "id": {"type": "integer", "format": "int64"}, "itemPolicy": {"type": "string", "description": "分身策略", "enum": ["dynamic", "manually"]}, "latestVersion": {"type": "string", "description": "相应流程的最新版本"}, "layoutConfig": {"type": "string", "description": "窗口布局配置,json格式"}, "maxMinutes": {"type": "integer", "description": "该计划最大执行时长，单位分钟。相应的流程如果执行超过这个值会被强制结束。为空或为0表示不限制", "format": "int32"}, "name": {"type": "string"}, "nextFireTime": {"type": "string", "description": "如果是自动或循环计划，该字段为下次触发时间", "format": "date-time"}, "othersRunPolicy": {"type": "string", "description": "他人执行此流程的策略。 为空表示只能创建者可以执行，非空可选值为：creator | others | creator_others"}, "paramFilePath": {"type": "string", "description": "变量定义文件路径;user_disk:// 或者 team_disk:// 开头才有效"}, "paused": {"type": "boolean"}, "planEventDelay": {"type": "integer", "format": "int32"}, "planEventName": {"type": "string"}, "planType": {"type": "string", "enum": ["Auto", "Loop", "Manual", "<PERSON><PERSON>"]}, "popupTaskLog": {"type": "boolean", "description": "是否自动弹出流程日志窗口，为空表示false", "example": false}, "provider": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "region": {"type": "string"}, "rpaType": {"type": "string", "description": "流程类型，分手机和browser", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "runOnCloud": {"type": "boolean"}, "shops": {"type": "array", "items": {"$ref": "#/components/schemas/ItemShopInfo"}}, "showMouseTrack": {"type": "boolean", "description": "是否显示鼠标轨迹", "example": false}, "sidePanel": {"type": "string", "description": "执行时侧边栏显示策略。hide | show，为空表示继承流程配置"}, "snapshot": {"type": "string", "enum": ["Node", "Not", "OnFail"]}, "snapshotScope": {"type": "string", "enum": ["fullPage", "pdf", "viewport", "window"]}, "startEventDelay": {"type": "integer", "format": "int32"}, "startEventName": {"type": "string"}, "stopOnError": {"type": "boolean", "description": "遇到错误是否退出循环任务（只有循环任务才有意义）", "example": false}, "systemType": {"type": "string", "enum": ["normal", "tk"]}, "taskNameEl": {"type": "string", "description": "任务命名规则"}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}, "totalShopCount": {"type": "integer", "description": "该计划总账号个数", "format": "int32"}, "triggerPlanEvent": {"type": "boolean", "description": "计划结束后是否触发一个计划事件", "example": false}, "triggerStartEvent": {"type": "boolean", "description": "计划开始后是否触发一个计划事件", "example": false}, "triggers": {"type": "array", "items": {"$ref": "#/components/schemas/RpaTriggerDto"}}, "updateTime": {"type": "string", "format": "date-time"}, "zonedTime": {"$ref": "#/components/schemas/LocalTime"}}}, "RpaPlatformVo": {"title": "RpaPlatformVo", "type": "object", "properties": {"flowId": {"type": "integer", "format": "int64"}, "platformName": {"type": "string"}}}, "RpaRunDeviceVo": {"title": "RpaRunDeviceVo", "type": "object", "properties": {"appId": {"type": "string"}, "cpus": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "currentDevice": {"type": "boolean"}, "deviceId": {"type": "string"}, "deviceType": {"type": "string", "enum": ["App", "Browser", "Extension", "HYRuntime", "RpaExecutor"]}, "hostName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastActiveTime": {"type": "string", "format": "date-time"}, "lastLoginTime": {"type": "string", "format": "date-time"}, "mem": {"type": "integer", "format": "int64"}, "online": {"type": "boolean"}, "osName": {"type": "string"}, "price": {"type": "number", "format": "bigdecimal"}, "scope": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ipp", "Openapi", "Partner", "Portal"]}, "userAgent": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "vouchers": {"type": "array", "items": {"$ref": "#/components/schemas/RpaVoucherVo"}}}}, "RpaShopInfo": {"title": "RpaShopInfo", "type": "object", "properties": {"account": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "itemId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "platform": {"$ref": "#/components/schemas/ShopPlatformVo"}}}, "RpaShopPasswordVo": {"title": "RpaShopPasswordVo", "type": "object", "properties": {"actionUrl": {"type": "string"}, "blacklistedByUser": {"type": "integer", "format": "int32"}, "dateCreated": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "originUrl": {"type": "string"}, "passwordElement": {"type": "string"}, "passwordType": {"type": "integer", "format": "int32"}, "passwordValue": {"type": "string"}, "platformId": {"type": "integer", "format": "int64"}, "scheme": {"type": "integer", "format": "int32"}, "shopId": {"type": "integer", "format": "int64"}, "signonRealm": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "updateTime": {"type": "string", "format": "date-time"}, "usernameElement": {"type": "string"}, "usernameValue": {"type": "string"}}}, "RpaSimpleHisVo": {"title": "RpaSimpleHisVo", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "done": {"type": "boolean", "description": "是否结束", "example": false}, "errorCode": {"type": "integer", "description": "#see RpaFailReason.xxx", "format": "int32"}, "errorMsg": {"type": "string"}, "executorId": {"type": "integer", "description": "执行者身份。历史数据访字段为空，展示的时候使用creatorId", "format": "int64"}, "failedItems": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "manualRun": {"type": "boolean"}, "name": {"type": "string"}, "planId": {"type": "integer", "format": "int64"}, "planName": {"type": "string"}, "rpaType": {"type": "string", "description": "流程类型，分手机和browser", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "status": {"type": "string", "enum": ["Cancelled", "CreateFailed", "Ended", "Ended_All_Failed", "Ended_Partial_Failed", "Ignored", "NotStart", "Running", "ScheduleCancelled", "Scheduled", "Scheduling", "UnusualEnded"]}, "successItems": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}, "totalItems": {"type": "integer", "format": "int32"}}}, "RpaSubConfig": {"title": "RpaSubConfig", "type": "object", "properties": {"appVersion": {"type": "string", "description": "该流程保存时的客户端版本号"}, "archive": {"type": "boolean", "description": "是否归档", "example": false}, "attachments": {"type": "array", "description": "附件列表", "items": {"$ref": "#/components/schemas/流程附件"}}, "browserExitPolicy": {"type": "string", "description": "当监听到浏览器退出时的动作。为空也表示 ExitTask", "enum": ["ExitTask", "Ignore"]}, "browserPolicy": {"type": "string", "description": "打开分身浏览器的策略。兼容历史数据，为空或true都表示自动打开", "enum": ["auto", "manually"]}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "dialogHandling": {"type": "string", "description": "遇到alert等对话框时处理方式，为空表示取消，否则点确定。如果dialog是prompt，该字符串会当成值传递给prompt"}, "elements": {"type": "array", "description": "元素库列表", "items": {"$ref": "#/components/schemas/Element"}}, "events": {"type": "array", "items": {"$ref": "#/components/schemas/RpaEvent"}}, "exitOnFail": {"type": "string"}, "extra": {"type": "object"}, "id": {"type": "string", "description": "id"}, "imageForbiddenSize": {"type": "integer", "format": "int32"}, "itemPolicy": {"type": "string", "description": "分身策略", "enum": ["dynamic", "manually"]}, "loadImage": {"type": "boolean"}, "loadVideo": {"type": "boolean"}, "minorVersion": {"type": "number", "description": "执行该流程所需要的最低客户端版本号，大版本号，如客户端是6.7.0.xxxx，那该值就是6.7。为空或0表示可以执行在任何版本的客户端", "format": "double"}, "name": {"type": "string"}, "nodeInterval": {"type": "number", "format": "double"}, "nodeSim": {"type": "boolean", "description": "节点是否开启拟人操作", "example": false}, "nodeTimeout": {"type": "integer", "format": "int32"}, "nodes": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/RpaNode"}}, "paramGroups": {"type": "array", "items": {"$ref": "#/components/schemas/RpaParamGroup"}}, "params": {"type": "array", "description": "用户自定义变量", "items": {"$ref": "#/components/schemas/RpaParam"}}, "plugins": {"type": "array", "description": "插件列表", "items": {"type": "string"}}, "readme": {"type": "string", "description": "流程说明"}, "redirectNodeErr": {"type": "string", "description": "将节点的错误日志重定向到哪里？debug | info | err | none:不输出，默认err"}, "refFlowId": {"type": "integer", "description": "子流程所对应的流程id，可能为空", "format": "int64"}, "refFlowVersion": {"type": "string", "description": "子流程版本号，可能为空"}, "scripts": {"type": "array", "description": "代码库列表", "items": {"$ref": "#/components/schemas/Script"}}, "shadows": {"type": "array", "description": "元素映射", "items": {"$ref": "#/components/schemas/DomShadow"}}, "sidePanel": {"type": "string", "description": "执行时侧边栏显示策略。hide | show，为空表示hide。"}, "subFlows": {"type": "object", "additionalProperties": {}, "description": "子流程定义，key即为子流程的sid"}, "timeout": {"type": "integer", "format": "int32"}, "type": {"type": "string", "description": "子流程类型"}, "windowMinimizedPolicy": {"type": "string", "description": "分身最小化策略。alert | ignore，为空表示 alert"}, "windowPosition": {"type": "string"}, "windowSize": {"type": "string"}}}, "RpaTaskFileVo": {"title": "RpaTaskFileVo", "type": "object", "properties": {"fileName": {"type": "string"}, "fileType": {"type": "string", "enum": ["Data", "Log", "Record", "Screenshot", "Unknown", "Upload"]}, "id": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Deleted", "Undefined", "<PERSON><PERSON>"]}, "taskId": {"type": "integer", "format": "int64"}, "taskItemId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "RpaTaskHeartbeatRequest": {"title": "RpaTaskHeartbeatRequest", "type": "object", "properties": {"liveItems": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "rpaTaskId": {"type": "integer", "format": "int64"}}}, "RpaTaskItemVo": {"title": "RpaTaskItemVo", "type": "object", "properties": {"baseDir": {"type": "string"}, "bucketId": {"type": "integer", "format": "int64"}, "errorMsg": {"type": "string"}, "executeEndTime": {"type": "string", "format": "date-time"}, "executeTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "rpaType": {"type": "string", "description": "流程类型，分手机和browser", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "sessionId": {"type": "integer", "format": "int64"}, "shop": {"description": "带上相应的店铺信息", "$ref": "#/components/schemas/ItemShopInfo"}, "shopId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Cancelled", "CreateFailed", "Ended", "Ended_All_Failed", "Ended_Partial_Failed", "Ignored", "NotStart", "Running", "ScheduleCancelled", "Scheduled", "Scheduling", "UnusualEnded"]}, "success": {"type": "boolean"}, "taskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["postrun", "prerun", "shop"]}}}, "RpaTaskNodeVo": {"title": "RpaTaskNodeVo", "type": "object", "properties": {"error": {"type": "string"}, "nid": {"type": "string"}, "success": {"type": "boolean"}}}, "RpaTaskReport": {"title": "RpaTaskReport", "type": "object", "properties": {"commonParams": {"type": "object"}, "createTime": {"type": "string", "format": "date-time"}, "environments": {"type": "object"}, "id": {"type": "string", "description": "id"}, "taskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "RpaTaskVo": {"title": "RpaTaskVo", "type": "object", "properties": {"caseBrowserOpened": {"type": "string", "description": "当账号的浏览器已打开时的策略：stop | reopen | reuse"}, "city": {"type": "string"}, "clientId": {"type": "string"}, "clientIp": {"type": "string"}, "closeBrowserOnEnd": {"type": "string", "description": "流程结束后是否关闭账号浏览器：close | keep"}, "cloudInstanceId": {"type": "integer", "format": "int64"}, "concurrent": {"type": "integer", "format": "int32"}, "concurrentDelay": {"type": "integer", "description": "并发等待时间间隔", "format": "int32"}, "configId": {"type": "string"}, "console": {"type": "boolean"}, "country": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "createType": {"type": "string", "enum": ["FileCopy", "Manual", "Market", "MarketCopy", "Shared", "TkPack", "Tkshop"]}, "creatorId": {"type": "integer", "format": "int64"}, "credit": {"type": "number", "description": "扣掉了多少个花瓣", "format": "bigdecimal"}, "creditDetailId": {"type": "integer", "format": "int64"}, "creditDetailSerialNumber": {"type": "string"}, "description": {"type": "string"}, "deviceName": {"type": "string"}, "deviceUserAgent": {"type": "string"}, "done": {"type": "boolean", "description": "是否结束", "example": false}, "errorCode": {"type": "integer", "description": "#see RpaFailReason.xxx", "format": "int32"}, "errorMsg": {"type": "string"}, "executeEndTime": {"type": "string", "format": "date-time"}, "executeTime": {"type": "string", "format": "date-time"}, "executorId": {"type": "integer", "description": "执行者身份。历史数据访字段为空，展示的时候使用creatorId", "format": "int64"}, "failedItems": {"type": "integer", "format": "int32"}, "fileLocked": {"type": "boolean"}, "fileSize": {"type": "integer", "description": "总文件大小，不包括.log文件", "format": "int64"}, "fileStatus": {"type": "string", "enum": ["Deleted", "Undefined", "<PERSON><PERSON>"]}, "firstItem": {"description": "第一个item的详细信息", "$ref": "#/components/schemas/RpaTaskItemVo"}, "flowId": {"type": "integer", "format": "int64"}, "flowName": {"type": "string"}, "flowVersion": {"type": "string"}, "forceRecord": {"type": "boolean"}, "headless": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "layoutConfig": {"type": "string", "description": "窗口布局配置,json格式"}, "manualRun": {"type": "boolean"}, "name": {"type": "string"}, "planId": {"type": "integer", "format": "int64"}, "planName": {"type": "string"}, "planType": {"type": "string", "description": "计划类型（如果是计划触发）", "enum": ["Auto", "Loop", "Manual", "<PERSON><PERSON>"]}, "popupTaskLog": {"type": "boolean", "description": "是否自动弹出流程日志窗口，为空表示false", "example": false}, "preview": {"type": "boolean"}, "price": {"type": "number", "description": "消耗单价:花瓣/分钟", "format": "bigdecimal"}, "provider": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "region": {"type": "string"}, "rpaType": {"type": "string", "description": "流程类型，分手机和browser", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "rpaVoucherId": {"type": "integer", "format": "int64"}, "runOnCloud": {"type": "boolean"}, "shops": {"type": "array", "items": {"$ref": "#/components/schemas/ItemShopInfo"}}, "showMouseTrack": {"type": "boolean", "description": "是否显示鼠标轨迹", "example": false}, "sidePanel": {"type": "string", "description": "执行时侧边栏显示策略。hide | show，为空表示继承流程配置"}, "snapshot": {"type": "string", "enum": ["Node", "Not", "OnFail"]}, "status": {"type": "string", "enum": ["Cancelled", "CreateFailed", "Ended", "Ended_All_Failed", "Ended_Partial_Failed", "Ignored", "NotStart", "Running", "ScheduleCancelled", "Scheduled", "Scheduling", "UnusualEnded"]}, "successItems": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}, "totalItems": {"type": "integer", "format": "int32"}, "triggerType": {"type": "string", "description": "触发类型", "enum": ["Email", "File", "Http", "Loop", "Message", "Schedule"]}}}, "RpaTriggerDto": {"title": "RpaTriggerDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "planId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "triggerType": {"type": "string", "enum": ["Email", "File", "Http", "Message"]}, "valid": {"type": "boolean"}}}, "RpaVoucherHisVo": {"title": "RpaVoucherHisVo", "type": "object", "properties": {"bindTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "creatorName": {"type": "string"}, "deviceCode": {"type": "string"}, "deviceId": {"type": "integer", "format": "int64"}, "deviceName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "rpaVoucherId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "unbindTime": {"type": "string", "format": "date-time"}}}, "RpaVoucherVo": {"title": "RpaVoucherVo", "type": "object", "properties": {"autoRenew": {"type": "boolean"}, "bornType": {"type": "string", "enum": ["PartnerGiving", "TeamBuy", "Unknown"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "creatorName": {"type": "string"}, "deleted": {"type": "boolean"}, "device": {"$ref": "#/components/schemas/LoginDeviceDto"}, "deviceId": {"type": "integer", "format": "int64"}, "goodsId": {"type": "integer", "format": "int64"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "maxCpuCount": {"type": "integer", "format": "int32"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "renewPrice": {"type": "number", "format": "bigdecimal"}, "rpaVoucherStatus": {"type": "string", "enum": ["Expired", "<PERSON><PERSON>"]}, "serialNumber": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "validEndTime": {"type": "string", "format": "date-time"}, "validStartTime": {"type": "string", "format": "date-time"}}}, "RunPlanRequest": {"title": "RunPlanRequest", "type": "object", "properties": {"deviceId": {"type": "string"}, "executorId": {"type": "integer", "description": "以谁的身份去执行", "format": "int64"}, "formId": {"type": "string"}, "name": {"type": "string"}, "params": {"type": "object", "additionalProperties": {"type": "object"}, "description": "创建一个task时用来指定变量值。如果指定的key不在流程定义里会被忽略"}, "provider": {"type": "string", "description": "云端执行的云厂商", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "region": {"type": "string", "description": "云端执行的区域"}, "runOnCloud": {"type": "boolean", "description": "是否云端执行", "example": false}, "scheduleId": {"type": "integer", "format": "int64"}, "scheduleJobId": {"type": "string"}}}, "RunTaskItemRequest": {"title": "RunTaskItemRequest", "type": "object", "properties": {"rpaTaskItemId": {"type": "integer", "format": "int64"}, "sessionId": {"type": "integer", "description": "相应的账户会话id", "format": "int64"}}}, "Script": {"title": "<PERSON><PERSON><PERSON>", "type": "object", "properties": {"content": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}}}, "SendNotifyRequest": {"title": "SendNotifyRequest", "type": "object", "properties": {"attachments": {"type": "array", "items": {"type": "string"}}, "bizId": {"type": "string"}, "businessType": {"type": "string"}, "content": {"type": "string"}, "detail": {"type": "string"}, "method": {"type": "string"}, "openIds": {"type": "array", "items": {"type": "string"}}, "rpaFlowId": {"type": "integer", "format": "int64"}, "rpaTaskId": {"type": "integer", "format": "int64"}, "rpaTaskItemId": {"type": "integer", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "subject": {"type": "string"}, "type": {"type": "string"}, "url": {"type": "string"}, "userIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "userStrs": {"type": "array", "items": {"type": "string"}}}}, "SendNotifyResponse": {"title": "SendNotifyResponse", "type": "object", "properties": {"expectCount": {"type": "integer", "description": "预计发送多少个", "format": "int32"}, "successCount": {"type": "integer", "description": "成功多少个人", "format": "int32"}}}, "SendNotifyUsers": {"title": "SendNotifyUsers", "type": "object", "properties": {"method": {"type": "string"}, "userIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "userStrs": {"type": "array", "items": {"type": "string"}}}}, "ShareFlowCheckInfo": {"title": "ShareFlowCheckInfo", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "platforms": {"type": "array", "items": {"$ref": "#/components/schemas/RpaPlatformVo"}}, "version": {"type": "string"}}}, "ShopChannelVo": {"title": "ShopChannelVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "dynamicStrategy": {"type": "string", "enum": ["Off", "<PERSON><PERSON><PERSON>", "SwitchOnSession"]}, "id": {"type": "integer", "format": "int64"}, "ip": {"description": "通道的IP", "$ref": "#/components/schemas/TeamIpVo"}, "ipId": {"type": "integer", "format": "int64"}, "ipPool": {"description": "通道的IP池", "$ref": "#/components/schemas/IpPoolDto"}, "ippId": {"type": "integer", "format": "int64"}, "locationId": {"type": "integer", "format": "int64"}, "locationLevel": {"type": "string", "enum": ["City", "Continent", "Country", "District", "None", "Province", "Unknown"]}, "officialChannelId": {"type": "integer", "format": "int64"}, "primary": {"type": "boolean"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "ShopPlatformVo": {"title": "ShopPlatformVo", "type": "object", "properties": {"area": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "category": {"type": "string", "enum": ["IM", "Mail", "Other", "Payment", "Shop", "SocialMedia"]}, "frontUrl": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "loginUrl": {"type": "string"}, "name": {"type": "string"}, "typeName": {"type": "string"}}}, "ShopWithChannelsVo": {"title": "ShopWithChannelsVo", "type": "object", "properties": {"channels": {"type": "array", "description": "通道列表", "items": {"$ref": "#/components/schemas/ShopChannelVo"}}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "operatingCategory": {"type": "string", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "platformArea": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "platformId": {"type": "integer", "format": "int64"}, "platformName": {"type": "string"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "securityPolicyEnabled": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}}}, "StsPostSignature": {"title": "StsPostSignature", "type": "object", "properties": {"accessKeyId": {"type": "string"}, "accessKeySecret": {"type": "string"}, "bucketName": {"type": "string"}, "expiration": {"type": "string"}, "fileVal": {"type": "string"}, "policy": {"type": "string"}, "provider": {"type": "string"}, "region": {"type": "string"}, "securityToken": {"type": "string"}, "serverTime": {"type": "string", "format": "date-time"}, "url": {"type": "string"}}}, "StunInfo": {"title": "StunInfo", "type": "object", "properties": {"channel": {"type": "string"}, "password": {"type": "string"}, "signaling": {"type": "string"}, "turn": {"type": "string"}, "username": {"type": "string"}}}, "SyncFilesRequest": {"title": "SyncFilesRequest", "type": "object", "properties": {"existAction": {"type": "string"}, "rpaTaskItemId": {"type": "integer", "format": "int64"}, "source": {"type": "string"}, "target": {"type": "string"}}}, "TeamAiConfig": {"title": "TeamAiConfig", "type": "object", "properties": {"aiAgent": {"type": "boolean", "description": "是否允许使用aiAgent", "example": false}, "chat": {"type": "boolean", "description": "是否开启聊天功能", "example": false}, "chatAllowCoding": {"type": "boolean", "description": "是否允许写代码", "example": false}, "chatLimitScope": {"type": "boolean", "description": "是否限制聊天范围", "example": false}, "rpaChat": {"type": "boolean", "description": "是否开启RPA聊天功能", "example": false}, "rpaChatAllowCoding": {"type": "boolean", "description": "是否允许RPA写代码", "example": false}, "rpaChatLimitScope": {"type": "boolean", "description": "是否限制RPA聊天范围", "example": false}}}, "TeamIpVo": {"title": "TeamIpVo", "type": "object", "properties": {"autoRenew": {"type": "boolean"}, "cloudProvider": {"type": "string"}, "cloudRegion": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "directDownTraffic": {"type": "integer", "format": "int64"}, "directUpTraffic": {"type": "integer", "format": "int64"}, "domestic": {"type": "boolean"}, "downTraffic": {"type": "integer", "format": "int64"}, "dynamic": {"type": "boolean"}, "eipId": {"type": "integer", "format": "int64"}, "enableWhitelist": {"type": "boolean"}, "expireStatus": {"type": "string", "description": "过期状态", "enum": ["Expired", "Expiring", "Normal"]}, "forbiddenLongLatitude": {"type": "boolean"}, "gatewayId": {"type": "integer", "format": "int64"}, "goodsId": {"type": "integer", "format": "int64"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "importType": {"type": "string", "enum": ["Platform", "User"]}, "invalidTime": {"type": "string", "format": "date-time"}, "ip": {"type": "string"}, "lastProbeTime": {"type": "string", "format": "date-time"}, "latitude": {"type": "number", "format": "double"}, "locale": {"type": "string"}, "locationId": {"type": "integer", "format": "int64"}, "longitude": {"type": "number", "format": "double"}, "name": {"type": "string"}, "networkType": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "originalTeam": {"type": "integer", "format": "int64"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "pipeType": {"type": "string", "enum": ["None", "Proxy", "Tunnel", "TunnelFailToProxy"]}, "preferTransit": {"type": "integer", "format": "int64"}, "probeError": {"type": "string"}, "providerName": {"type": "string", "description": "供应商名称"}, "realIp": {"type": "string"}, "refreshUrl": {"type": "string"}, "remoteLogin": {"type": "boolean"}, "renewPrice": {"type": "number", "format": "bigdecimal"}, "source": {"type": "string"}, "speedLimit": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Available", "Pending", "Unavailable"]}, "sticky": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "testingTime": {"type": "integer", "format": "int32"}, "timezone": {"type": "string"}, "traffic": {"type": "number", "format": "double"}, "trafficCurrency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "trafficPrice": {"type": "number", "format": "bigdecimal"}, "trafficUnlimited": {"type": "boolean"}, "transitType": {"type": "string", "enum": ["Auto", "Direct", "Transit"]}, "tunnelTypes": {"type": "string"}, "upTraffic": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}, "validEndDate": {"type": "string", "format": "date-time"}, "vpsId": {"type": "integer", "format": "int64"}}}, "ToggleRpaVoucherAutoRenewRequest": {"title": "ToggleRpaVoucherAutoRenewRequest", "type": "object", "properties": {"autoRenew": {"type": "boolean"}, "rpaVoucherIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "Track": {"title": "Track", "type": "object", "properties": {"t": {"type": "integer", "format": "int32"}, "x": {"type": "integer", "format": "int32"}, "y": {"type": "integer", "format": "int32"}}}, "TriggerFlowByBizCodeRequest": {"title": "TriggerFlowByBizCodeRequest", "type": "object", "properties": {"deviceId": {"type": "string"}, "params": {"type": "object"}, "rpaFlowBizCode": {"type": "string", "description": "流程bizCode，和rpaFlowId至少提供一个，且只在rpaFlowId为空时有意义"}, "rpaFlowId": {"type": "integer", "description": "流程id，和rpaFlowBizCode至少提供一个", "format": "int64"}, "shopId": {"type": "integer", "description": "在哪个分身上执行，如果 bizCode 对应的流程是手机流程，代表手机id", "format": "int64"}}}, "UpdateFlowGroupRequest": {"title": "UpdateFlowGroupRequest", "type": "object", "properties": {"description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "sortNumber": {"type": "integer", "format": "int32"}}}, "UpdatePlanBasicRequest": {"title": "UpdatePlanBasicRequest", "type": "object", "properties": {"description": {"type": "string"}, "flowId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "taskNameEl": {"type": "string", "description": "任务命名规则"}}}, "UpdatePlanParamsRequest": {"title": "UpdatePlanParamsRequest", "type": "object", "properties": {"paramFilePath": {"type": "string", "description": "如果流程输入变量来自网盘，保存文件路径，以 team_disk:// 或 user_disk:// 开头"}, "params": {"type": "object", "additionalProperties": {"type": "object"}, "description": "每个店铺自己的变量，如果某个shopId不在map里，则不对其进行改变"}, "sharingParams": {"type": "object", "description": "所有店铺共用的变量"}}}, "UpdatePlanPolicyRequest": {"title": "UpdatePlanPolicyRequest", "type": "object", "properties": {"allowOthersEdit": {"type": "boolean", "description": "是否允许他人编辑此计划", "example": false}, "caseBrowserOpened": {"type": "string", "description": "当账号的浏览器已打开时的策略：stop | reopen | reuse"}, "closeBrowserOnEnd": {"type": "string", "description": "流程结束后是否关闭账号浏览器：close | keep"}, "concurrent": {"type": "integer", "description": "账号并发数量", "format": "int32"}, "concurrentDelay": {"type": "integer", "format": "int32"}, "cronExpression": {"type": "string", "description": "重复日期，只包含星期信息的表达式"}, "deviceId": {"type": "string", "description": "login_device表里的device_id"}, "duration": {"type": "integer", "description": "间隔时间（只有循环任务才有意义）", "format": "int32"}, "emailTrigger": {"description": "邮件触发器", "$ref": "#/components/schemas/CreateEmailRpaTriggerVo"}, "expressions": {"type": "array", "description": "如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...]", "items": {"type": "string"}}, "fileTrigger": {"description": "文件触发器", "$ref": "#/components/schemas/CreateFileRpaTriggerVo"}, "forceRecord": {"type": "boolean"}, "headless": {"type": "boolean", "description": "是否以无头模式运行", "example": false}, "httpTrigger": {"description": "Http触发器", "$ref": "#/components/schemas/CreateHttpRpaTriggerVo"}, "layoutConfig": {"type": "string", "description": "窗口布局配置,json格式"}, "maxMinutes": {"type": "integer", "description": "该计划最大执行时长，单位分钟。相应的流程如果执行超过这个值会被强制结束。为空或为0表示不限制", "format": "int32"}, "messageTrigger": {"description": "事件触发器", "$ref": "#/components/schemas/CreateMessageRpaTriggerVo"}, "othersRunPolicy": {"type": "string", "description": "他人执行此流程的策略。 为空表示只能创建者可以执行，非空可选值为：creator | others | creator_others"}, "planEventDelay": {"type": "integer", "format": "int32"}, "planEventName": {"type": "string"}, "planType": {"type": "string", "description": "计划类型", "enum": ["Auto", "Loop", "Manual", "<PERSON><PERSON>"]}, "popupTaskLog": {"type": "boolean", "description": "是否自动弹出流程日志窗口，为空表示false", "example": false}, "provider": {"type": "string", "description": "云端执行的云厂商", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "region": {"type": "string", "description": "云端执行的区域"}, "runOnCloud": {"type": "boolean", "description": "是否云端执行", "example": false}, "showMouseTrack": {"type": "boolean", "description": "是否显示鼠标轨迹", "example": false}, "sidePanel": {"type": "string", "description": "执行时侧边栏显示策略。hide | show，为空表示继承流程配置"}, "snapshot": {"type": "string", "enum": ["Node", "Not", "OnFail"]}, "snapshotScope": {"type": "string", "enum": ["fullPage", "pdf", "viewport", "window"]}, "startEventDelay": {"type": "integer", "format": "int32"}, "startEventName": {"type": "string"}, "stopOnError": {"type": "boolean", "description": "遇到错误是否退出循环任务（只有循环任务才有意义）", "example": false}, "triggerPlanEvent": {"type": "boolean", "description": "计划结束后是否触发一个计划事件", "example": false}, "triggerStartEvent": {"type": "boolean", "description": "计划开始后是否触发一个计划事件", "example": false}}}, "UpdatePlanShopsRequest": {"title": "UpdatePlanShopsRequest", "type": "object", "properties": {"shopIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "UpdateRpaFlowGroupRequest": {"title": "UpdateRpaFlowGroupRequest", "type": "object", "properties": {"groupIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "rpaFlowId": {"type": "integer", "format": "int64"}}}, "UpdateRpaFlowRequest": {"title": "UpdateRpaFlowRequest", "type": "object", "properties": {"console": {"type": "boolean", "description": "是否控制台流程", "example": false}, "description": {"type": "string"}, "groupIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "name": {"type": "string"}, "platforms": {"type": "array", "items": {"type": "string"}}, "rpaFlowId": {"type": "integer", "format": "int64"}, "shopId": {"type": "integer", "description": "编辑用到账号id", "format": "int64"}, "version": {"type": "string"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CalcPriceResponse»": {"title": "WebResult«CalcPriceResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CalcPriceResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CalcRenewRpaVoucherResponse»": {"title": "WebResult«CalcRenewRpaVoucherResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CalcRenewRpaVoucherResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CaptchaOcrResponse»": {"title": "WebResult«CaptchaOcrResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CaptchaOcrResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CheckShareCodeResponse»": {"title": "WebResult«CheckShareCodeResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CheckShareCodeResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CreateOrderResponse»": {"title": "WebResult«CreateOrderResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CreateOrderResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«AbstractRpaShopInfo»»": {"title": "WebResult«List«AbstractRpaShopInfo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AbstractRpaShopInfo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«LoginDeviceDto»»": {"title": "WebResult«List«LoginDeviceDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/LoginDeviceDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«MarketFlowImageVo»»": {"title": "WebResult«List«MarketFlowImageVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/MarketFlowImageVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaAvailableRegionVo»»": {"title": "WebResult«List«RpaAvailableRegionVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaAvailableRegionVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaFileTriggerVo»»": {"title": "WebResult«List«RpaFileTriggerVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaFileTriggerVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaFlowGroupVo»»": {"title": "WebResult«List«RpaFlowGroupVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaFlowGroupVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaFlowShareCodeVo»»": {"title": "WebResult«List«RpaFlowShareCodeVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaFlowShareCodeVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaFlowVersionVo»»": {"title": "WebResult«List«RpaFlowVersionVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaFlowVersionVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaFlowVo»»": {"title": "WebResult«List«RpaFlowVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaFlowVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaMessageTriggerVo»»": {"title": "WebResult«List«RpaMessageTriggerVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaMessageTriggerVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaMobileInfo»»": {"title": "WebResult«List«RpaMobileInfo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaMobileInfo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaPlanGroupVo»»": {"title": "WebResult«List«RpaPlanGroupVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaPlanGroupVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaPlanVo»»": {"title": "WebResult«List«RpaPlanVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaPlanVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaRunDeviceVo»»": {"title": "WebResult«List«RpaRunDeviceVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaRunDeviceVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaShopInfo»»": {"title": "WebResult«List«RpaShopInfo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaShopInfo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaShopPasswordVo»»": {"title": "WebResult«List«RpaShopPasswordVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaShopPasswordVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaSimpleHisVo»»": {"title": "WebResult«List«RpaSimpleHisVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaSimpleHisVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaTaskFileVo»»": {"title": "WebResult«List«RpaTaskFileVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaTaskFileVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaTaskItemVo»»": {"title": "WebResult«List«RpaTaskItemVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaTaskItemVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaTaskNodeVo»»": {"title": "WebResult«List«RpaTaskNodeVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaTaskNodeVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaVoucherHisVo»»": {"title": "WebResult«List«RpaVoucherHisVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaVoucherHisVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«Track»»": {"title": "WebResult«List«Track»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Track"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«long»»": {"title": "WebResult«List«long»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«string»»": {"title": "WebResult«List«string»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "string"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«long,Map«string,object»»»": {"title": "WebResult«Map«long,Map«string,object»»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "object"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«long,RpaConfig»»": {"title": "WebResult«Map«long,RpaConfig»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/RpaConfig"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«long,long»»": {"title": "WebResult«Map«long,long»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«string,object»»": {"title": "WebResult«Map«string,object»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«MarketFlowGainsVo»": {"title": "WebResult«MarketFlowGainsVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/MarketFlowGainsVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«MarketFlowVo»": {"title": "WebResult«MarketFlowVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/MarketFlowVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«OpenAiResponse»": {"title": "WebResult«OpenAiResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OpenAiResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«MarketFlowVo»»": {"title": "WebResult«PageResult«MarketFlowVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«MarketFlowVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«RpaFlowShareVo»»": {"title": "WebResult«PageResult«RpaFlowShareVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«RpaFlowShareVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«RpaFlowVo»»": {"title": "WebResult«PageResult«RpaFlowVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«RpaFlowVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«RpaPlanVo»»": {"title": "WebResult«PageResult«RpaPlanVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«RpaPlanVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«RpaTaskVo»»": {"title": "WebResult«PageResult«RpaTaskVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«RpaTaskVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«RpaVoucherVo»»": {"title": "WebResult«PageResult«RpaVoucherVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«RpaVoucherVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaConfig»": {"title": "WebResult«RpaConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaFileTriggerEventVo»": {"title": "WebResult«RpaFileTriggerEventVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaFileTriggerEventVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaFlowGroupVo»": {"title": "WebResult«RpaFlowGroupVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaFlowGroupVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaFlowVersionVo»": {"title": "WebResult«RpaFlowVersionVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaFlowVersionVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaFlowVo»": {"title": "WebResult«RpaFlowVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaFlowVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaHttpTriggerResponseVo»": {"title": "WebResult«RpaHttpTriggerResponseVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaHttpTriggerResponseVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaPlanExports»": {"title": "WebResult«RpaPlanExports»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaPlanExports"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaPlanGroupVo»": {"title": "WebResult«RpaPlanGroupVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaPlanGroupVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaPlanGroup»": {"title": "WebResult«RpaPlanGroup»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaPlanGroup"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaPlanVo»": {"title": "WebResult«RpaPlanVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaPlanVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaTaskFileVo»": {"title": "WebResult«RpaTaskFileVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaTaskFileVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaTaskItemVo»": {"title": "WebResult«RpaTaskItemVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaTaskItemVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaTaskReport»": {"title": "WebResult«RpaTaskReport»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaTaskReport"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaTaskVo»": {"title": "WebResult«RpaTaskVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaTaskVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaVoucherVo»": {"title": "WebResult«RpaVoucherVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaVoucherVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«SendNotifyResponse»": {"title": "WebResult«SendNotifyResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/SendNotifyResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«StsPostSignature»": {"title": "WebResult«StsPostSignature»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/StsPostSignature"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«StunInfo»": {"title": "WebResult«StunInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/StunInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamAiConfig»": {"title": "WebResult«TeamAiConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamAiConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«boolean»": {"title": "WebResult«boolean»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "boolean"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«int»": {"title": "WebResult«int»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "流程附件": {"title": "流程附件", "type": "object", "properties": {"attachType": {"type": "string", "description": "附件类型，为空或者是 user 表示用户上传，对用户可见，否则是系统生成的"}, "createTime": {"type": "integer", "format": "int64"}, "key": {"type": "string", "description": "附件路径，key含有uuid，可以当id用"}, "name": {"type": "string", "description": "附件名称"}, "size": {"type": "integer", "description": "附件大小", "format": "int64"}, "type": {"type": "string", "description": "附件类型,如 png, jpg, jpeg, xlsx"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}