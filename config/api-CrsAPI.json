{"openapi": "3.0.3", "info": {"title": "CRS SubSystem API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "CRS basic API", "description": "Crs Basic Controller"}, {"name": "CRS delivery API", "description": "Crs Delivery Controller"}, {"name": "CRS order API", "description": "Crs Order Controller"}, {"name": "CRS order log API", "description": "Crs Order Log Controller"}, {"name": "CRS pcc API", "description": "Crs Pcc Controller"}, {"name": "CRS product API", "description": "Crs Product Controller"}, {"name": "CRS stock API", "description": "Crs Stock Controller"}, {"name": "CRS transfer API", "description": "Crs Transfer Controller"}, {"name": "CRS 物流订单 API", "description": "Crs Delivery Order Controller"}], "paths": {"/api/crs/team": {"post": {"tags": ["CrsBasicController"], "summary": "创建团队", "operationId": "crsTeamPost", "parameters": [{"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/delivery": {"post": {"tags": ["CrsDeliveryController"], "summary": "新增物流商授权", "operationId": "crsDeliveryPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCrsDeliveryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CrsDeliveryDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/delivery/file/{token}": {"get": {"tags": ["CrsDeliveryController"], "summary": "下载物流单文件(RPA调用)", "operationId": "crsDeliveryFileByTokenGet", "parameters": [{"name": "token", "in": "path", "description": "token", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/delivery/params": {"get": {"tags": ["CrsDeliveryController"], "summary": "获取物流商参数", "operationId": "crsDeliveryParamsGet", "parameters": [{"name": "provider", "in": "query", "description": "provider", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON>", "GBS"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«DeliveryParamVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/delivery/result/{token}": {"post": {"tags": ["CrsDeliveryController"], "summary": "更新物流单结果(RPA调用)", "operationId": "crsDeliveryResultByTokenPost", "parameters": [{"name": "token", "in": "path", "description": "token", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "success", "in": "query", "description": "是否执行成功", "required": true, "style": "form", "schema": {"type": "boolean"}}, {"name": "message", "in": "query", "description": "消息", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/delivery/resultV2/{token}": {"post": {"tags": ["CrsDeliveryController"], "summary": "更新物流单结果V2(RPA调用)", "operationId": "crsDeliveryResultV2ByTokenPost", "parameters": [{"name": "token", "in": "path", "description": "token", "required": true, "style": "simple", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDeliveryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/delivery/triggerUploadDelivery": {"post": {"tags": ["CrsDeliveryController"], "summary": "触发批量上传物流单流程", "operationId": "crsDeliveryTriggerUploadDeliveryPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TriggerUploadDeliveryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/delivery/uploadDeliveryToken": {"post": {"tags": ["CrsDeliveryController"], "summary": "生产批量上传物流单Token", "operationId": "crsDeliveryUploadDeliveryTokenPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TriggerUploadDeliveryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/delivery/{id}": {"put": {"tags": ["CrsDeliveryController"], "summary": "修改物流商授权", "operationId": "crsDeliveryByIdPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCrsDeliveryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CrsDeliveryDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["CrsDeliveryController"], "summary": "删除物流商授权", "operationId": "crsDeliveryByIdDelete", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/deliveryList": {"get": {"tags": ["CrsDeliveryController"], "summary": "团队或用户配置的物流商列表", "operationId": "crsDeliveryListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«CrsDeliveryDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/providers": {"get": {"tags": ["CrsDeliveryController"], "summary": "系统支持的物流商列表", "operationId": "crsProvidersGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«CrsDeliveryProviderVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/delivery/order/delete": {"put": {"tags": ["CrsDeliveryOrderController"], "summary": "删除物流订单", "operationId": "crsDeliveryOrderDeletePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrsIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/delivery/order/{id}": {"get": {"tags": ["CrsDeliveryOrderController"], "summary": "getDeliveryNo", "operationId": "crsDeliveryOrderByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CrsDeliveryOrderDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/delivery/orders": {"get": {"tags": ["CrsDeliveryOrderController"], "summary": "分页查询物流订单", "operationId": "crsDeliveryOrdersGet", "parameters": [{"name": "batchNo", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "consignee<PERSON><PERSON>", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "consignee<PERSON><PERSON>", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "deliveryNo", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "printed", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "shopId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["batchNo", "deliveryNo", "skus", "status"]}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "status", "in": "query", "description": "订单CRS状态", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Canceled", "Delivered", "Finished", "Shelved", "<PERSON><PERSON><PERSON><PERSON>", "WaitDelivery"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«CrsDeliveryOrderDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/delivery/printLabel": {"put": {"tags": ["CrsDeliveryOrderController"], "summary": "批量调用打印面单", "operationId": "crsDeliveryPrintLabelPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrsIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«CrsDeliveryOrderDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/delivery/skuStat": {"get": {"tags": ["CrsDeliveryOrderController"], "summary": "下载SKU统计信息", "operationId": "crsDeliverySkuStatGet", "parameters": [{"name": "shopIds", "in": "query", "description": "shopIds", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Resource"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/order/byExcel": {"post": {"tags": ["CrsOrderController"], "summary": "上传订单Excel统一接口", "operationId": "crsOrderByExcelPost", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file", "shopId"], "type": "object", "properties": {"exchanging": {"type": "boolean", "description": "exchanging"}, "file": {"type": "string", "description": "订单Excel文件", "format": "binary"}, "headerRows": {"type": "integer", "description": "headerRows", "format": "int32"}, "password": {"type": "string", "description": "password"}, "purchasing": {"type": "boolean", "description": "purchasing"}, "returning": {"type": "boolean", "description": "returning"}, "shopId": {"type": "integer", "description": "shopId", "format": "int64"}, "status": {"type": "string", "description": "手工指定订单的状态", "enum": ["Canceled", "Delivered", "Finished", "Shelved", "<PERSON><PERSON><PERSON><PERSON>", "WaitDelivery"]}}}, "encoding": {"password": {"contentType": "text/plain"}, "purchasing": {"contentType": "text/plain"}, "returning": {"contentType": "text/plain"}, "headerRows": {"contentType": "text/plain"}, "shopId": {"contentType": "text/plain"}, "exchanging": {"contentType": "text/plain"}, "status": {"contentType": "text/plain"}}}}}, "responses": {"200": {"description": "OK", "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/order/deductStock": {"put": {"tags": ["CrsOrderController"], "summary": "扣减库存", "operationId": "crsOrderDeductStockPut", "parameters": [{"name": "ids", "in": "query", "description": "ids", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/order/delete": {"put": {"tags": ["CrsOrderController"], "summary": "删除订单（需要确认）V2", "operationId": "crsOrderDeletePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrsIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["CrsOrderController"], "summary": "删除订单（需要确认）", "operationId": "crsOrderDeleteDelete", "parameters": [{"name": "ids", "in": "query", "description": "ids", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "confirm", "in": "query", "description": "confirm", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/order/item/{id}/info": {"put": {"tags": ["CrsOrderController"], "summary": "修改订单商品的申报品名、单价、数量", "operationId": "crsOrderItemByIdInfoPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "hscode", "in": "query", "description": "hscode", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "nameCn", "in": "query", "description": "nameCn", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "nameEn", "in": "query", "description": "nameEn", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "quantity", "in": "query", "description": "quantity", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "price", "in": "query", "description": "price", "required": true, "style": "form", "schema": {"type": "number", "format": "double"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/order/label/{fileId}": {"get": {"tags": ["CrsOrderController"], "summary": "下载物流面单PDF", "operationId": "crsOrderLabelByFileIdGet", "parameters": [{"name": "fileId", "in": "path", "description": "fileId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/order/validateAddress": {"put": {"tags": ["CrsOrderController"], "summary": "触发订单校验通关符", "operationId": "crsOrderValidateAddressPut", "parameters": [{"name": "ids", "in": "query", "description": "ids", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/order/validatePcc": {"put": {"tags": ["CrsOrderController"], "summary": "触发订单校验通关符", "operationId": "crsOrderValidatePccPut", "parameters": [{"name": "ids", "in": "query", "description": "ids", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/order/{id}/address": {"put": {"tags": ["CrsOrderController"], "summary": "更新订单地址", "operationId": "crsOrderByIdAddressPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "address", "in": "query", "description": "address", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "consigneePostcode", "in": "query", "description": "consigneePostcode", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "validate", "in": "query", "description": "validate", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/order/{id}/generateLabelPdf": {"post": {"tags": ["CrsOrderController"], "summary": "打印物流面单PDF", "description": "返回pdf文件ID。如果订单已经打印过，则返回原本id", "operationId": "crsOrderByIdGenerateLabelPdfPost", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/orders/audit": {"put": {"tags": ["CrsOrderController"], "summary": "审核订单", "operationId": "crsOrdersAuditPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuditOrdersRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/orders/page": {"post": {"tags": ["CrsOrderController"], "summary": "分页获取订单", "operationId": "crsOrdersPagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageCrsOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«CrsOrderDetailVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/orders/shelve": {"put": {"tags": ["CrsOrderController"], "summary": "搁置订单", "operationId": "crsOrdersShelvePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShelveOrdersRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/platformTypes": {"get": {"tags": ["CrsOrderController"], "summary": "获取CRS支持的平台列表", "operationId": "crsPlatformTypesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/returnOrders/page": {"post": {"tags": ["CrsOrderController"], "summary": "分页获取退货订单", "operationId": "crsReturnOrdersPagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageCrsReturnOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«CrsReturnOrderDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/rpaAction": {"post": {"tags": ["CrsOrderController"], "summary": "发起一个RPA动作", "operationId": "crsRpaActionPost", "parameters": [{"name": "rpaAction", "in": "query", "description": "rpaAction", "required": true, "style": "form", "schema": {"type": "string", "enum": ["SyncOrders", "SyncProduct", "SyncReturnOrders", "UploadDelivery"]}}, {"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/order/{id}/logs": {"get": {"tags": ["CrsOrderLogController"], "summary": "获取订单日志", "operationId": "crsOrderByIdLogsGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«CrsOrderLogDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/pcc/config": {"get": {"tags": ["CrsPccController"], "summary": "baodankorea.com的通关符API配置", "operationId": "crsPccConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamPccConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "post": {"tags": ["CrsPccController"], "summary": "配置baodankorea.com的通关符API", "operationId": "crsPccConfigPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeamPccConfig"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/product/byFile": {"post": {"tags": ["CrsProductController"], "summary": "上传商品信息", "operationId": "crsProductByFilePost", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file", "shopId"], "type": "object", "properties": {"file": {"type": "string", "description": "商品Excel/CSV文件", "format": "binary"}, "password": {"type": "string", "description": "password"}, "shopId": {"type": "integer", "description": "shopId", "format": "int64"}}}, "encoding": {"password": {"contentType": "text/plain"}, "shopId": {"contentType": "text/plain"}}}}}, "responses": {"200": {"description": "OK", "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/product/hscode": {"get": {"tags": ["CrsProductController"], "summary": "搜索hscode", "operationId": "crsProductHscodeGet", "parameters": [{"name": "query", "in": "query", "description": "查询字符串", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«CrsHscodeDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["CrsProductController"], "summary": "批量更新hscode和申报名", "operationId": "crsProductHscodePut", "parameters": [{"name": "hscode", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "ids", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "nameCn", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "nameEn", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "price", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "bigdecimal"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/product/images": {"post": {"tags": ["CrsProductController"], "summary": "更新子商品的图片", "description": "更新vendorItem的图片", "operationId": "crsProductImagesPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductImageRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/product/imagesV2": {"post": {"tags": ["CrsProductController"], "summary": "更新商品的图片V2", "description": "不传vendorItemId时，会用urls[0]更新主商品的imageUrl", "operationId": "crsProductImagesV2Post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductImageRequestV2"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/product/options": {"post": {"tags": ["CrsProductController"], "summary": "同步商品选项信息", "operationId": "crsProductOptionsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductOptionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/product/page": {"post": {"tags": ["CrsProductController"], "summary": "分页查询商品列表", "operationId": "crsProductPagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageCrsProductRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«CrsProductVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/product/{id}/hscode": {"put": {"tags": ["CrsProductController"], "summary": "修改商品的申报品名", "operationId": "crsProductByIdHscodePut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "hscode", "in": "query", "description": "hscode", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "nameCn", "in": "query", "description": "nameCn", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "nameEn", "in": "query", "description": "nameEn", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "price", "in": "query", "description": "price", "required": true, "style": "form", "schema": {"type": "number", "format": "double"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/stock": {"post": {"tags": ["CrsStockController"], "summary": "创建库存", "operationId": "crsStockPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveCrsStockRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CrsStockDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/stock/page": {"get": {"tags": ["CrsStockController"], "summary": "查询库存", "operationId": "crsStockPageGet", "parameters": [{"name": "orderBy", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "productId", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "unsafeOrderBy", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "vendorItemId", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "warehouseId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«CrsStockDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/stock/{id}": {"delete": {"tags": ["CrsStockController"], "summary": "删除库存记录", "operationId": "crsStockByIdDelete", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/stock/{id}/add": {"post": {"tags": ["CrsStockController"], "summary": "新增或扣减当前库存数", "operationId": "crsStockByIdAddPost", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "amount", "in": "query", "description": "amount", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/warehouse": {"post": {"tags": ["CrsStockController"], "summary": "创建或修改仓库", "operationId": "crsWarehousePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CrsWarehouseDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CrsWarehouseDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/warehouse/{id}": {"delete": {"tags": ["CrsStockController"], "summary": "删除仓库", "operationId": "crsWarehouseByIdDelete", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/warehouses": {"get": {"tags": ["CrsStockController"], "summary": "查询仓库列表", "operationId": "crsWarehousesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«CrsWarehouseDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/crs/tmon_baima/transferJob": {"post": {"tags": ["CrsTransferController"], "summary": "创建一个tmon到白马的转换任务", "operationId": "crsTmon_baimaTransferJobPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTMonBaimaTransferJobRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TaskDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AddTMonBaimaTransferJobRequest": {"title": "AddTMonBaimaTransferJobRequest", "type": "object", "properties": {"shopId": {"type": "integer", "description": "分身ID", "format": "int64"}, "sourceFile": {"type": "string", "description": "订单源文件路径：team_disk://tmon_白马/分身name/订单文件.xlsx"}}}, "AuditOrdersRequest": {"title": "AuditOrdersRequest", "type": "object", "properties": {"confirm": {"type": "boolean", "description": "确认", "example": false}, "orderIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "params": {"type": "object", "additionalProperties": {"type": "string"}, "description": "物流参数：paramKey=>itemValue"}, "provider": {"type": "string", "enum": ["<PERSON><PERSON>", "GBS"]}}}, "CreateCrsDeliveryRequest": {"title": "CreateCrsDeliveryRequest", "type": "object", "properties": {"credentialId": {"type": "string"}, "credentialSecret": {"type": "string"}, "provider": {"type": "string", "enum": ["<PERSON><PERSON>", "GBS"]}, "tenantId": {"type": "string"}}}, "CrsDeliveryDto": {"title": "CrsDeliveryDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "credentialId": {"type": "string"}, "credentialSecret": {"type": "string"}, "endpoint": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "provider": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "tenantId": {"type": "string"}, "tokenExpireTime": {"type": "string", "format": "date-time"}, "tokenJson": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}}}, "CrsDeliveryOrderDto": {"title": "CrsDeliveryOrderDto", "type": "object", "properties": {"batchNo": {"type": "string"}, "consigneeAddress": {"type": "string"}, "consigneeName": {"type": "string"}, "consigneePcc": {"type": "string"}, "consigneePhone": {"type": "string"}, "consigneePostcode": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "customerOrderNo": {"type": "string"}, "deliveryCompany": {"type": "string"}, "deliveryNo": {"type": "string"}, "deliveryOrderNo": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "labelError": {"type": "string"}, "labelUrl": {"type": "string"}, "printed": {"type": "boolean"}, "provider": {"type": "string"}, "quantity": {"type": "integer", "format": "int32"}, "shopId": {"type": "integer", "format": "int64"}, "skus": {"type": "string"}, "status": {"type": "string", "enum": ["Canceled", "Delivered", "Finished", "Shelved", "<PERSON><PERSON><PERSON><PERSON>", "WaitDelivery"]}, "teamId": {"type": "integer", "format": "int64"}, "vendorType": {"type": "string"}}}, "CrsDeliveryProviderVo": {"title": "CrsDeliveryProviderVo", "type": "object", "properties": {"desc": {"type": "string"}, "provider": {"type": "string", "enum": ["<PERSON><PERSON>", "GBS"]}}}, "CrsHscodeDto": {"title": "CrsHscodeDto", "type": "object", "properties": {"hscode": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "nameCn": {"type": "string"}, "nameEn": {"type": "string"}, "nameKr": {"type": "string"}}}, "CrsIdsRequest": {"title": "CrsIdsRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "CrsOrderDetailVo": {"title": "CrsOrderDetailVo", "type": "object", "properties": {"addressError": {"type": "string"}, "addressStatus": {"type": "string", "enum": ["Disabled", "Error", "Initial", "Success"]}, "auditTime": {"type": "string", "format": "date-time"}, "auditUser": {"type": "integer", "format": "int64"}, "buyerId": {"type": "string"}, "buyerName": {"type": "string"}, "buyerPhone": {"type": "string"}, "buyerRemark": {"type": "string"}, "cancelReason": {"type": "string"}, "cancelTime": {"type": "string", "format": "date-time"}, "consigneeAddress": {"type": "string"}, "consigneeCountry": {"type": "string"}, "consigneeName": {"type": "string"}, "consigneePcc": {"type": "string"}, "consigneePhone": {"type": "string"}, "consigneePostcode": {"type": "string"}, "crsStatus": {"type": "string", "enum": ["Canceled", "Delivered", "Finished", "Shelved", "<PERSON><PERSON><PERSON><PERSON>", "WaitDelivery"]}, "currency": {"type": "string"}, "deliveredTime": {"type": "string", "format": "date-time"}, "deliveryCode": {"type": "string"}, "deliveryCompany": {"type": "string"}, "deliveryNo": {"type": "string"}, "deliveryProvider": {"type": "string"}, "deliveryRemark": {"type": "string"}, "estimatedShippingTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "imageUrl": {"type": "string"}, "itemCount": {"type": "integer", "description": "总商品数量", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/CrsOrderItemVo"}}, "labelFileId": {"type": "string"}, "mergingId": {"type": "string"}, "orderPrice": {"type": "number", "format": "bigdecimal"}, "orderStatus": {"type": "string"}, "orderedTime": {"type": "string", "format": "date-time"}, "paidTime": {"type": "string", "format": "date-time"}, "pccError": {"type": "string"}, "pccStatus": {"type": "string", "enum": ["Disabled", "Error", "Initial", "Success"]}, "platformOrderId": {"type": "string"}, "printTime": {"type": "string", "format": "date-time"}, "printUser": {"type": "integer", "format": "int64"}, "printed": {"type": "boolean"}, "receiveTime": {"type": "string", "format": "date-time"}, "shippingPrice": {"type": "number", "format": "bigdecimal"}, "shopId": {"type": "integer", "format": "int64"}, "stockDeductedCount": {"type": "integer", "description": "已扣减库存数量", "format": "int32"}, "syncTime": {"type": "string", "format": "date-time"}, "teamId": {"type": "integer", "format": "int64"}}}, "CrsOrderItemVo": {"title": "CrsOrderItemVo", "type": "object", "properties": {"crsOrderId": {"type": "integer", "format": "int64"}, "declareName": {"type": "string"}, "declareNameEn": {"type": "string"}, "hscode": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "imageUrl": {"type": "string"}, "orderPrice": {"type": "number", "format": "bigdecimal"}, "productId": {"type": "string"}, "productName": {"type": "string"}, "productOrderId": {"type": "string"}, "productPrice": {"type": "number", "format": "bigdecimal"}, "quantity": {"type": "integer", "format": "int32"}, "sellerProductId": {"type": "string"}, "sellerProductName": {"type": "string"}, "sku": {"type": "string"}, "stockDetailId": {"type": "integer", "format": "int64"}, "stockError": {"type": "string"}, "vendorItemId": {"type": "string"}, "vendorItemName": {"type": "string"}}}, "CrsOrderLogDto": {"title": "CrsOrderLogDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "detail": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "operation": {"type": "string", "enum": ["取消搁置", "同步", "审核", "搁置", "更新申报信息", "校验地址", "获取PCC"]}, "orderId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "userName": {"type": "string"}}}, "CrsProductOptionDto": {"title": "CrsProductOptionDto", "type": "object", "properties": {"crsProductId": {"type": "integer", "format": "int64"}, "declareHeight": {"type": "number", "format": "double"}, "declareLength": {"type": "number", "format": "double"}, "declareName": {"type": "string"}, "declareNameEn": {"type": "string"}, "declareWeight": {"type": "number", "format": "double"}, "declareWidth": {"type": "number", "format": "double"}, "haveElectric": {"type": "boolean"}, "hscode": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "inWarehouse": {"type": "boolean"}, "ossImageSize": {"type": "integer", "format": "int64"}, "ossImageUrl": {"type": "string"}, "productId": {"type": "string"}, "productPrice": {"type": "number", "format": "bigdecimal"}, "sellerProductId": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "special": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "vendorItemId": {"type": "string"}, "vendorItemName": {"type": "string"}, "vendorItemSku": {"type": "string"}}}, "CrsProductVo": {"title": "CrsProductVo", "type": "object", "properties": {"brand": {"type": "string"}, "category": {"type": "string"}, "currency": {"type": "string"}, "declareName": {"type": "string"}, "declareNameEn": {"type": "string"}, "declareWeight": {"type": "number", "format": "double"}, "haveElectric": {"type": "boolean"}, "hscode": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "imageUrl": {"type": "string"}, "inWarehouse": {"type": "boolean"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/CrsProductOptionDto"}}, "ossImageSize": {"type": "integer", "format": "int64"}, "ossImageUrl": {"type": "string"}, "productId": {"type": "string"}, "productName": {"type": "string"}, "productPrice": {"type": "number", "format": "bigdecimal"}, "remark": {"type": "string"}, "sellerProductId": {"type": "string"}, "sellerProductName": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "sku": {"type": "string"}, "special": {"type": "boolean"}, "syncTime": {"type": "string", "format": "date-time"}, "teamId": {"type": "integer", "format": "int64"}}}, "CrsReturnOrderDto": {"title": "CrsReturnOrderDto", "type": "object", "properties": {"buyerId": {"type": "string"}, "buyerName": {"type": "string"}, "buyerPhone": {"type": "string"}, "companyCode": {"type": "string"}, "crsOrderId": {"type": "integer", "format": "int64"}, "exchange": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "optionCode": {"type": "string"}, "optionInfo": {"type": "string"}, "orderId": {"type": "string"}, "orderStatus": {"type": "string"}, "payeeName": {"type": "string"}, "pendingStatus": {"type": "string"}, "platformOrderId": {"type": "string"}, "productId": {"type": "string"}, "productName": {"type": "string"}, "productOrderId": {"type": "string"}, "productPrice": {"type": "number", "format": "bigdecimal"}, "quantity": {"type": "integer", "format": "int32"}, "returnProcessingStatus": {"type": "string"}, "returnReason": {"type": "string"}, "returnRequestTime": {"type": "string", "format": "date-time"}, "sellerProductCode": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "syncTime": {"type": "string", "format": "date-time"}, "teamId": {"type": "integer", "format": "int64"}}}, "CrsStockDto": {"title": "CrsStockDto", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "productId": {"type": "string"}, "quantity": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}, "vendorItemId": {"type": "string"}, "warehouseId": {"type": "integer", "format": "int64"}}}, "CrsWarehouseDto": {"title": "CrsWarehouseDto", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "vendorTypes": {"type": "string"}}}, "DeliveryParamVo": {"title": "DeliveryParamVo", "type": "object", "properties": {"paramKey": {"type": "string"}, "paramLabel": {"type": "string"}, "paramList": {"type": "array", "items": {"$ref": "#/components/schemas/ParamItem"}}}}, "ItemShopInfo": {"title": "ItemShopInfo", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "PageCrsOrderRequest": {"title": "PageCrsOrderRequest", "type": "object", "properties": {"autoMerge": {"type": "boolean"}, "buyerName": {"type": "string"}, "buyerPhone": {"type": "string"}, "consigneeName": {"type": "string"}, "consigneePhone": {"type": "string"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "platformOrderId": {"type": "string"}, "printed": {"type": "boolean"}, "shopId": {"type": "integer", "format": "int64"}, "sortField": {"type": "string", "enum": ["orderStatus", "orderedTime", "syncTime"]}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "status": {"type": "string", "description": "订单CRS状态", "enum": ["Canceled", "Delivered", "Finished", "Shelved", "<PERSON><PERSON><PERSON><PERSON>", "WaitDelivery"]}}}, "PageCrsProductRequest": {"title": "PageCrsProductRequest", "type": "object", "properties": {"pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "productId": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "sortField": {"type": "string", "enum": ["sellerProductId", "sellerProductName", "syncTime"]}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}}}, "PageCrsReturnOrderRequest": {"title": "PageCrsReturnOrderRequest", "type": "object", "properties": {"buyerName": {"type": "string"}, "buyerPhone": {"type": "string"}, "exchange": {"type": "boolean"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "platformOrderId": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "sortField": {"type": "string", "enum": ["orderId", "orderStatus", "returnRequestTime", "syncTime"]}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}}}, "PageResult«CrsDeliveryOrderDto»": {"title": "PageResult«CrsDeliveryOrderDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CrsDeliveryOrderDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«CrsOrderDetailVo»": {"title": "PageResult«CrsOrderDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CrsOrderDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«CrsOrderLogDto»": {"title": "PageResult«CrsOrderLogDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CrsOrderLogDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«CrsProductVo»": {"title": "PageResult«CrsProductVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CrsProductVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«CrsReturnOrderDto»": {"title": "PageResult«CrsReturnOrderDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CrsReturnOrderDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«CrsStockDto»": {"title": "PageResult«CrsStockDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CrsStockDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "ParamItem": {"title": "ParamItem", "type": "object", "properties": {"itemLabel": {"type": "string"}, "itemValue": {"type": "string"}}}, "ProductImagePack": {"title": "ProductImagePack", "type": "object", "properties": {"productId": {"type": "string"}, "vendorItems": {"type": "array", "items": {"$ref": "#/components/schemas/ProductVendorImageVo"}}}}, "ProductVendorImageVo": {"title": "ProductVendorImageVo", "type": "object", "properties": {"urls": {"type": "array", "items": {"type": "string"}}, "vendorItemId": {"type": "string"}}}, "ProductVendorVo": {"title": "ProductVendorVo", "type": "object", "properties": {"hscode": {"type": "string"}, "productPrice": {"type": "number", "format": "bigdecimal"}, "vendorItemId": {"type": "string"}, "vendorItemName": {"type": "string"}, "vendorItemSku": {"type": "string"}}}, "Resource": {"title": "Resource", "type": "object"}, "RpaTaskItemVo": {"title": "RpaTaskItemVo", "type": "object", "properties": {"baseDir": {"type": "string"}, "bucketId": {"type": "integer", "format": "int64"}, "errorMsg": {"type": "string"}, "executeEndTime": {"type": "string", "format": "date-time"}, "executeTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "rpaType": {"type": "string", "description": "流程类型，分手机和browser", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "sessionId": {"type": "integer", "format": "int64"}, "shop": {"description": "带上相应的店铺信息", "$ref": "#/components/schemas/ItemShopInfo"}, "shopId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Cancelled", "CreateFailed", "Ended", "Ended_All_Failed", "Ended_Partial_Failed", "Ignored", "NotStart", "Running", "ScheduleCancelled", "Scheduled", "Scheduling", "UnusualEnded"]}, "success": {"type": "boolean"}, "taskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["postrun", "prerun", "shop"]}}}, "RpaTaskVo": {"title": "RpaTaskVo", "type": "object", "properties": {"caseBrowserOpened": {"type": "string", "description": "当账号的浏览器已打开时的策略：stop | reopen | reuse"}, "city": {"type": "string"}, "clientId": {"type": "string"}, "clientIp": {"type": "string"}, "closeBrowserOnEnd": {"type": "string", "description": "流程结束后是否关闭账号浏览器：close | keep"}, "cloudInstanceId": {"type": "integer", "format": "int64"}, "concurrent": {"type": "integer", "format": "int32"}, "concurrentDelay": {"type": "integer", "description": "并发等待时间间隔", "format": "int32"}, "configId": {"type": "string"}, "console": {"type": "boolean"}, "country": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "createType": {"type": "string", "enum": ["FileCopy", "Manual", "Market", "MarketCopy", "Shared", "TkPack", "Tkshop"]}, "creatorId": {"type": "integer", "format": "int64"}, "credit": {"type": "number", "description": "扣掉了多少个花瓣", "format": "bigdecimal"}, "creditDetailId": {"type": "integer", "format": "int64"}, "creditDetailSerialNumber": {"type": "string"}, "description": {"type": "string"}, "deviceName": {"type": "string"}, "deviceUserAgent": {"type": "string"}, "done": {"type": "boolean", "description": "是否结束", "example": false}, "errorCode": {"type": "integer", "description": "#see RpaFailReason.xxx", "format": "int32"}, "errorMsg": {"type": "string"}, "executeEndTime": {"type": "string", "format": "date-time"}, "executeTime": {"type": "string", "format": "date-time"}, "executorId": {"type": "integer", "description": "执行者身份。历史数据访字段为空，展示的时候使用creatorId", "format": "int64"}, "failedItems": {"type": "integer", "format": "int32"}, "fileLocked": {"type": "boolean"}, "fileSize": {"type": "integer", "description": "总文件大小，不包括.log文件", "format": "int64"}, "fileStatus": {"type": "string", "enum": ["Deleted", "Undefined", "<PERSON><PERSON>"]}, "firstItem": {"description": "第一个item的详细信息", "$ref": "#/components/schemas/RpaTaskItemVo"}, "flowId": {"type": "integer", "format": "int64"}, "flowName": {"type": "string"}, "flowVersion": {"type": "string"}, "forceRecord": {"type": "boolean"}, "headless": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "layoutConfig": {"type": "string", "description": "窗口布局配置,json格式"}, "manualRun": {"type": "boolean"}, "name": {"type": "string"}, "planId": {"type": "integer", "format": "int64"}, "planName": {"type": "string"}, "planType": {"type": "string", "description": "计划类型（如果是计划触发）", "enum": ["Auto", "Loop", "Manual", "<PERSON><PERSON>"]}, "popupTaskLog": {"type": "boolean", "description": "是否自动弹出流程日志窗口，为空表示false", "example": false}, "preview": {"type": "boolean"}, "price": {"type": "number", "description": "消耗单价:花瓣/分钟", "format": "bigdecimal"}, "provider": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "region": {"type": "string"}, "rpaType": {"type": "string", "description": "流程类型，分手机和browser", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "rpaVoucherId": {"type": "integer", "format": "int64"}, "runOnCloud": {"type": "boolean"}, "shops": {"type": "array", "items": {"$ref": "#/components/schemas/ItemShopInfo"}}, "showMouseTrack": {"type": "boolean", "description": "是否显示鼠标轨迹", "example": false}, "sidePanel": {"type": "string", "description": "执行时侧边栏显示策略。hide | show，为空表示继承流程配置"}, "snapshot": {"type": "string", "enum": ["Node", "Not", "OnFail"]}, "status": {"type": "string", "enum": ["Cancelled", "CreateFailed", "Ended", "Ended_All_Failed", "Ended_Partial_Failed", "Ignored", "NotStart", "Running", "ScheduleCancelled", "Scheduled", "Scheduling", "UnusualEnded"]}, "successItems": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}, "totalItems": {"type": "integer", "format": "int32"}, "triggerType": {"type": "string", "description": "触发类型", "enum": ["Email", "File", "Http", "Loop", "Message", "Schedule"]}}}, "SaveCrsStockRequest": {"title": "SaveCrsStockRequest", "type": "object", "properties": {"productId": {"type": "string"}, "quantity": {"type": "integer", "format": "int32"}, "vendorItemId": {"type": "string"}, "warehouseId": {"type": "integer", "format": "int64"}}}, "ShelveOrdersRequest": {"title": "ShelveOrdersRequest", "type": "object", "properties": {"orderIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "shelved": {"type": "boolean", "description": "搁置(true)、取消搁置(false)", "example": false}}}, "TaskDto": {"title": "TaskDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "detail": {"type": "string"}, "finishTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "progress": {"type": "integer", "format": "int32"}, "remarks": {"type": "string"}, "status": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}, "targetId": {"type": "integer", "format": "int64"}, "taskType": {"type": "string", "enum": ["BatchUpdateBandwidth", "CleanColdTable", "CleanMongo", "CleanTable", "CopyTkCreatorToClient", "CrsTransferOrder", "DbTransfer", "DeleteIps", "FireOpsMessage", "ImportAccounts", "ImportIp", "ImportIppIps", "ImportIps", "ImportShop", "InstallGlider", "OpenaiChatGenerate", "OpenaiChatTranslate", "ProbeBatchLaunchInstance", "ProbeIps", "RebootIp", "RefreshClash", "RefreshExtensions", "RepairGhLiveTime", "RepairKolLiveRate", "RepairKolLiveTime", "RepairOps", "RepairTkCreatorFollower", "ResetJdEip", "ReviseIpHostLocation", "ShardTableOps", "ShardTeamTableSql", "SshChangePort", "SshCommands", "SshCommandsBatchLaunchInstance", "SyncKolCreator", "SyncKolRegionMap", "TkSendEmail", "TransferShardTable", "TransferTable", "TransferTagResource", "TransferTkCreator", "UpgradeGhMessage", "UploadAiKnowledge", "UploadDiskFile", "UserRefreshIp"]}, "teamId": {"type": "integer", "format": "int64"}}}, "TeamDto": {"title": "TeamDto", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "domesticCloudEnabled": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invalidTime": {"type": "string", "format": "date-time"}, "inviteCode": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "overseaCloudEnabled": {"type": "boolean"}, "paid": {"type": "boolean"}, "partnerId": {"type": "integer", "format": "int64"}, "payTime": {"type": "string", "format": "date-time"}, "repurchaseTime": {"type": "string", "format": "date-time"}, "repurchased": {"type": "boolean"}, "status": {"type": "string", "enum": ["Blocked", "Deleted", "Pending", "Ready"]}, "teamType": {"type": "string", "enum": ["crs", "gh", "krShop", "normal", "partner", "plugin", "tk", "tkshop"]}, "tenantId": {"type": "integer", "format": "int64"}, "testing": {"type": "boolean"}, "validateTime": {"type": "string", "format": "date-time"}, "validated": {"type": "boolean"}, "verified": {"type": "boolean"}}}, "TeamPccConfig": {"title": "TeamPccConfig", "type": "object", "properties": {"pccEnabled": {"type": "boolean"}, "pccKey": {"type": "string"}}}, "TriggerUploadDeliveryRequest": {"title": "TriggerUploadDeliveryRequest", "type": "object", "properties": {"orderIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "shopId": {"type": "integer", "format": "int64"}}}, "UpdateDeliveryRequest": {"title": "UpdateDeliveryRequest", "type": "object", "properties": {"failOrderIds": {"type": "array", "description": "失败的订单ID", "items": {"type": "string"}}, "successOrderIds": {"type": "array", "description": "成功的订单ID", "items": {"type": "string"}}}}, "UpdateProductImageRequest": {"title": "UpdateProductImageRequest", "type": "object", "properties": {"productPacks": {"type": "array", "items": {"$ref": "#/components/schemas/ProductImagePack"}}, "shopId": {"type": "integer", "format": "int64"}}}, "UpdateProductImageRequestV2": {"title": "UpdateProductImageRequestV2", "type": "object", "properties": {"productId": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "urls": {"type": "array", "items": {"type": "string"}}, "vendorItemId": {"type": "string"}}}, "UpdateProductOptionRequest": {"title": "UpdateProductOptionRequest", "type": "object", "properties": {"clean": {"type": "boolean"}, "options": {"type": "array", "items": {"$ref": "#/components/schemas/ProductVendorVo"}}, "productId": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CrsDeliveryDto»": {"title": "WebResult«CrsDeliveryDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CrsDeliveryDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CrsDeliveryOrderDto»": {"title": "WebResult«CrsDeliveryOrderDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CrsDeliveryOrderDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CrsStockDto»": {"title": "WebResult«CrsStockDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CrsStockDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CrsWarehouseDto»": {"title": "WebResult«CrsWarehouseDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CrsWarehouseDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«CrsDeliveryDto»»": {"title": "WebResult«List«CrsDeliveryDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CrsDeliveryDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«CrsDeliveryOrderDto»»": {"title": "WebResult«List«CrsDeliveryOrderDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CrsDeliveryOrderDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«CrsDeliveryProviderVo»»": {"title": "WebResult«List«CrsDeliveryProviderVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CrsDeliveryProviderVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«CrsHscodeDto»»": {"title": "WebResult«List«CrsHscodeDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CrsHscodeDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«CrsWarehouseDto»»": {"title": "WebResult«List«CrsWarehouseDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CrsWarehouseDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«DeliveryParamVo»»": {"title": "WebResult«List«DeliveryParamVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryParamVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«string»»": {"title": "WebResult«List«string»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "string"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«CrsDeliveryOrderDto»»": {"title": "WebResult«PageResult«CrsDeliveryOrderDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«CrsDeliveryOrderDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«CrsOrderDetailVo»»": {"title": "WebResult«PageResult«CrsOrderDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«CrsOrderDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«CrsOrderLogDto»»": {"title": "WebResult«PageResult«CrsOrderLogDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«CrsOrderLogDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«CrsProductVo»»": {"title": "WebResult«PageResult«CrsProductVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«CrsProductVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«CrsReturnOrderDto»»": {"title": "WebResult«PageResult«CrsReturnOrderDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«CrsReturnOrderDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«CrsStockDto»»": {"title": "WebResult«PageResult«CrsStockDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«CrsStockDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaTaskVo»": {"title": "WebResult«RpaTaskVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaTaskVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TaskDto»": {"title": "WebResult«TaskDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TaskDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamDto»": {"title": "WebResult«TeamDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamPccConfig»": {"title": "WebResult«TeamPccConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamPccConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«int»": {"title": "WebResult«int»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«long»": {"title": "WebResult«long»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}