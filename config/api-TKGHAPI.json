{"openapi": "3.0.3", "info": {"title": "TK GH SubSystem API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "TK公会交互API", "description": "Gh Interact Controller"}, {"name": "TK公会普通用户API", "description": "Gh User Controller"}, {"name": "TK公会直播API", "description": "Gh Live Controller"}, {"name": "TK公会直播达人API", "description": "Gh Live Creator Controller"}, {"name": "TK公会私信API", "description": "Gh Message Controller"}, {"name": "TK公会视频达人API", "description": "Gh Video Creator Controller"}, {"name": "TK公会话术API", "description": "Gh Speech Controller"}, {"name": "TK公会达人API", "description": "Gh Creator Controller"}, {"name": "TK公会达人流程调试相关API", "description": "Gh Job Controller"}, {"name": "TK公会达人自动调度相关API", "description": "Gh Job Plan Controller"}, {"name": "TK公会通用设置相关API", "description": "Gh Settings Controller"}, {"name": "TK公共库达人API", "description": "Kol Creator Controller"}, {"name": "TK子团队达人API", "description": "Kol Sub Team Controller"}, {"name": "刷榜大哥API", "description": "<PERSON>h Gifter Controller"}], "paths": {"/api/gh/bindMobileAccountToCreator": {"put": {"tags": ["GhCreatorController"], "summary": "为该达人分配一个发私信的手机账号", "operationId": "ghBindMobileAccountToCreatorPut", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "mobileAccountId", "in": "query", "description": "mobileAccountId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "ghBizScene", "in": "query", "description": "ghBizScene", "required": false, "style": "form", "schema": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/bindPmShopToCreator": {"put": {"tags": ["GhCreatorController"], "summary": "为该达人分配一个发私信的TikTok分身，返回绑定的shopId", "operationId": "ghBindPmShopToCreatorPut", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopId", "in": "query", "description": "shopId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "ghBizScene", "in": "query", "description": "ghBizScene", "required": false, "style": "form", "schema": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}}, {"name": "ghScheduleType", "in": "query", "description": "ghScheduleType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/byHandle": {"get": {"tags": ["GhCreatorController"], "summary": "获取kol四类用户对象之一", "operationId": "ghByHandleGet", "parameters": [{"name": "handle", "in": "query", "description": "handle", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KolUserInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/acquire": {"put": {"tags": ["GhCreatorController"], "summary": "认领达人", "operationId": "ghCreatorAcquirePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/allocate": {"put": {"tags": ["GhCreatorController"], "summary": "分配达人", "operationId": "ghCreatorAllocatePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorAllocateRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/allocateByQuery": {"put": {"tags": ["GhCreatorController"], "summary": "分配达人【按查询】", "operationId": "ghCreatorAllocateByQueryPut", "parameters": [{"name": "responsibleUserId", "in": "query", "description": "responsibleUserId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindGhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/availableList": {"get": {"tags": ["GhCreatorController"], "summary": "批量查询达人是否可见（用handle）", "operationId": "ghCreatorAvailableListGet", "parameters": [{"name": "handles", "in": "query", "description": "handles", "required": false, "style": "form", "explode": true, "schema": {"type": "string"}}, {"name": "creatorIds", "in": "query", "description": "creatorIds", "required": false, "style": "form", "explode": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhAvailableVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/avatar": {"get": {"tags": ["GhCreatorController"], "summary": "获取TK达人头像", "operationId": "ghCreatorAvatarGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "region", "in": "query", "description": "region", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/byKeepInfo": {"delete": {"tags": ["GhCreatorController"], "summary": "根据缓存配置删除达人", "operationId": "ghCreatorByKeepInfoDelete", "parameters": [{"name": "keepDays", "in": "query", "description": "keepDays", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "filterRevenue", "in": "query", "description": "filterRevenue", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/cancelAllocate": {"put": {"tags": ["GhCreatorController"], "summary": "取消分配", "operationId": "ghCreatorCancelAllocatePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/countV2": {"get": {"tags": ["GhCreatorController"], "summary": "统计团队达人的数量", "operationId": "ghCreatorCountV2Get", "parameters": [{"name": "alias", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "anchor", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "avgLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "avgLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "avgViewersFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "avgViewersTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "deleting", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "elite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "general", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasNewMsg", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasNoRegion", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "importTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "importTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "importTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "interactTypes", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}}, {"name": "lastLiveTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastLiveTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastLiveTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "maxGameRankFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxGameRankTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxHourRevenueFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxHourRevenueTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxPopularRankFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxPopularRankTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxRevenueFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxRevenueTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxViewersFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "maxViewersTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "maxWeekRevenueFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxWeekRevenueTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "messageStatus", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "name", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "platformType", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "revenueLc", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "showcase", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/delete": {"post": {"tags": ["GhCreatorController"], "summary": "删除达人", "operationId": "ghCreatorDeletePost", "parameters": [{"name": "force", "in": "query", "description": "force", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "deleteRemark", "in": "query", "description": "deleteRemark", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/deleteByQuery": {"post": {"tags": ["GhCreatorController"], "summary": "删除达人（按查询）", "operationId": "ghCreatorDeleteByQueryPost", "parameters": [{"name": "force", "in": "query", "description": "force", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "deleteRemark", "in": "query", "description": "deleteRemark", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindGhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/elite": {"put": {"tags": ["GhCreatorController"], "summary": "批量更新达人是否金牌主播", "operationId": "ghCreatorElitePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateEliteRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/filterStat": {"get": {"tags": ["GhCreatorController"], "summary": "获取筛选配置和统计信息", "operationId": "ghCreatorFilterStatGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«FilterStatInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/findExists": {"post": {"tags": ["GhCreatorController"], "summary": "批量查询达人是否已经落库", "operationId": "ghCreatorFindExistsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FilterExistsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«KolAvailableVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/page": {"get": {"tags": ["GhCreatorController"], "summary": "分页查询团队达人", "operationId": "ghCreatorPageGet", "parameters": [{"name": "alias", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "anchor", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "avgLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "avgLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "avgViewersFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "avgViewersTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "deleting", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "elite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "general", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasNewMsg", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasNoRegion", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "importTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "importTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "importTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "interactTypes", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}}, {"name": "lastLiveTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastLiveTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastLiveTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "maxGameRankFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxGameRankTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxHourRevenueFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxHourRevenueTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxPopularRankFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxPopularRankTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxRevenueFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxRevenueTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxViewersFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "maxViewersTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "maxWeekRevenueFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxWeekRevenueTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "messageStatus", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "name", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "platformType", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "revenueLc", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "showcase", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhCreatorDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/pageByAvailable": {"get": {"tags": ["GhCreatorController"], "summary": "根据筛选状态分页查询达人", "operationId": "ghCreatorPageByAvailableGet", "parameters": [{"name": "availableStatus", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Available", "Unavailable", "Unset"]}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "filterRevenue", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "platformType", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/pageResponsible": {"get": {"tags": ["GhCreatorController"], "summary": "分页查询我认领的达人", "operationId": "ghCreatorPageResponsibleGet", "parameters": [{"name": "alias", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "anchor", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "avgLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "avgLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "avgViewersFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "avgViewersTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "deleting", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "elite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "general", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasNewMsg", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasNoRegion", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "importTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "importTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "importTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "interactTypes", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}}, {"name": "lastLiveTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastLiveTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastLiveTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "maxGameRankFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxGameRankTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxHourRevenueFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxHourRevenueTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxPopularRankFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxPopularRankTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxRevenueFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxRevenueTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxViewersFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "maxViewersTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "maxWeekRevenueFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxWeekRevenueTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "messageStatus", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "name", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "platformType", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "revenueLc", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "showcase", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhCreatorDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/pageV2": {"get": {"tags": ["GhCreatorController"], "summary": "分页查询团队达人V2（不返回总数）", "operationId": "ghCreatorPageV2Get", "parameters": [{"name": "alias", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "anchor", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "avgLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "avgLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "avgViewersFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "avgViewersTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "deleting", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "elite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "general", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasNewMsg", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasNoRegion", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "importTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "importTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "importTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "interactTypes", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}}, {"name": "lastLiveTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastLiveTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastLiveTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "maxGameRankFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxGameRankTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxHourRevenueFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxHourRevenueTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxPopularRankFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxPopularRankTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxRevenueFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxRevenueTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxViewersFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "maxViewersTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "maxWeekRevenueFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "maxWeekRevenueTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "messageStatus", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "name", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "platformType", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "revenueLc", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "showcase", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhCreatorDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/recover": {"post": {"tags": ["GhCreatorController"], "summary": "恢复达人", "operationId": "ghCreatorRecoverPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/regionByCreatorIds": {"post": {"tags": ["GhCreatorController"], "summary": "根据TK creatorId查询所在区域", "operationId": "ghCreatorRegionByCreatorIdsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhGetRegionByCreatorIdRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«KolRegionMapDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/regions": {"get": {"tags": ["GhCreatorController"], "summary": "获取TK达人区域", "operationId": "ghCreatorRegionsGet", "parameters": [{"name": "platformType", "in": "query", "description": "platformType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/release": {"put": {"tags": ["GhCreatorController"], "summary": "取消认领达人", "operationId": "ghCreatorReleasePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/responsibleUserList": {"get": {"tags": ["GhCreatorController"], "summary": "getResponsibleUserList", "operationId": "ghCreatorResponsibleUserListGet", "parameters": [{"name": "platformType", "in": "query", "description": "platformType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/statusList": {"get": {"tags": ["GhCreatorController"], "summary": "批量查询达人状态（用handle）", "operationId": "ghCreatorStatusListGet", "parameters": [{"name": "handles", "in": "query", "description": "handles", "required": true, "style": "form", "explode": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhCreatorStatusVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/sync": {"post": {"tags": ["GhCreatorController"], "summary": "同步（创建）TK达人", "operationId": "ghCreatorSyncPost", "parameters": [{"name": "rank", "in": "query", "description": "rank", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "availableStatus", "in": "query", "description": "availableStatus", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Available", "Unavailable", "Unset"]}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorDocument"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/syncAvatar": {"post": {"tags": ["GhCreatorController"], "summary": "同步TK达人头像", "operationId": "ghCreatorSyncAvatarPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncCreatorAvatarRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/syncBatch": {"post": {"tags": ["GhCreatorController"], "summary": "批量同步（创建）TK达人", "operationId": "ghCreatorSyncBatchPost", "parameters": [{"name": "rank", "in": "query", "description": "rank", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "availableStatus", "in": "query", "description": "availableStatus", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Available", "Unavailable", "Unset"]}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorDocumentBatch"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/syncHasNewMsg": {"put": {"tags": ["GhCreatorController"], "summary": "批量更新达人是否有新消息（用handle更新）", "operationId": "ghCreatorSyncHasNewMsgPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorSyncHasNewMsgRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/syncList": {"get": {"tags": ["GhCreatorController"], "summary": "获取待同步的达人handle列表", "operationId": "ghCreatorSyncListGet", "parameters": [{"name": "limit", "in": "query", "description": "limit", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«HandleCreatorIdVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/syncNotFoundBatch": {"post": {"tags": ["GhCreatorController"], "summary": "批量更新达人不存在", "operationId": "ghCreatorSyncNotFoundBatchPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhSyncNotFoundRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/syncRevenue": {"post": {"tags": ["GhCreatorController"], "summary": "同步达人的最高xx流水", "operationId": "ghCreatorSyncRevenuePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhRevenueDocument"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/syncStatus": {"put": {"tags": ["GhCreatorController"], "summary": "批量更新达人状态（用handle）", "operationId": "ghCreatorSyncStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorSyncStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhCreatorDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/tag": {"post": {"tags": ["GhCreatorController"], "summary": "给指定达人打标签", "operationId": "ghCreatorTagPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/untag": {"post": {"tags": ["GhCreatorController"], "summary": "取消指定达人的特定标签", "operationId": "ghCreatorUntagPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/updateAnchor": {"put": {"tags": ["GhCreatorController"], "summary": "批量更新达人是否为主播", "operationId": "ghCreatorUpdateAnchorPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateAnchorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/updateAvailable": {"put": {"tags": ["GhCreatorController"], "summary": "批量更新达人可用性", "operationId": "ghCreatorUpdateAvailablePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateAvailableRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhCreatorDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/updateBackstageShopId": {"put": {"tags": ["GhCreatorController"], "summary": "批量更新达人的BackstageShopId", "operationId": "ghCreatorUpdateBackstageShopIdPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateBackstageShopIdRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/updateGeneral": {"put": {"tags": ["GhCreatorController"], "summary": "批量更新达人是否为主播", "operationId": "ghCreatorUpdateGeneralPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateGeneralRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/updateHasNewMsg": {"put": {"tags": ["GhCreatorController"], "summary": "批量更新达人是否有新消息", "operationId": "ghCreatorUpdateHasNewMsgPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateHasNewMsgRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhCreatorDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/updateMessageStatus": {"put": {"tags": ["GhCreatorController"], "summary": "批量更新达人是否关注后私信", "operationId": "ghCreatorUpdateMessageStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateMessageStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/updateShowcase": {"put": {"tags": ["GhCreatorController"], "summary": "批量更新达人是否开通橱窗", "operationId": "ghCreatorUpdateShowcasePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateShowcaseRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/updateStatus": {"put": {"tags": ["GhCreatorController"], "summary": "批量更新达人状态", "operationId": "ghCreatorUpdateStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhCreatorDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/creator/{creatorId}": {"get": {"tags": ["GhCreatorController"], "summary": "查询达人详情", "operationId": "ghCreatorByCreatorIdGet", "parameters": [{"name": "creatorId", "in": "path", "description": "花漾达人ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhCreatorDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/downloadByQuery/{token}": {"get": {"tags": ["GhCreatorController"], "summary": "下载达人信息【按查询】", "operationId": "ghDownloadByQueryByTokenGet", "parameters": [{"name": "token", "in": "path", "description": "token", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Resource"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/exportToken": {"post": {"tags": ["GhCreatorController"], "summary": "下载达人的token", "operationId": "ghExportTokenPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportTkCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/exportTokenByQuery": {"post": {"tags": ["GhCreatorController"], "summary": "导出达人（按查询）", "operationId": "ghExportTokenByQueryPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportTkCreatorByQueryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/fields": {"get": {"tags": ["GhCreatorController"], "summary": "达人可导出的字段元信息", "operationId": "ghFieldsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkCreatorFiledVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/mobile/notifyNewTkMessage": {"put": {"tags": ["GhCreatorController"], "summary": "用来通知有哪些手机账号有新消息了", "operationId": "ghMobileNotifyNewTkMessagePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyNewTkMessageRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/shop/tag": {"post": {"tags": ["GhCreatorController"], "summary": "给分身批量打标签", "operationId": "ghShopTagPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhTagRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/shop/untag": {"post": {"tags": ["GhCreatorController"], "summary": "给分身批量删除标签", "operationId": "ghShopUntagPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhTagRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/acquire": {"put": {"tags": ["GhGifterController"], "summary": "认领达人", "operationId": "ghGifterAcquirePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/allocate": {"put": {"tags": ["GhGifterController"], "summary": "分配达人", "operationId": "ghGifterAllocatePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhGifterAllocateRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/avatar": {"get": {"tags": ["GhGifterController"], "summary": "获取TK达人头像", "operationId": "ghGifterAvatarGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/cancelAllocate": {"put": {"tags": ["GhGifterController"], "summary": "取消分配", "operationId": "ghGifterCancelAllocatePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/count": {"get": {"tags": ["GhGifterController"], "summary": "统计团队达人的数量", "operationId": "ghGifterCountGet", "parameters": [{"name": "alias", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "deleting", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "giftMaxFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "giftMaxTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "giftTotalFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "giftTotalTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "gifterLevelFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "gifterLevelTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasNewMsg", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasNoRegion", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "myStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "videoCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/delete": {"post": {"tags": ["GhGifterController"], "summary": "删除达人", "operationId": "ghGifterDeletePost", "parameters": [{"name": "force", "in": "query", "description": "force", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "deleteRemark", "in": "query", "description": "deleteRemark", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/deleteByQuery": {"post": {"tags": ["GhGifterController"], "summary": "删除达人（按查询）", "operationId": "ghGifterDeleteByQueryPost", "parameters": [{"name": "force", "in": "query", "description": "force", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "deleteRemark", "in": "query", "description": "deleteRemark", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindGhGifterRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/fields": {"get": {"tags": ["GhGifterController"], "summary": "达人可导出的字段元信息", "operationId": "ghGifterFieldsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkCreatorFiledVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/live-gift/sync": {"post": {"tags": ["GhGifterController"], "summary": "同步直播打赏记录", "operationId": "ghGifterLiveGiftSyncPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncGhLiveGiftRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/page": {"get": {"tags": ["GhGifterController"], "summary": "分页查询团队达人V2（不返回总数）", "operationId": "ghGifterPageGet", "parameters": [{"name": "alias", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "deleting", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "giftMaxFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "giftMaxTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "giftTotalFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "giftTotalTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "gifterLevelFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "gifterLevelTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasNewMsg", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasNoRegion", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "myStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "videoCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhGifterDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/pageResponsible": {"get": {"tags": ["GhGifterController"], "summary": "分页查询我认领的达人", "operationId": "ghGifterPageResponsibleGet", "parameters": [{"name": "alias", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "deleting", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "giftMaxFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "giftMaxTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "giftTotalFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "giftTotalTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "gifterLevelFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "gifterLevelTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasNewMsg", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasNoRegion", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "myStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "videoCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhGifterDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/recover": {"post": {"tags": ["GhGifterController"], "summary": "恢复达人", "operationId": "ghGifterRecoverPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/regions": {"get": {"tags": ["GhGifterController"], "summary": "获取TK达人区域", "operationId": "ghGifterRegionsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/release": {"put": {"tags": ["GhGifterController"], "summary": "取消认领达人", "operationId": "ghGifterReleasePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/responsibleUserList": {"get": {"tags": ["GhGifterController"], "summary": "getResponsibleUserList", "operationId": "ghGifterResponsibleUserListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/syncAvatar": {"post": {"tags": ["GhGifterController"], "summary": "同步TK达人头像", "operationId": "ghGifterSyncAvatarPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncCreatorAvatarRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/syncBatch": {"post": {"tags": ["GhGifterController"], "summary": "批量同步（创建）TK达人", "operationId": "ghGifterSyncBatchPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhUserDocumentBatch"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhGifterDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/updateMessageStatus": {"put": {"tags": ["GhGifterController"], "summary": "批量更新达人是否关注后私信", "operationId": "ghGifterUpdateMessageStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateMessageStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/updateStatus": {"put": {"tags": ["GhGifterController"], "summary": "批量更新达人状态", "operationId": "ghGifterUpdateStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhCreatorDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/{creatorId}": {"get": {"tags": ["GhGifterController"], "summary": "查询达人详情", "operationId": "ghGifterByCreatorIdGet", "parameters": [{"name": "creatorId", "in": "path", "description": "creatorId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhGifterDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/gifter/{gifterId}/live-gift/list": {"get": {"tags": ["GhGifterController"], "summary": "查询直播打赏记录", "operationId": "ghGifterByGifterIdLiveGiftListGet", "parameters": [{"name": "gifterId", "in": "path", "description": "gifterId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhLiveGiftDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/hasInteraction": {"get": {"tags": ["GhInteractController"], "summary": "是否与达人产生过交互", "operationId": "ghHasInteractionGet", "parameters": [{"name": "shopId", "in": "query", "description": "店铺ID，为null时，从团队全局统计", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "handle", "in": "query", "description": "达人账号", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "interactType", "in": "query", "description": "交互类型", "required": false, "style": "form", "schema": {"type": "string", "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}}, {"name": "interactTimeFrom", "in": "query", "description": "interactTimeFrom", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "interactTimeTo", "in": "query", "description": "interactTimeTo", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/hasInteractions": {"post": {"tags": ["GhInteractController"], "summary": "是否与达人产生过交互（批量）", "operationId": "ghHasInteractionsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HasGhInteractionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«HasInteractionVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/interaction": {"post": {"tags": ["GhInteractController"], "summary": "添加交互记录", "operationId": "ghInteractionPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhInteractionVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhInteractionDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/interaction/page": {"get": {"tags": ["GhInteractController"], "summary": "查询交互记录", "operationId": "ghInteractionPageGet", "parameters": [{"name": "shopId", "in": "query", "description": "shopId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "creatorId", "in": "query", "description": "花漾达人库ID，不是TK库的ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "interactType", "in": "query", "description": "交互类型", "required": false, "style": "form", "schema": {"type": "string", "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}}, {"name": "operatorId", "in": "query", "description": "操作者ID", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "interactTimeFrom", "in": "query", "description": "交互时间开始", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "interactTimeTo", "in": "query", "description": "交互时间截止", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhInteractionDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/interaction/{id}": {"get": {"tags": ["GhInteractController"], "summary": "查询交互记录详情", "operationId": "ghInteractionByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhInteractionDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/interactions": {"post": {"tags": ["GhInteractController"], "summary": "添加交互记录（批量）", "operationId": "ghInteractionsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddGhInteractionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/latestInteraction": {"get": {"tags": ["GhInteractController"], "summary": "查询与达人最近产生过交互", "operationId": "ghLatestInteractionGet", "parameters": [{"name": "shopId", "in": "query", "description": "店铺ID，为null时，从团队全局统计", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "handle", "in": "query", "description": "达人账号", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "interactType", "in": "query", "description": "交互类型", "required": true, "style": "form", "schema": {"type": "string", "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TkInteractionDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/accountFollow": {"put": {"tags": ["GhJobController"], "summary": "创建主播关注任务", "operationId": "ghJobsAccountFollowPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountFollowRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/accountMaintenance": {"put": {"tags": ["GhJobController"], "summary": "手机养号", "operationId": "ghJobsAccountMaintenancePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountMaintenanceRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/batchCancelJobs": {"delete": {"tags": ["GhJobController"], "summary": "批量取消公会任务", "operationId": "ghJobsBatchCancelJobsDelete", "parameters": [{"name": "jobIds", "in": "query", "description": "jobIds", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/cancelAllJobs": {"delete": {"tags": ["GhJobController"], "summary": "取消当前团队所有公会任务", "operationId": "ghJobsCancelAllJobsDelete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelJobsFilter"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/cancelJob": {"delete": {"tags": ["GhJobController"], "summary": "取消一个公会任务", "operationId": "ghJobsCancelJobDelete", "parameters": [{"name": "jobId", "in": "query", "description": "jobId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/jobDetail": {"get": {"tags": ["GhJobController"], "summary": "获取任务详情", "operationId": "ghJobsJobDetailGet", "parameters": [{"name": "jobId", "in": "query", "description": "jobId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "alive", "in": "query", "description": "是不是查任务池，否则查历史任务表", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhJobDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/listJobs/history": {"get": {"tags": ["GhJobController"], "summary": "按条件查询历史公会任务", "operationId": "ghJobsListJobsHistoryGet", "parameters": [{"name": "jobType", "in": "query", "description": "为空表示所有类型", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Acco<PERSON><PERSON><PERSON><PERSON>", "AccountHealthCheck", "AccountMaintenance", "AccountMessageCheck", "KOL_LiveRewards", "<PERSON><PERSON><PERSON><PERSON>", "SendInvite", "SendPm", "SendUserCard", "ShareVideo", "<PERSON><PERSON><PERSON>", "SyncContactToPhone", "SyncContacts", "SyncCreator", "SyncLiveStream", "SyncPm", "TS_AddContacts", "TS_AddFriends", "TS_DemandPayment", "TS_IMChatByFilter", "TS_IMChatByHandle", "TS_LoginCheck", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendEmail", "TS_SendFacebook", "TS_SendLine", "TS_SendWhatsApp", "TS_SendZalo", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncProducts", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_TargetPlanClear", "TS_TargetPlanModify", "TS_VideoADCode"]}}, {"name": "ghScheduleType", "in": "query", "description": "为空表示所有类型，仅当jobType=SendInvite时有意义", "required": false, "style": "form", "schema": {"type": "string", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}}, {"name": "ghPlatformType", "in": "query", "description": "为空表示所有类型", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, {"name": "status", "in": "query", "description": "为空表示所有状态", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Cancelled", "Failed", "Pending", "Running", "Scheduled", "Succeed"]}}, {"name": "creator", "in": "query", "description": "按达人精准ID过滤，注意不是like！！！", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "mobileId", "in": "query", "description": "手机id，为空表示不限制手机（实际可能以该手机下对应的手机账号去查询）", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopId", "in": "query", "description": "分身id或者手机账号id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "userEvent", "in": "query", "description": "是否用户触发的查询，区别setInterval查询", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createFrom", "in": "query", "description": "createFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTo", "in": "query", "description": "createTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "executeFrom", "in": "query", "description": "executeFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "executeTo", "in": "query", "description": "executeTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "orderBy", "in": "query", "description": "格式为[field_name asc|desc], 可选列：create_time,execute_end_time", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhJobDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/listJobs/pending": {"get": {"tags": ["GhJobController"], "summary": "按条件查询待执行公会任务", "operationId": "ghJobsListJobsPendingGet", "parameters": [{"name": "jobType", "in": "query", "description": "为空表示所有类型", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Acco<PERSON><PERSON><PERSON><PERSON>", "AccountHealthCheck", "AccountMaintenance", "AccountMessageCheck", "KOL_LiveRewards", "<PERSON><PERSON><PERSON><PERSON>", "SendInvite", "SendPm", "SendUserCard", "ShareVideo", "<PERSON><PERSON><PERSON>", "SyncContactToPhone", "SyncContacts", "SyncCreator", "SyncLiveStream", "SyncPm", "TS_AddContacts", "TS_AddFriends", "TS_DemandPayment", "TS_IMChatByFilter", "TS_IMChatByHandle", "TS_LoginCheck", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendEmail", "TS_SendFacebook", "TS_SendLine", "TS_SendWhatsApp", "TS_SendZalo", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncProducts", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_TargetPlanClear", "TS_TargetPlanModify", "TS_VideoADCode"]}}, {"name": "ghScheduleType", "in": "query", "description": "为空表示所有类型，仅当jobType=SendInvite时有意义", "required": false, "style": "form", "schema": {"type": "string", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}}, {"name": "ghPlatformType", "in": "query", "description": "为空表示所有类型", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, {"name": "status", "in": "query", "description": "为空表示所有状态", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Cancelled", "Failed", "Pending", "Running", "Scheduled", "Succeed"]}}, {"name": "creator", "in": "query", "description": "按达人精准ID过滤，注意不是like！！！", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "mobileId", "in": "query", "description": "手机id，为空表示不限制手机（实际可能以该手机下对应的手机账号去查询）", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopId", "in": "query", "description": "分身id或者手机账号id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "userEvent", "in": "query", "description": "是否用户触发的查询，区别setInterval查询", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createFrom", "in": "query", "description": "createFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTo", "in": "query", "description": "createTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "orderBy", "in": "query", "description": "格式为[field_name asc|desc], 可选列：create_time,execute_end_time", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhJobDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/listShops": {"get": {"tags": ["GhJobController"], "summary": "查询任务池的分身（或mobile account）", "operationId": "ghJobsListShopsGet", "parameters": [{"name": "history", "in": "query", "description": "history", "required": true, "style": "form", "schema": {"type": "boolean"}}, {"name": "ghPlatformType", "in": "query", "description": "ghPlatformType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, {"name": "rpaType", "in": "query", "description": "rpaType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«IdNameVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/queryCreatingInfo": {"get": {"tags": ["GhJobController"], "summary": "queryCreatingInfo", "operationId": "ghJobsQueryCreatingInfoGet", "parameters": [{"name": "jobId", "in": "query", "description": "jobId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«JobCreatingInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/sendAccountHealthCheck": {"put": {"tags": ["GhJobController"], "summary": "创建手机账号可用性检查任务", "operationId": "ghJobsSendAccountHealthCheckPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountHealthCheckRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/sendInvitePM": {"put": {"tags": ["GhJobController"], "summary": "发送邀约建联信息", "operationId": "ghJobsSendInvitePMPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendInvitePMRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/sendKakaoMessage": {"post": {"tags": ["GhJobController"], "summary": "同步kakao聊天记录", "operationId": "ghJobsSendKakaoMessagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendKakaoMessageRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/sendReplyPM": {"put": {"tags": ["GhJobController"], "summary": "发送回复私信消息", "operationId": "ghJobsSendReplyPMPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendReplyPMRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/sendUserCard": {"put": {"tags": ["GhJobController"], "summary": "发送用户卡片", "operationId": "ghJobsSendUserCardPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendUserCardRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/shareVideo": {"put": {"tags": ["GhJobController"], "summary": "创建分享视频的任务", "operationId": "ghJobsShareVideoPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareVideoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/syncAllPmRequest": {"put": {"tags": ["GhJobController"], "summary": "对团队所有的未签约的达人进行私信同步", "operationId": "ghJobsSyncAllPmRequestPut", "parameters": [{"name": "deleteSession", "in": "query", "description": "同步时是否同步删除与主播的聊天记录，默认为true", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "ghPlatformType", "in": "query", "description": "ghPlatformType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, {"name": "ghBizScene", "in": "query", "description": "ghBizScene", "required": false, "style": "form", "schema": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/syncKakaoChats": {"put": {"tags": ["GhJobController"], "summary": "同步kakao聊天记录", "operationId": "ghJobsSyncKakaoChatsPut", "parameters": [{"name": "mobileId", "in": "query", "description": "mobileId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/syncKakaoContacts": {"put": {"tags": ["GhJobController"], "summary": "同步kakao联系人", "operationId": "ghJobsSyncKakaoContactsPut", "parameters": [{"name": "mobileId", "in": "query", "description": "mobileId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/syncKaokaoContactsToPhone": {"put": {"tags": ["GhJobController"], "summary": "同步kakao联系人到手机", "operationId": "ghJobsSyncKaokaoContactsToPhonePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncKaokaoContactsToPhoneRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/syncPMRequest": {"put": {"tags": ["GhJobController"], "summary": "选中达人私信同步", "operationId": "ghJobsSyncPMRequestPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncPMRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/task/fetchJob": {"get": {"tags": ["GhJobController"], "summary": "流程执行时获取任务", "operationId": "ghJobsTaskFetchJobGet", "parameters": [{"name": "rpaTaskId", "in": "query", "description": "rpaTaskId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhJobDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/task/reportJob": {"put": {"tags": ["GhJobController"], "summary": "汇报ghJob执行结果", "operationId": "ghJobsTaskReportJobPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportGhJobRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/jobs/updateCreatorInfo": {"put": {"tags": ["GhJobController"], "summary": "更新公会达人信息", "operationId": "ghJobsUpdateCreatorInfoPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGhCreatorInfoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/plans/addPlan": {"post": {"tags": ["GhJobPlanController"], "summary": "添加一个调度计划", "operationId": "ghPlansAddPlanPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGhJobPlanRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhJobPlanVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/plans/checkNameExists": {"get": {"tags": ["GhJobPlanController"], "summary": "检查计划名称是否存在", "operationId": "ghPlansCheckNameExistsGet", "parameters": [{"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "ghPlatformType", "in": "query", "description": "ghPlatformType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/plans/deletePlan": {"delete": {"tags": ["GhJobPlanController"], "summary": "添加交互记录", "operationId": "ghPlansDeletePlanDelete", "parameters": [{"name": "ghJobPlanId", "in": "query", "description": "ghJobPlanId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/plans/plans": {"get": {"tags": ["GhJobPlanController"], "summary": "查询计划列表", "operationId": "ghPlansPlansGet", "parameters": [{"name": "ghPlatformType", "in": "query", "description": "ghPlatformType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, {"name": "ghPlanType", "in": "query", "description": "ghPlanType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Team", "User"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhJobPlanVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/plans/runJob": {"post": {"tags": ["GhJobPlanController"], "summary": "执行一个任务", "operationId": "ghPlansRunJobPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGhJobPlanRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/plans/togglePlanEnabled": {"put": {"tags": ["GhJobPlanController"], "summary": "切换计划的开启状态", "operationId": "ghPlansTogglePlanEnabledPut", "parameters": [{"name": "ghJobPlanId", "in": "query", "description": "ghJobPlanId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "enabled", "in": "query", "description": "enabled", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/plans/updatePlan": {"post": {"tags": ["GhJobPlanController"], "summary": "updatePlan", "operationId": "ghPlansUpdatePlanPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGhJobPlanRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhJobPlanVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/live/page": {"get": {"tags": ["GhLiveController"], "summary": "直播历史记录", "operationId": "ghLivePageGet", "parameters": [{"name": "handle", "in": "query", "description": "handle", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "region", "in": "query", "description": "region", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "description": "sortField", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«KolLiveDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/live/sync": {"post": {"tags": ["GhLiveController"], "summary": "同步（创建）直播记录", "operationId": "ghLiveSyncPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhLiveDocument"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KolLiveDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/liveCreator/isWatchingLiveRewards": {"get": {"tags": ["GhLiveCreatorController"], "summary": "当前是不正在直播间打赏守候。", "operationId": "ghLiveCreatorIsWatchingLiveRewardsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/liveCreator/markAllOffline": {"post": {"tags": ["GhLiveCreatorController"], "summary": "标记所有达人下播", "operationId": "ghLiveCreatorMarkAllOfflinePost", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/liveCreator/markOffline": {"post": {"tags": ["GhLiveCreatorController"], "summary": "标记某个达人下播", "operationId": "ghLiveCreatorMarkOfflinePost", "parameters": [{"name": "handle", "in": "query", "description": "handle", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/liveCreator/page": {"get": {"tags": ["GhLiveCreatorController"], "summary": "分页查询在线主播", "operationId": "ghLiveCreatorPageGet", "parameters": [{"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "elite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "gameRankFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "gameRankTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "likesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "likesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "liveTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "liveTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "popularRankFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "popularRankTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "revenueFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "revenueTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "viewersFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "viewersTo", "in": "query", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhLiveCreatorDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/liveCreator/startWatchLiveRewards": {"post": {"tags": ["GhLiveCreatorController"], "summary": "开始直播间打赏守候", "operationId": "ghLiveCreatorStartWatchLiveRewardsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartWatchLiveRewardsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/liveCreator/stopWatchLiveRewards": {"delete": {"tags": ["GhLiveCreatorController"], "summary": "停止直播间打赏守候", "operationId": "ghLiveCreatorStopWatchLiveRewardsDelete", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/liveCreator/syncBatch": {"post": {"tags": ["GhLiveCreatorController"], "summary": "同步直播主播", "operationId": "ghLiveCreatorSyncBatchPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhLiveCreatorDocumentBatch"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/message/page": {"get": {"tags": ["GhMessageController"], "summary": "消息历史记录", "operationId": "ghMessagePageGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "rpaType", "in": "query", "description": "rpaType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}}, {"name": "scene", "in": "query", "description": "scene", "required": false, "style": "form", "schema": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhMessageDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/message/session/page": {"post": {"tags": ["GhMessageController"], "summary": "查询待回复消息列表", "operationId": "ghMessageSessionPagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageGhSessionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhSessionVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/message/session/status": {"put": {"tags": ["GhMessageController"], "summary": "批量修改达人会话状态", "operationId": "ghMessageSessionStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGhSessionStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/message/session/{id}/status": {"put": {"tags": ["GhMessageController"], "summary": "修改达人会话状态", "operationId": "ghMessageSessionByIdStatusPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "description": "status", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Error", "NeedReply", "Replied", "Replying"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/message/sync": {"post": {"tags": ["GhMessageController"], "summary": "同步达人私信", "description": "返回达人新发送消息数目", "operationId": "ghMessageSyncPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddGhMessageRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«SyncMessageResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings": {"delete": {"tags": ["GhSettingsController"], "summary": "删除一个用于执行rpa的设备", "operationId": "ghSettingsDelete", "parameters": [{"name": "ghJobDeviceId", "in": "query", "description": "ghJobDeviceId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/bindRpaDevice": {"put": {"tags": ["GhSettingsController"], "summary": "添加一个用于执行rpa的设备", "operationId": "ghSettingsBindRpaDevicePut", "parameters": [{"name": "concurrent", "in": "query", "description": "concurrent", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhJobDeviceVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/countries": {"get": {"tags": ["GhSettingsController"], "summary": "获取团队的工会注册地", "operationId": "ghSettingsCountriesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["GhSettingsController"], "summary": "设置团队的工会注册地", "operationId": "ghSettingsCountriesPut", "parameters": [{"name": "area", "in": "query", "description": "area", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "countries", "in": "query", "description": "countries", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/creatorExpiredAutoTags": {"get": {"tags": ["GhSettingsController"], "summary": "达人自动化标签配置", "operationId": "ghSettingsCreatorExpiredAutoTagsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhCreatorExpiredAutoTags»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["GhSettingsController"], "summary": "配置达人自动化标签", "operationId": "ghSettingsCreatorExpiredAutoTagsPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorExpiredAutoTags"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/creatorTypes": {"put": {"tags": ["GhSettingsController"], "summary": "修改主播类型", "operationId": "ghSettingsCreatorTypesPut", "parameters": [{"name": "types", "in": "query", "description": "types", "required": true, "style": "form", "explode": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/deleteRemarks": {"put": {"tags": ["GhSettingsController"], "summary": "修改屏蔽原因", "operationId": "ghSettingsDeleteRemarksPut", "parameters": [{"name": "deleteRemarks", "in": "query", "description": "deleteRemarks", "required": true, "style": "form", "explode": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/getShopStatistics": {"get": {"tags": ["GhSettingsController"], "summary": "获取公会平台分身统计信息", "operationId": "ghSettingsGetShopStatisticsGet", "parameters": [{"name": "ghPlatformType", "in": "query", "description": "ghPlatformType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhShopStatistics»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/grabApiDomains": {"get": {"tags": ["GhSettingsController"], "summary": "抓tk数据域名配置", "operationId": "ghSettingsGrabApiDomainsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/keepDays": {"put": {"tags": ["GhSettingsController"], "summary": "缓存时长（天）", "operationId": "ghSettingsKeepDaysPut", "parameters": [{"name": "keepDays", "in": "query", "description": "keepDays", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/keepTagDays": {"put": {"tags": ["GhSettingsController"], "summary": "修改日期类标签保留天数", "description": "0表示一直保留", "operationId": "ghSettingsKeepTagDaysPut", "parameters": [{"name": "keepTagDays", "in": "query", "description": "keepTagDays", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/kolConfig": {"get": {"tags": ["GhSettingsController"], "summary": "KOL配置", "operationId": "ghSettingsKolConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamKolConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/liveHours": {"put": {"tags": ["GhSettingsController"], "summary": "设置最近开播时间", "operationId": "ghSettingsLiveHoursPut", "parameters": [{"name": "liveHours", "in": "query", "description": "liveHours", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/loadBlockTags": {"get": {"tags": ["GhSettingsController"], "summary": "获取blockTags", "operationId": "ghSettingsLoadBlockTagsGet", "parameters": [{"name": "ghPlatformType", "in": "query", "description": "ghPlatformType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, {"name": "mobile", "in": "query", "description": "mobile", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«string,string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/loadSettings": {"get": {"tags": ["GhSettingsController"], "summary": "获取公会执行任务的通用配置", "operationId": "ghSettingsLoadSettingsGet", "parameters": [{"name": "ghPlatformType", "in": "query", "description": "ghPlatformType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhGeneralSettingsVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/minRevenue": {"put": {"tags": ["GhSettingsController"], "summary": "设置最低流水", "operationId": "ghSettingsMinRevenuePut", "parameters": [{"name": "minRevenue", "in": "query", "description": "minRevenue", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/mobileDetail": {"get": {"tags": ["GhSettingsController"], "summary": "获取一个手机详情", "operationId": "ghSettingsMobileDetailGet", "parameters": [{"name": "mobileId", "in": "query", "description": "mobileId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamMobileVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/notifySchedule": {"get": {"tags": ["GhSettingsController"], "summary": "获取通知配置", "operationId": "ghSettingsNotifyScheduleGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«NotifyScheduleVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["GhSettingsController"], "summary": "更新通知配置", "operationId": "ghSettingsNotifySchedulePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyScheduleVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/updateFailPolicy": {"put": {"tags": ["GhSettingsController"], "summary": "updateFailPolicy", "operationId": "ghSettingsUpdateFailPolicyPut", "parameters": [{"name": "ghPlatformType", "in": "query", "description": "ghPlatformType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, {"name": "jobType", "in": "query", "description": "jobType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Acco<PERSON><PERSON><PERSON><PERSON>", "AccountHealthCheck", "AccountMaintenance", "AccountMessageCheck", "KOL_LiveRewards", "<PERSON><PERSON><PERSON><PERSON>", "SendInvite", "SendPm", "SendUserCard", "ShareVideo", "<PERSON><PERSON><PERSON>", "SyncContactToPhone", "SyncContacts", "SyncCreator", "SyncLiveStream", "SyncPm", "TS_AddContacts", "TS_AddFriends", "TS_DemandPayment", "TS_IMChatByFilter", "TS_IMChatByHandle", "TS_LoginCheck", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendEmail", "TS_SendFacebook", "TS_SendLine", "TS_SendWhatsApp", "TS_SendZalo", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncProducts", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_TargetPlanClear", "TS_TargetPlanModify", "TS_VideoADCode"]}}, {"name": "failPolicy", "in": "query", "description": "failPolicy", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Ignore", "Retry"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/updateRpaDeviceConcurrent": {"put": {"tags": ["GhSettingsController"], "summary": "更改一个设备的迸发数", "operationId": "ghSettingsUpdateRpaDeviceConcurrentPut", "parameters": [{"name": "ghJobDeviceId", "in": "query", "description": "ghJobDeviceId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "concurrent", "in": "query", "description": "concurrent", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhJobDeviceVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/updateScheduleConfig": {"put": {"tags": ["GhSettingsController"], "summary": "更新TikTok私信账号调度配置", "operationId": "ghSettingsUpdateScheduleConfigPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateScheduleConfigRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/updateSchedulePriority": {"put": {"tags": ["GhSettingsController"], "summary": "updateSchedulePriority", "operationId": "ghSettingsUpdateSchedulePriorityPut", "parameters": [{"name": "ghPlatformType", "in": "query", "description": "ghPlatformType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, {"name": "priority", "in": "query", "description": "priority", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/settings/updateSyncCreatorConfig": {"put": {"tags": ["GhSettingsController"], "summary": "更新主播信息更新调度配置", "operationId": "ghSettingsUpdateSyncCreatorConfigPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSyncCreatorConfigRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/speech/accountConfig": {"get": {"tags": ["GhSpeechController"], "summary": "修改手机账号（或分身的）默认话术分组", "description": "不传入groupId时，清除配置", "operationId": "ghSpeechAccountConfigGet", "parameters": [{"name": "accountId", "in": "query", "description": "accountId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "rpaType", "in": "query", "description": "rpaType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhSpeechGroupDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["GhSpeechController"], "summary": "修改手机账号（或分身的）默认话术分组", "description": "不传入groupId时，清除配置", "operationId": "ghSpeechAccountConfigPut", "parameters": [{"name": "accountId", "in": "query", "description": "accountId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "rpaType", "in": "query", "description": "rpaType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}}, {"name": "groupId", "in": "query", "description": "groupId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/speech/check": {"post": {"tags": ["GhSpeechController"], "summary": "校验话术分组", "operationId": "ghSpeechCheckPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckSpeechAndGroupRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhSpeechGroupVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/speech/create": {"post": {"tags": ["GhSpeechController"], "summary": "批量创建话术", "operationId": "ghSpeechCreatePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSpeechRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/speech/delete": {"post": {"tags": ["GhSpeechController"], "summary": "删除话术", "operationId": "ghSpeechDeletePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/speech/group/create": {"post": {"tags": ["GhSpeechController"], "summary": "创建话术分组", "operationId": "ghSpeechGroupCreatePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSpeechGroupRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhSpeechGroupDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/speech/group/list": {"get": {"tags": ["GhSpeechController"], "summary": "查询话术分组", "operationId": "ghSpeechGroupListGet", "parameters": [{"name": "speechType", "in": "query", "description": "speechType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["BuyerImChat", "BuyerSendMsg", "Comment", "Im<PERSON><PERSON>", "Invite", "LiveComment", "RequestAdCode", "SendMsg", "TargetPlan"]}}, {"name": "scene", "in": "query", "description": "scene", "required": false, "style": "form", "schema": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhSpeechGroupVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/speech/group/{groupId}": {"delete": {"tags": ["GhSpeechController"], "summary": "删除分组", "operationId": "ghSpeechGroupByGroupIdDelete", "parameters": [{"name": "groupId", "in": "path", "description": "groupId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "confirm", "in": "query", "description": "confirm", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/speech/group/{id}/name": {"put": {"tags": ["GhSpeechController"], "summary": "修改分组名称", "operationId": "ghSpeechGroupByIdNamePut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/speech/page": {"get": {"tags": ["GhSpeechController"], "summary": "分页查询话术", "operationId": "ghSpeechPageGet", "parameters": [{"name": "content", "in": "query", "description": "content", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "speechType", "in": "query", "description": "speechType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["BuyerImChat", "BuyerSendMsg", "Comment", "Im<PERSON><PERSON>", "Invite", "LiveComment", "RequestAdCode", "SendMsg", "TargetPlan"]}}, {"name": "scene", "in": "query", "description": "scene", "required": false, "style": "form", "schema": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}}, {"name": "groupId", "in": "query", "description": "groupId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "description": "sortField", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhSpeechDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/speech/switchSortNo": {"put": {"tags": ["GhSpeechController"], "summary": "切换话术顺序", "operationId": "ghSpeechSwitchSortNoPut", "parameters": [{"name": "groupId", "in": "query", "description": "groupId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "speechId1", "in": "query", "description": "speechId1", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "speechId2", "in": "query", "description": "speechId2", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/speech/updateSortNo": {"put": {"tags": ["GhSpeechController"], "summary": "更新话术顺序", "operationId": "ghSpeechUpdateSortNoPut", "parameters": [{"name": "groupId", "in": "query", "description": "groupId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/speech/{id}": {"get": {"tags": ["GhSpeechController"], "summary": "获取话术详情", "operationId": "ghSpeechByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhSpeechDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["GhSpeechController"], "summary": "修改话术", "operationId": "ghSpeechByIdPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "name", "in": "query", "description": "name", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "text", "in": "query", "description": "text", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/allocateByQuery": {"post": {"tags": ["GhUserController"], "summary": "批量分配【按查询】", "operationId": "ghAllocateByQueryPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AllocateTkCreatorByQueryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/favoriteByQuery": {"post": {"tags": ["GhUserController"], "summary": "批量收藏或取消收藏", "operationId": "ghFavoriteByQueryPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FavoriteTkCreatorByQueryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/import": {"post": {"tags": ["GhUserController"], "summary": "导入工会达人", "operationId": "ghImportPost", "parameters": [{"name": "scene", "in": "query", "description": "scene", "required": false, "style": "form", "schema": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorImportRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/importExcel/{scene}": {"post": {"tags": ["GhUserController"], "summary": "Excel导入达人", "operationId": "ghImportExcelByScenePost", "parameters": [{"name": "scene", "in": "path", "description": "scene", "required": true, "style": "simple", "schema": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "description": "file", "format": "binary"}, "tags": {"type": "string", "description": "tags"}}}, "encoding": {"tags": {"contentType": "text/plain"}}}}}, "responses": {"200": {"description": "OK", "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/listTagByQuery": {"post": {"tags": ["GhUserController"], "summary": "根据条件查询出资源的所有标签", "operationId": "ghListTagByQueryPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkCreatorByQueryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TagDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/releaseByQuery": {"post": {"tags": ["GhUserController"], "summary": "批量取消分配", "operationId": "ghReleaseByQueryPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TkCreatorByQueryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/statPerformance": {"get": {"tags": ["GhUserController"], "summary": "绩效统计(3类用户相加）", "operationId": "ghStatPerformanceGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhCreatorPerformanceStatVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/statRows": {"get": {"tags": ["GhUserController"], "summary": "数据统计", "operationId": "ghStatRowsGet", "parameters": [{"name": "days", "in": "query", "description": "days", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhStatRowVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/tag": {"post": {"tags": ["GhUserController"], "summary": "给指定达人打标签", "operationId": "ghTagPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhTagByHandleRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/tagByQuery": {"post": {"tags": ["GhUserController"], "summary": "批量打标签或取消标签", "operationId": "ghTagByQueryPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagTkCreatorByQueryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/untag": {"post": {"tags": ["GhUserController"], "summary": "取消指定达人的特定标签", "operationId": "ghUntagPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhTagByHandleRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/acquire": {"put": {"tags": ["GhUserController"], "summary": "认领达人", "operationId": "ghUserAcquirePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/allocate": {"put": {"tags": ["GhUserController"], "summary": "分配达人", "operationId": "ghUserAllocatePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorAllocateRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/avatar": {"get": {"tags": ["GhUserController"], "summary": "获取TK达人头像", "operationId": "ghUserAvatarGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/cancelAllocate": {"put": {"tags": ["GhUserController"], "summary": "取消分配", "operationId": "ghUserCancelAllocatePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/count": {"get": {"tags": ["GhUserController"], "summary": "统计团队达人的数量", "operationId": "ghUserCountGet", "parameters": [{"name": "alias", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "deleting", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "gifterLevelFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "gifterLevelTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasNewMsg", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasNoRegion", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "videoCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/delete": {"post": {"tags": ["GhUserController"], "summary": "删除达人", "operationId": "ghUserDeletePost", "parameters": [{"name": "force", "in": "query", "description": "force", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "deleteRemark", "in": "query", "description": "deleteRemark", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/deleteByQuery": {"post": {"tags": ["GhUserController"], "summary": "删除达人（按查询）", "operationId": "ghUserDeleteByQueryPost", "parameters": [{"name": "force", "in": "query", "description": "force", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "deleteRemark", "in": "query", "description": "deleteRemark", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindGhUserRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/fields": {"get": {"tags": ["GhUserController"], "summary": "达人可导出的字段元信息", "operationId": "ghUserFieldsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkCreatorFiledVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/page": {"get": {"tags": ["GhUserController"], "summary": "分页查询团队达人V2（不返回总数）", "operationId": "ghUserPageGet", "parameters": [{"name": "alias", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "deleting", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "gifterLevelFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "gifterLevelTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasNewMsg", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasNoRegion", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "videoCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhUserDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/pageResponsible": {"get": {"tags": ["GhUserController"], "summary": "分页查询我认领的达人", "operationId": "ghUserPageResponsibleGet", "parameters": [{"name": "alias", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "deleting", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "gifterLevelFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "gifterLevelTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasNewMsg", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasNoRegion", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "videoCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhUserDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/recover": {"post": {"tags": ["GhUserController"], "summary": "恢复达人", "operationId": "ghUserRecoverPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/regions": {"get": {"tags": ["GhUserController"], "summary": "获取TK达人区域", "operationId": "ghUserRegionsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/release": {"put": {"tags": ["GhUserController"], "summary": "取消认领达人", "operationId": "ghUserReleasePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/responsibleUserList": {"get": {"tags": ["GhUserController"], "summary": "getResponsibleUserList", "operationId": "ghUserResponsibleUserListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/syncAvatar": {"post": {"tags": ["GhUserController"], "summary": "同步TK达人头像", "operationId": "ghUserSyncAvatarPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncCreatorAvatarRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/syncBatch": {"post": {"tags": ["GhUserController"], "summary": "批量同步（创建）TK达人", "operationId": "ghUserSyncBatchPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhUserDocumentBatch"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhUserDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/updateMessageStatus": {"put": {"tags": ["GhUserController"], "summary": "批量更新达人是否关注后私信", "operationId": "ghUserUpdateMessageStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateMessageStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/updateStatus": {"put": {"tags": ["GhUserController"], "summary": "批量更新达人状态", "operationId": "ghUserUpdateStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhCreatorDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/user/{creatorId}": {"get": {"tags": ["GhUserController"], "summary": "查询达人详情", "operationId": "ghUserByCreatorIdGet", "parameters": [{"name": "creatorId", "in": "path", "description": "creatorId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhUserDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/v2/download/{token}": {"get": {"tags": ["GhUserController"], "summary": "下载达人信息", "operationId": "ghV2DownloadByTokenGet", "parameters": [{"name": "token", "in": "path", "description": "token", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Resource"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/v2/downloadByQuery/{token}": {"get": {"tags": ["GhUserController"], "summary": "下载达人信息【按查询】", "operationId": "ghV2DownloadByQueryByTokenGet", "parameters": [{"name": "token", "in": "path", "description": "token", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Resource"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/v2/exportHandle/{token}": {"get": {"tags": ["GhUserController"], "summary": "下载达人handle信息", "operationId": "ghV2ExportHandleByTokenGet", "parameters": [{"name": "token", "in": "path", "description": "token", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "prefix", "in": "query", "description": "prefix", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "separator", "in": "query", "description": "separator", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/v2/exportHandleByQuery/{token}": {"get": {"tags": ["GhUserController"], "summary": "下载达人handle信息【按查询】", "operationId": "ghV2ExportHandleByQueryByTokenGet", "parameters": [{"name": "token", "in": "path", "description": "token", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "prefix", "in": "query", "description": "prefix", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "separator", "in": "query", "description": "separator", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/v2/exportToken": {"post": {"tags": ["GhUserController"], "summary": "下载达人的token", "operationId": "ghV2ExportTokenPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportTkCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/v2/exportTokenByQuery": {"post": {"tags": ["GhUserController"], "summary": "导出达人（按查询）", "operationId": "ghV2ExportTokenByQueryPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportTkCreatorByQueryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/acquire": {"put": {"tags": ["GhVideoCreatorController"], "summary": "认领达人", "operationId": "ghVideoCreatorAcquirePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/allocate": {"put": {"tags": ["GhVideoCreatorController"], "summary": "分配达人", "operationId": "ghVideoCreatorAllocatePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorAllocateRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/avatar": {"get": {"tags": ["GhVideoCreatorController"], "summary": "获取TK达人头像", "operationId": "ghVideoCreatorAvatarGet", "parameters": [{"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/cancelAllocate": {"put": {"tags": ["GhVideoCreatorController"], "summary": "取消分配", "operationId": "ghVideoCreatorCancelAllocatePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/count": {"get": {"tags": ["GhVideoCreatorController"], "summary": "统计团队达人的数量", "operationId": "ghVideoCreatorCountGet", "parameters": [{"name": "alias", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "deleting", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasNewMsg", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasNoRegion", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "videoCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/delete": {"post": {"tags": ["GhVideoCreatorController"], "summary": "删除达人", "operationId": "ghVideoCreatorDeletePost", "parameters": [{"name": "force", "in": "query", "description": "force", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "deleteRemark", "in": "query", "description": "deleteRemark", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/deleteByQuery": {"post": {"tags": ["GhVideoCreatorController"], "summary": "删除达人（按查询）", "operationId": "ghVideoCreatorDeleteByQueryPost", "parameters": [{"name": "force", "in": "query", "description": "force", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "deleteRemark", "in": "query", "description": "deleteRemark", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FindGhVideoCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/page": {"get": {"tags": ["GhVideoCreatorController"], "summary": "分页查询团队达人V2（不返回总数）", "operationId": "ghVideoCreatorPageGet", "parameters": [{"name": "alias", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "deleting", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasNewMsg", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasNoRegion", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "videoCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhVideoCreatorDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/pageResponsible": {"get": {"tags": ["GhVideoCreatorController"], "summary": "分页查询我认领的达人", "operationId": "ghVideoCreatorPageResponsibleGet", "parameters": [{"name": "alias", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "deleting", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "handle", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hasNewMsg", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasNoRegion", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "hasResponsibleUser", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeLastDays", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "messageStatusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "region", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "regions", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "responsibleUserId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "statusList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}, "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}, {"name": "videoCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "videoLikesTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«GhVideoCreatorDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/recover": {"post": {"tags": ["GhVideoCreatorController"], "summary": "恢复达人", "operationId": "ghVideoCreatorRecoverPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/regions": {"get": {"tags": ["GhVideoCreatorController"], "summary": "获取TK达人区域", "operationId": "ghVideoCreatorRegionsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/release": {"put": {"tags": ["GhVideoCreatorController"], "summary": "取消认领达人", "operationId": "ghVideoCreatorReleasePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/responsibleUserList": {"get": {"tags": ["GhVideoCreatorController"], "summary": "getResponsibleUserList", "operationId": "ghVideoCreatorResponsibleUserListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/syncAvatar": {"post": {"tags": ["GhVideoCreatorController"], "summary": "同步TK达人头像", "operationId": "ghVideoCreatorSyncAvatarPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncCreatorAvatarRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/syncBatch": {"post": {"tags": ["GhVideoCreatorController"], "summary": "批量同步（创建）TK达人", "operationId": "ghVideoCreatorSyncBatchPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhVideoCreatorDocumentBatch"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«SyncBatchResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/updateMessageStatus": {"put": {"tags": ["GhVideoCreatorController"], "summary": "批量更新达人是否关注后私信", "operationId": "ghVideoCreatorUpdateMessageStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateMessageStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/updateStatus": {"put": {"tags": ["GhVideoCreatorController"], "summary": "批量更新达人状态", "operationId": "ghVideoCreatorUpdateStatusPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorUpdateStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhCreatorDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/creator/{creatorId}": {"get": {"tags": ["GhVideoCreatorController"], "summary": "查询达人详情", "operationId": "ghVideoCreatorByCreatorIdGet", "parameters": [{"name": "creatorId", "in": "path", "description": "creatorId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GhVideoCreatorDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/fields": {"get": {"tags": ["GhVideoCreatorController"], "summary": "达人可导出的字段元信息", "operationId": "ghVideoFieldsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkCreatorFiledVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/video/statPerformance": {"get": {"tags": ["GhVideoCreatorController"], "summary": "绩效统计", "operationId": "ghVideoStatPerformanceGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GhCreatorPerformanceStatVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/pub/creator/deleteBatch": {"post": {"tags": ["KolCreatorController"], "summary": "批量删除国家库达人", "operationId": "ghPubCreatorDeleteBatchPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HandleRegionBatchRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/pub/creator/earliestUpdatedCreators": {"get": {"tags": ["KolCreatorController"], "summary": "获取最早待更新的达人列表", "operationId": "ghPubCreatorEarliestUpdatedCreatorsGet", "parameters": [{"name": "limit", "in": "query", "description": "limit", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "region", "in": "query", "description": "region", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "countryCode", "in": "query", "description": "countryCode", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«CreatorBriefVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/pub/creator/regions": {"post": {"tags": ["KolCreatorController"], "summary": "批量查询达人的区域（如果没找到，不会在返回的map中）", "description": "creatorId=>region map", "operationId": "ghPubCreatorRegionsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonCreatorIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«string,string»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/pub/creator/scrapTkUserProfile": {"get": {"tags": ["KolCreatorController"], "summary": "使用开放服务抓取tk达人的信息：faas-sgp1-18bc02ac.doserverless.co", "operationId": "ghPubCreatorScrapTkUserProfileGet", "parameters": [{"name": "handle", "in": "query", "description": "handle", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/pub/creator/syncBatch": {"post": {"tags": ["KolCreatorController"], "summary": "批量同步（创建）TK国家库达人", "operationId": "ghPubCreatorSyncBatchPost", "parameters": [{"name": "rank", "in": "query", "description": "rank", "required": false, "style": "form", "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GhCreatorDocumentBatch"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/pub/creator/tag": {"post": {"tags": ["KolCreatorController"], "summary": "给公共库达人批量打标签", "operationId": "ghPubCreatorTagPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KolTagRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/pub/creator/untagBySuffix": {"post": {"tags": ["KolCreatorController"], "summary": "清空带特定后缀的标签（更新所有团队）", "operationId": "ghPubCreatorUntagBySuffixPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KolUntagBySuffixRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/subTeam": {"post": {"tags": ["KolSubTeamController"], "summary": "激活子团队", "operationId": "ghSubTeamPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KolSubTeamVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KolSubTeamDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/subTeam/page": {"get": {"tags": ["KolSubTeamController"], "summary": "查询子团队", "operationId": "ghSubTeamPageGet", "parameters": [{"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "query", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["createTime", "subTeamId", "valid", "validTimeFrom", "validTimeTo"]}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "valid", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«KolSubTeamDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/subTeam/{subTeamId}": {"put": {"tags": ["KolSubTeamController"], "summary": "修改子团队", "operationId": "ghSubTeamBySubTeamIdPut", "parameters": [{"name": "subTeamId", "in": "path", "description": "subTeamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KolUpdateSubTeamRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KolSubTeamDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/subTeam/{subTeamId}/valid": {"put": {"tags": ["KolSubTeamController"], "summary": "修改子团队有效性", "operationId": "ghSubTeamBySubTeamIdValidPut", "parameters": [{"name": "subTeamId", "in": "path", "description": "subTeamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "valid", "in": "query", "description": "valid", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KolSubTeamDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AccountFollowRequest": {"title": "AccountFollowRequest", "type": "object", "properties": {"advanceSettings": {"type": "object", "description": "邀约高级设置"}, "allowedStartTime": {"type": "string", "description": "允许开始执行的时间", "format": "date-time"}, "bizScene": {"type": "string", "description": "是什么样类型的达人，为空表示 LiveCreator ", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "creatorIds": {"type": "array", "description": "拟发送的达人列表", "items": {"type": "integer", "format": "int64"}}, "ghPlatformType": {"type": "string", "description": "公会平台类型", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "ghScheduleType": {"type": "string", "description": "拟使用的TK账号类型，默认值 Normal", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "mobileChoicePolicy": {"description": "发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义", "$ref": "#/components/schemas/MobileChoicePolicy"}, "wordsIds": {"type": "array", "description": "邀约要使用的话术列表，会随机从中选择发送", "items": {"type": "integer", "format": "int64"}}}}, "AccountHealthCheckRequest": {"title": "AccountHealthCheckRequest", "type": "object", "properties": {"advanceSettings": {"type": "object", "description": "其它的流程关心但引擎不关心的属性"}, "friendType": {"type": "string", "description": "first | random | assign  为空表示random"}, "ghPlatformType": {"type": "string", "description": "公会平台类型", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "mobileAccountIds": {"type": "array", "description": "手机账号id列表", "items": {"type": "integer", "format": "int64"}}}}, "AccountMaintenanceRequest": {"title": "AccountMaintenanceRequest", "type": "object", "properties": {"advanceSettings": {"type": "object", "description": "流程相关的参数全部放到高级设置里"}, "deadTime": {"type": "string", "format": "date-time"}, "ghPlatformType": {"type": "string", "description": "公会平台类型", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "mobileAccountIds": {"type": "array", "description": "有哪些手机账号要养号", "items": {"type": "integer", "format": "int64"}}, "triggerTeamScheduleImmediately": {"type": "boolean"}, "wordsIds": {"type": "array", "description": "养号评论时要使用的话术列表，会随机从中选择发送", "items": {"type": "integer", "format": "int64"}}}}, "AddGhInteractionRequest": {"title": "AddGhInteractionRequest", "type": "object", "properties": {"interactions": {"type": "array", "items": {"$ref": "#/components/schemas/GhInteractionVo"}}}}, "AddGhMessageRequest": {"title": "AddGhMessageRequest", "type": "object", "properties": {"accountId": {"type": "integer", "description": "浏览器的分身ID和手机的mobileAccount.id", "format": "int64"}, "handle": {"type": "string"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/GhMessageVo"}}, "scene": {"type": "string", "description": "达人的类型：主播、视频达人、普通用户", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "scheduleType": {"type": "string", "description": "私信流程的scheduleType，也是账号类型", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "status": {"type": "string", "description": "私信是否成功", "enum": ["Fail", "Pending", "Success"]}}}, "AllocateTkCreatorByQueryRequest": {"title": "AllocateTkCreatorByQueryRequest", "type": "object", "properties": {"cleanFirst": {"type": "boolean"}, "query": {"type": "object"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "responsibleUserIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "scene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "tag": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "AllowTimeConfig": {"title": "AllowTimeConfig", "type": "object", "properties": {"FRI": {"type": "string"}, "MON": {"type": "string", "description": "以逗号分隔开的小时，允许在哪几个小时执行就列出哪些数字如：0,1,2,5,23"}, "SAT": {"type": "string"}, "SUN": {"type": "string"}, "THU": {"type": "string"}, "TUE": {"type": "string"}, "WED": {"type": "string"}}}, "CancelJobsFilter": {"title": "<PERSON>celJ<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object", "properties": {"createFrom": {"type": "string", "format": "date-time"}, "createTo": {"type": "string", "format": "date-time"}, "creator": {"type": "string"}, "ghCreatorId": {"type": "integer", "format": "int64"}, "ghScheduleType": {"type": "string", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "jobType": {"type": "string", "enum": ["Acco<PERSON><PERSON><PERSON><PERSON>", "AccountHealthCheck", "AccountMaintenance", "AccountMessageCheck", "KOL_LiveRewards", "<PERSON><PERSON><PERSON><PERSON>", "SendInvite", "SendPm", "SendUserCard", "ShareVideo", "<PERSON><PERSON><PERSON>", "SyncContactToPhone", "SyncContacts", "SyncCreator", "SyncLiveStream", "SyncPm", "TS_AddContacts", "TS_AddFriends", "TS_DemandPayment", "TS_IMChatByFilter", "TS_IMChatByHandle", "TS_LoginCheck", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendEmail", "TS_SendFacebook", "TS_SendLine", "TS_SendWhatsApp", "TS_SendZalo", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncProducts", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_TargetPlanClear", "TS_TargetPlanModify", "TS_VideoADCode"]}, "mobileId": {"type": "integer", "format": "int64"}, "platformType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "shopId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Cancelled", "Failed", "Pending", "Running", "Scheduled", "Succeed"]}, "userEvent": {"type": "boolean"}}}, "CheckSpeechAndGroupRequest": {"title": "CheckSpeechAndGroupRequest", "type": "object", "properties": {"groupId": {"type": "integer", "format": "int64"}, "speechIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "CloudMobileInfo": {"title": "CloudMobileInfo", "type": "object", "properties": {"autoRenew": {"type": "boolean", "description": "是否自动续费", "example": false}, "cloudMobileInsId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "networkEnabled": {"type": "boolean"}, "padCode": {"type": "string"}, "periodUnit": {"type": "string", "description": "计价周期单位", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "proxy": {"type": "string", "description": "手机代理"}, "renewPrice": {"type": "number", "description": "续费价格", "format": "bigdecimal"}, "validEndDate": {"type": "string", "description": "过期时间", "format": "date-time"}}}, "CommonCreatorIdsRequest": {"title": "CommonCreatorIdsRequest", "type": "object", "properties": {"creatorIds": {"type": "array", "items": {"type": "string"}}}}, "CommonIdsRequest": {"title": "CommonIdsRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "CreateGhJobPlanRequest": {"title": "CreateGhJobPlanRequest", "type": "object", "properties": {"bizScene": {"type": "string", "description": "场景", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "commentWordIds": {"type": "array", "description": "邀约时评论要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义", "items": {"type": "integer", "format": "int64"}}, "creatorFilter": {"type": "string", "description": "查询条件，由 /api/gh/creator/page 这个接口的params JSON.stringify(params) 而来"}, "cronExpression": {"type": "string", "description": "重复日期，只包含星期信息的表达式(具体参考自动流程计划)"}, "deleteMsg": {"type": "boolean"}, "deleteSession": {"type": "boolean", "description": "同步时是否删除与主播的聊天记录，默认为true。只在 ghJobType 为 SyncPm 时有意义", "example": false}, "expressions": {"type": "array", "description": "如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...]", "items": {"type": "string"}}, "ghJobType": {"type": "string", "enum": ["Acco<PERSON><PERSON><PERSON><PERSON>", "AccountHealthCheck", "AccountMaintenance", "AccountMessageCheck", "KOL_LiveRewards", "<PERSON><PERSON><PERSON><PERSON>", "SendInvite", "SendPm", "SendUserCard", "ShareVideo", "<PERSON><PERSON><PERSON>", "SyncContactToPhone", "SyncContacts", "SyncCreator", "SyncLiveStream", "SyncPm", "TS_AddContacts", "TS_AddFriends", "TS_DemandPayment", "TS_IMChatByFilter", "TS_IMChatByHandle", "TS_LoginCheck", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendEmail", "TS_SendFacebook", "TS_SendLine", "TS_SendWhatsApp", "TS_SendZalo", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncProducts", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_TargetPlanClear", "TS_TargetPlanModify", "TS_VideoADCode"]}, "ghPlatformType": {"type": "string", "description": "公会平台类型，tk、抖音、小红书", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "ghScheduleType": {"type": "string", "description": "拟使用的TK账号类型，默认值 Normal。只在 ghJobType 为 SendInvite 时有意义", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "inviteAdvanceSettings": {"type": "object", "description": "私信高级设置，只在 ghJobType 为 SendInvite/AccountMaintenance 时有意义"}, "inviteMobileChoicePolicy": {"description": "发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义", "$ref": "#/components/schemas/MobileChoicePolicy"}, "inviteShopChoicePolicy": {"description": "发送邀约分身选择策略，仅在 ghScheduleType != Mobile 的时候有意义", "$ref": "#/components/schemas/ShopChoicePolicy"}, "maxMinutes": {"type": "integer", "description": "最多允许执行多少分钟(需求方要求是流程自己实现，所以应该只有部分流程支持这个参数)", "format": "int32"}, "mobileAccountIds": {"type": "array", "description": "相应的手机账号id，只有在 ghJobType 为 AccountMaintenance 时有意义", "items": {"type": "integer", "format": "int64"}}, "name": {"type": "string"}, "planType": {"type": "string", "enum": ["Team", "User"]}, "sendComment": {"type": "boolean", "description": "发送私信前关注主播并将私信内容评论到主播的第一条视频", "example": false}, "sendEmoji": {"type": "boolean", "description": "是否增加随机表情", "example": false}, "sendInviteCard": {"type": "boolean", "description": "是否发送邀约卡片，只有发送邀约建联计划才有意义(只有公会后台账号才支持此特性）", "example": false}, "skipResponsible": {"type": "boolean", "description": "已分配/已认领的达人不再发送 (为空表示 true )，只对发送邀约建联的计划有意义", "example": false}, "wordsIds": {"type": "array", "description": "邀约要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义", "items": {"type": "integer", "format": "int64"}}}}, "CreateSpeechGroupRequest": {"title": "CreateSpeechGroupRequest", "type": "object", "properties": {"bizScene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "name": {"type": "string"}, "speechType": {"type": "string", "enum": ["BuyerImChat", "BuyerSendMsg", "Comment", "Im<PERSON><PERSON>", "Invite", "LiveComment", "RequestAdCode", "SendMsg", "TargetPlan"]}}}, "CreateSpeechRequest": {"title": "CreateSpeechRequest", "type": "object", "properties": {"groupId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "textList": {"type": "array", "items": {"type": "string"}}}}, "CreatorBriefVo": {"title": "CreatorBriefVo", "type": "object", "properties": {"creatorId": {"type": "string"}, "handle": {"type": "string"}, "region": {"type": "string"}}}, "DepartmentDto": {"title": "DepartmentDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "hidden": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invitingAuditEnabled": {"type": "boolean"}, "invitingEnabled": {"type": "boolean"}, "name": {"type": "string"}, "parentId": {"type": "integer", "format": "int64"}, "sortNumber": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "ExportTkCreatorByQueryRequest": {"title": "ExportTkCreatorByQueryRequest", "type": "object", "properties": {"props": {"type": "array", "items": {"type": "string"}}, "query": {"type": "object"}, "scene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "ExportTkCreatorRequest": {"title": "ExportTkCreatorRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "props": {"type": "array", "items": {"type": "string"}}, "scene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "FavoriteTkCreatorByQueryRequest": {"title": "FavoriteTkCreatorByQueryRequest", "type": "object", "properties": {"favorite": {"type": "boolean", "description": "关注或取消关注", "example": false}, "query": {"type": "object"}, "scene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "FilterExistsRequest": {"title": "FilterExistsRequest", "type": "object", "properties": {"creators": {"type": "array", "items": {"$ref": "#/components/schemas/KolHandleRegionVo"}}}}, "FilterStatInfo": {"title": "FilterStatInfo", "type": "object", "properties": {"continueFilter": {"type": "boolean", "description": "当天是否应继续筛选", "example": false}, "filterTotal": {"type": "integer", "description": "限制团队每日筛选达人数量（0表示不限制）", "format": "int32"}, "filteredCount": {"type": "integer", "description": "当天已经筛选的达人数量", "format": "int32"}}}, "FindGhCreatorRequest": {"title": "FindGhCreatorRequest", "type": "object", "properties": {"alias": {"type": "string"}, "anchor": {"type": "boolean"}, "avgLikesFrom": {"type": "number", "format": "double"}, "avgLikesTo": {"type": "number", "format": "double"}, "avgViewersFrom": {"type": "number", "format": "double"}, "avgViewersTo": {"type": "number", "format": "double"}, "containsTag": {"type": "boolean", "description": "包含指定标签。取false时，tagLc只能是OR", "example": false}, "createTimeLastDays": {"type": "integer", "format": "int32"}, "deleting": {"type": "boolean"}, "elite": {"type": "boolean"}, "filterByFavorite": {"type": "boolean"}, "followerCntFrom": {"type": "integer", "format": "int32"}, "followerCntTo": {"type": "integer", "format": "int32"}, "general": {"type": "boolean"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "hasNoRegion": {"type": "boolean"}, "hasResponsibleUser": {"type": "boolean"}, "importTimeFrom": {"type": "string", "format": "date-time"}, "importTimeLastDays": {"type": "integer", "format": "int32"}, "importTimeTo": {"type": "string", "format": "date-time"}, "interactTypes": {"type": "array", "items": {"type": "string", "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}}, "lastLiveTimeFrom": {"type": "string", "format": "date-time"}, "lastLiveTimeLastDays": {"type": "integer", "format": "int32"}, "lastLiveTimeTo": {"type": "string", "format": "date-time"}, "lastSyncTimeFrom": {"type": "string", "format": "date-time"}, "lastSyncTimeLastDays": {"type": "integer", "format": "int32"}, "lastSyncTimeTo": {"type": "string", "format": "date-time"}, "limit": {"type": "integer", "format": "int32"}, "maxGameRankFrom": {"type": "number", "format": "double"}, "maxGameRankTo": {"type": "number", "format": "double"}, "maxHourRevenueFrom": {"type": "number", "format": "double"}, "maxHourRevenueTo": {"type": "number", "format": "double"}, "maxLikesFrom": {"type": "number", "format": "double"}, "maxLikesTo": {"type": "number", "format": "double"}, "maxPopularRankFrom": {"type": "number", "format": "double"}, "maxPopularRankTo": {"type": "number", "format": "double"}, "maxRevenueFrom": {"type": "number", "format": "double"}, "maxRevenueTo": {"type": "number", "format": "double"}, "maxViewersFrom": {"type": "integer", "format": "int64"}, "maxViewersTo": {"type": "integer", "format": "int64"}, "maxWeekRevenueFrom": {"type": "number", "format": "double"}, "maxWeekRevenueTo": {"type": "number", "format": "double"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "messageStatusList": {"type": "array", "items": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, "name": {"type": "string"}, "platformType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "region": {"type": "string"}, "regions": {"type": "array", "items": {"type": "string"}}, "responsibleUserId": {"type": "integer", "format": "int64"}, "revenueLc": {"type": "string", "enum": ["AND", "NOT", "OR"]}, "showcase": {"type": "boolean"}, "statusList": {"type": "array", "items": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, "tagIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "tagLc": {"type": "string", "description": "标签的逻辑条件，支持OR|AND", "enum": ["AND", "NOT", "OR"]}}}, "FindGhGifterRequest": {"title": "FindGhGifterRequest", "type": "object", "properties": {"alias": {"type": "string"}, "containsTag": {"type": "boolean", "description": "包含指定标签。取false时，tagLc只能是OR", "example": false}, "createTimeFrom": {"type": "string", "format": "date-time"}, "createTimeLastDays": {"type": "integer", "format": "int32"}, "createTimeTo": {"type": "string", "format": "date-time"}, "creatorHandle": {"type": "string"}, "deleting": {"type": "boolean"}, "filterByFavorite": {"type": "boolean"}, "followerCntFrom": {"type": "integer", "format": "int32"}, "followerCntTo": {"type": "integer", "format": "int32"}, "giftMaxFrom": {"type": "integer", "format": "int32"}, "giftMaxTo": {"type": "integer", "format": "int32"}, "giftTotalFrom": {"type": "integer", "format": "int32"}, "giftTotalTo": {"type": "integer", "format": "int32"}, "gifterLevelFrom": {"type": "integer", "format": "int32"}, "gifterLevelTo": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "hasNoRegion": {"type": "boolean"}, "hasResponsibleUser": {"type": "boolean"}, "lastSyncTimeFrom": {"type": "string", "format": "date-time"}, "lastSyncTimeLastDays": {"type": "integer", "format": "int32"}, "lastSyncTimeTo": {"type": "string", "format": "date-time"}, "limit": {"type": "integer", "format": "int32"}, "messageStatusList": {"type": "array", "items": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, "myStatusList": {"type": "array", "items": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, "region": {"type": "string"}, "regions": {"type": "array", "items": {"type": "string"}}, "responsibleUserId": {"type": "integer", "format": "int64"}, "statusList": {"type": "array", "items": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, "tagIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "tagLc": {"type": "string", "description": "标签的逻辑条件，支持OR|AND", "enum": ["AND", "NOT", "OR"]}, "videoCntFrom": {"type": "integer", "format": "int32"}, "videoCntTo": {"type": "integer", "format": "int32"}, "videoLikesFrom": {"type": "integer", "format": "int32"}, "videoLikesTo": {"type": "integer", "format": "int32"}}}, "FindGhUserRequest": {"title": "FindGhUserRequest", "type": "object", "properties": {"alias": {"type": "string"}, "containsTag": {"type": "boolean", "description": "包含指定标签。取false时，tagLc只能是OR", "example": false}, "createTimeFrom": {"type": "string", "format": "date-time"}, "createTimeLastDays": {"type": "integer", "format": "int32"}, "createTimeTo": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "filterByFavorite": {"type": "boolean"}, "followerCntFrom": {"type": "integer", "format": "int32"}, "followerCntTo": {"type": "integer", "format": "int32"}, "gifterLevelFrom": {"type": "integer", "format": "int32"}, "gifterLevelTo": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "hasNoRegion": {"type": "boolean"}, "hasResponsibleUser": {"type": "boolean"}, "lastSyncTimeFrom": {"type": "string", "format": "date-time"}, "lastSyncTimeLastDays": {"type": "integer", "format": "int32"}, "lastSyncTimeTo": {"type": "string", "format": "date-time"}, "limit": {"type": "integer", "format": "int32"}, "messageStatusList": {"type": "array", "items": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, "region": {"type": "string"}, "regions": {"type": "array", "items": {"type": "string"}}, "responsibleUserId": {"type": "integer", "format": "int64"}, "statusList": {"type": "array", "items": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, "tagIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "tagLc": {"type": "string", "description": "标签的逻辑条件，支持OR|AND", "enum": ["AND", "NOT", "OR"]}, "videoCntFrom": {"type": "integer", "format": "int32"}, "videoCntTo": {"type": "integer", "format": "int32"}, "videoLikesFrom": {"type": "integer", "format": "int32"}, "videoLikesTo": {"type": "integer", "format": "int32"}}}, "FindGhVideoCreatorRequest": {"title": "FindGhVideoCreatorRequest", "type": "object", "properties": {"alias": {"type": "string"}, "containsTag": {"type": "boolean", "description": "包含指定标签。取false时，tagLc只能是OR", "example": false}, "createTimeFrom": {"type": "string", "format": "date-time"}, "createTimeLastDays": {"type": "integer", "format": "int32"}, "createTimeTo": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "filterByFavorite": {"type": "boolean"}, "followerCntFrom": {"type": "integer", "format": "int32"}, "followerCntTo": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "hasNoRegion": {"type": "boolean"}, "hasResponsibleUser": {"type": "boolean"}, "lastSyncTimeFrom": {"type": "string", "format": "date-time"}, "lastSyncTimeLastDays": {"type": "integer", "format": "int32"}, "lastSyncTimeTo": {"type": "string", "format": "date-time"}, "limit": {"type": "integer", "format": "int32"}, "messageStatusList": {"type": "array", "items": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}}, "region": {"type": "string"}, "regions": {"type": "array", "items": {"type": "string"}}, "responsibleUserId": {"type": "integer", "format": "int64"}, "statusList": {"type": "array", "items": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, "tagIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "tagLc": {"type": "string", "description": "标签的逻辑条件，支持OR|AND", "enum": ["AND", "NOT", "OR"]}, "videoCntFrom": {"type": "integer", "format": "int32"}, "videoCntTo": {"type": "integer", "format": "int32"}, "videoLikesFrom": {"type": "integer", "format": "int32"}, "videoLikesTo": {"type": "integer", "format": "int32"}}}, "GhAvailableVo": {"title": "GhAvailableVo", "type": "object", "properties": {"available": {"type": "boolean"}, "availableStatus": {"type": "string", "enum": ["Available", "Unavailable", "Unset"]}, "creatorId": {"type": "string"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "region": {"type": "string"}}}, "GhCreatorAllocateRequest": {"title": "GhCreatorAllocateRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "responsibleUserId": {"type": "integer", "format": "int64"}, "tag": {"type": "string"}}}, "GhCreatorDetailVo": {"title": "GhCreatorDetailVo", "type": "object", "properties": {"alias": {"type": "string"}, "anchor": {"type": "boolean"}, "available": {"type": "string", "enum": ["Available", "Unavailable", "Unset"]}, "avatar": {"type": "string"}, "avgLikes": {"type": "number", "format": "double"}, "avgRevenue": {"type": "number", "format": "double"}, "avgViewers": {"type": "number", "format": "double"}, "backstageShopId": {"type": "integer", "format": "int64"}, "commerceUser": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "deleteRemark": {"type": "string"}, "elite": {"type": "boolean"}, "email": {"type": "string"}, "firstInteraction": {"description": "首次交互", "$ref": "#/components/schemas/GhInteractionDto"}, "followerCnt": {"type": "integer", "format": "int32"}, "gaming": {"type": "boolean"}, "general": {"type": "boolean"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "hyperlink": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "importTime": {"type": "string", "format": "date-time"}, "importType": {"type": "string", "enum": ["Collected", "Imported"]}, "lastGameRank": {"type": "number", "format": "double"}, "lastHourRevenue": {"type": "number", "format": "double"}, "lastInteractTime": {"type": "string", "format": "date-time"}, "lastInteractType": {"type": "string", "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}, "lastInteraction": {"description": "最后一次交互", "$ref": "#/components/schemas/GhInteractionDto"}, "lastLikes": {"type": "integer", "format": "int32"}, "lastLiveTime": {"type": "string", "format": "date-time"}, "lastPopularRank": {"type": "number", "format": "double"}, "lastRevenue": {"type": "number", "format": "double"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "lastViewers": {"type": "integer", "format": "int32"}, "lastWeekRevenue": {"type": "number", "format": "double"}, "liveCount": {"type": "integer", "description": "直播次数", "format": "int32"}, "liveRate": {"type": "number", "format": "double"}, "liveRewardsMobileAccountId": {"type": "integer", "format": "int64"}, "liveRewardsShopId": {"type": "integer", "format": "int64"}, "maxGameRank": {"type": "number", "format": "double"}, "maxHourRevenue": {"type": "number", "format": "double"}, "maxLikes": {"type": "integer", "format": "int32"}, "maxPopularRank": {"type": "number", "format": "double"}, "maxRevenue": {"type": "number", "format": "double"}, "maxViewers": {"type": "integer", "format": "int32"}, "maxWeekRevenue": {"type": "number", "format": "double"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "mobile": {"type": "string"}, "mobileAccountId": {"type": "integer", "format": "int64"}, "officialShopId": {"type": "integer", "format": "int64"}, "platformType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "pmShopId": {"type": "integer", "format": "int64"}, "region": {"type": "string"}, "responsibleUser": {"description": "认领人", "$ref": "#/components/schemas/UserDto"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "showcase": {"type": "boolean"}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}, "tags": {"type": "array", "description": "标签", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}, "ttSeller": {"type": "boolean"}, "unionShopId": {"type": "integer", "format": "int64"}, "videoCnt": {"type": "integer", "format": "int32"}, "videoLikes": {"type": "integer", "format": "int32"}, "whatsapp": {"type": "string"}}}, "GhCreatorDocument": {"title": "GhCreatorDocument", "type": "object", "properties": {"alias": {"type": "string"}, "anchor": {"type": "boolean"}, "avatar": {"type": "string"}, "categories": {"type": "array", "items": {"type": "string"}}, "commerceUser": {"type": "boolean"}, "creatorId": {"type": "string"}, "elite": {"type": "boolean"}, "email": {"type": "string"}, "followerCnt": {"type": "integer", "format": "int32"}, "gaming": {"type": "boolean"}, "general": {"type": "boolean"}, "handle": {"type": "string"}, "liveDocument": {"$ref": "#/components/schemas/GhLiveDocument"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "mobile": {"type": "string"}, "platformType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "region": {"type": "string"}, "showcase": {"type": "boolean"}, "ttSeller": {"type": "boolean"}, "videoCnt": {"type": "integer", "format": "int32"}, "videoLikes": {"type": "integer", "format": "int32"}, "whatsapp": {"type": "string"}}}, "GhCreatorDocumentBatch": {"title": "GhCreatorDocumentBatch", "type": "object", "properties": {"creators": {"type": "array", "items": {"$ref": "#/components/schemas/GhCreatorDocument"}}}}, "GhCreatorDto": {"title": "GhCreatorDto", "type": "object", "properties": {"alias": {"type": "string"}, "anchor": {"type": "boolean"}, "available": {"type": "string", "enum": ["Available", "Unavailable", "Unset"]}, "avatar": {"type": "string"}, "avgLikes": {"type": "number", "format": "double"}, "avgRevenue": {"type": "number", "format": "double"}, "avgViewers": {"type": "number", "format": "double"}, "backstageShopId": {"type": "integer", "format": "int64"}, "commerceUser": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "deleteRemark": {"type": "string"}, "elite": {"type": "boolean"}, "email": {"type": "string"}, "followerCnt": {"type": "integer", "format": "int32"}, "gaming": {"type": "boolean"}, "general": {"type": "boolean"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "importTime": {"type": "string", "format": "date-time"}, "importType": {"type": "string", "enum": ["Collected", "Imported"]}, "lastGameRank": {"type": "number", "format": "double"}, "lastHourRevenue": {"type": "number", "format": "double"}, "lastInteractTime": {"type": "string", "format": "date-time"}, "lastInteractType": {"type": "string", "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}, "lastLikes": {"type": "integer", "format": "int32"}, "lastLiveTime": {"type": "string", "format": "date-time"}, "lastPopularRank": {"type": "number", "format": "double"}, "lastRevenue": {"type": "number", "format": "double"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "lastViewers": {"type": "integer", "format": "int32"}, "lastWeekRevenue": {"type": "number", "format": "double"}, "liveRate": {"type": "number", "format": "double"}, "liveRewardsMobileAccountId": {"type": "integer", "format": "int64"}, "liveRewardsShopId": {"type": "integer", "format": "int64"}, "maxGameRank": {"type": "number", "format": "double"}, "maxHourRevenue": {"type": "number", "format": "double"}, "maxLikes": {"type": "integer", "format": "int32"}, "maxPopularRank": {"type": "number", "format": "double"}, "maxRevenue": {"type": "number", "format": "double"}, "maxViewers": {"type": "integer", "format": "int32"}, "maxWeekRevenue": {"type": "number", "format": "double"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "mobile": {"type": "string"}, "mobileAccountId": {"type": "integer", "format": "int64"}, "officialShopId": {"type": "integer", "format": "int64"}, "platformType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "pmShopId": {"type": "integer", "format": "int64"}, "region": {"type": "string"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "showcase": {"type": "boolean"}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}, "teamId": {"type": "integer", "format": "int64"}, "ttSeller": {"type": "boolean"}, "unionShopId": {"type": "integer", "format": "int64"}, "videoCnt": {"type": "integer", "format": "int32"}, "videoLikes": {"type": "integer", "format": "int32"}, "whatsapp": {"type": "string"}}}, "GhCreatorExpiredAutoTags": {"title": "GhCreatorExpiredAutoTags", "type": "object", "properties": {"new_message": {"type": "string"}}}, "GhCreatorImportRequest": {"title": "GhCreatorImportRequest", "type": "object", "properties": {"handles": {"type": "array", "items": {"type": "string"}}, "region": {"type": "string", "description": "所属区域"}, "tags": {"type": "array", "items": {"type": "string"}}}}, "GhCreatorInterface": {"title": "GhCreatorInterface", "type": "object"}, "GhCreatorPerformanceStatVo": {"title": "GhCreatorPerformanceStatVo", "type": "object", "properties": {"allocated": {"type": "integer", "description": "已分配的", "format": "int32"}, "replied": {"type": "integer", "description": "已建联的", "format": "int32"}, "responsibleUserId": {"type": "integer", "description": "认领人ID", "format": "int64"}, "sent": {"type": "integer", "description": "已触达的", "format": "int32"}}}, "GhCreatorRequest": {"title": "GhCreatorRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "GhCreatorStatusVo": {"title": "GhCreatorStatusVo", "type": "object", "properties": {"handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}}, "GhCreatorSyncHasNewMsgRequest": {"title": "GhCreatorSyncHasNewMsgRequest", "type": "object", "properties": {"handles": {"type": "array", "items": {"type": "string"}}, "hasNewMsg": {"type": "boolean"}}}, "GhCreatorSyncStatusRequest": {"title": "GhCreatorSyncStatusRequest", "type": "object", "properties": {"handles": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}}, "GhCreatorUpdateAnchorRequest": {"title": "GhCreatorUpdateAnchorRequest", "type": "object", "properties": {"anchor": {"type": "boolean"}, "handles": {"type": "array", "items": {"type": "string"}}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "GhCreatorUpdateAvailableRequest": {"title": "GhCreatorUpdateAvailableRequest", "type": "object", "properties": {"available": {"type": "boolean"}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "nameList": {"type": "array", "items": {"type": "string"}}, "risk": {"type": "boolean"}, "signed": {"type": "boolean"}}}, "GhCreatorUpdateBackstageShopIdRequest": {"title": "GhCreatorUpdateBackstageShopIdRequest", "type": "object", "properties": {"backstageShopId": {"type": "integer", "format": "int64"}, "creatorIds": {"type": "array", "items": {"type": "string"}}, "handles": {"type": "array", "items": {"type": "string"}}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "GhCreatorUpdateEliteRequest": {"title": "GhCreatorUpdateEliteRequest", "type": "object", "properties": {"elite": {"type": "boolean"}, "handles": {"type": "array", "items": {"type": "string"}}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "GhCreatorUpdateGeneralRequest": {"title": "GhCreatorUpdateGeneralRequest", "type": "object", "properties": {"general": {"type": "boolean"}, "handles": {"type": "array", "items": {"type": "string"}}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "GhCreatorUpdateHasNewMsgRequest": {"title": "GhCreatorUpdateHasNewMsgRequest", "type": "object", "properties": {"handles": {"type": "array", "items": {"type": "string"}}, "hasNewMsg": {"type": "boolean"}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "GhCreatorUpdateMessageStatusRequest": {"title": "GhCreatorUpdateMessageStatusRequest", "type": "object", "properties": {"handles": {"type": "array", "items": {"type": "string"}}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "pmByFollow": {"type": "boolean"}}}, "GhCreatorUpdateShowcaseRequest": {"title": "GhCreatorUpdateShowcaseRequest", "type": "object", "properties": {"handles": {"type": "array", "items": {"type": "string"}}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "showcase": {"type": "boolean"}}}, "GhCreatorUpdateStatusRequest": {"title": "GhCreatorUpdateStatusRequest", "type": "object", "properties": {"assigned": {"type": "boolean"}, "handles": {"type": "array", "items": {"type": "string"}}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}}, "GhGeneralSettingsVo": {"title": "GhGeneralSettingsVo", "type": "object", "properties": {"devices": {"type": "array", "items": {"$ref": "#/components/schemas/GhJobDeviceVo"}}, "failPolicies": {"type": "object", "additionalProperties": {"type": "string"}}, "ghBackstageConfig": {"$ref": "#/components/schemas/GhScheduleConfig"}, "mobileConfig": {"$ref": "#/components/schemas/GhScheduleConfig"}, "normalConfig": {"$ref": "#/components/schemas/GhScheduleConfig"}, "officialConfig": {"$ref": "#/components/schemas/GhScheduleConfig"}, "priority": {"type": "string", "description": "流程调度优先级，以逗号分隔开，排在前面的优先调度，默认值为 'SendPm,SyncPm,SendInvite,SyncCreator' "}, "syncCreatorConfig": {"description": "主播信息更新配置", "$ref": "#/components/schemas/SyncCreatorConfig"}, "unionConfig": {"description": "联盟号相关配置", "$ref": "#/components/schemas/GhScheduleConfig"}}}, "GhGetRegionByCreatorIdRequest": {"title": "GhGetRegionByCreatorIdRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}}}, "GhGifterAllocateRequest": {"title": "GhGifterAllocateRequest", "type": "object", "properties": {"cleanFirst": {"type": "boolean"}, "ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "responsibleUserIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "tag": {"type": "string"}}}, "GhGifterAssignUser": {"title": "GhGifterAssignUser", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "ghStatus": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "residentCity": {"type": "string"}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}}}, "GhGifterDetailVo": {"title": "GhGifterDetailVo", "type": "object", "properties": {"alias": {"type": "string"}, "avatar": {"type": "string"}, "backstageShopId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "deleteRemark": {"type": "string"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "followerCnt": {"type": "integer", "format": "int32"}, "giftCount": {"type": "integer", "description": "打赏记录条数", "format": "int32"}, "giftMax": {"type": "integer", "format": "int32"}, "giftTotal": {"type": "integer", "format": "int32"}, "gifterLevel": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "hyperlink": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "mobileAccountId": {"type": "integer", "format": "int64"}, "officialShopId": {"type": "integer", "format": "int64"}, "pmShopId": {"type": "integer", "format": "int64"}, "region": {"type": "string"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "responsibleUsers": {"type": "array", "items": {"$ref": "#/components/schemas/GhGifterAssignUser"}}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}, "tags": {"type": "array", "description": "标签", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}, "videoCnt": {"type": "integer", "format": "int32"}, "videoLikes": {"type": "integer", "format": "int32"}}}, "GhGifterDto": {"title": "GhGifterDto", "type": "object", "properties": {"alias": {"type": "string"}, "avatar": {"type": "string"}, "backstageShopId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "deleteRemark": {"type": "string"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "followerCnt": {"type": "integer", "format": "int32"}, "giftMax": {"type": "integer", "format": "int32"}, "giftTotal": {"type": "integer", "format": "int32"}, "gifterLevel": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "mobileAccountId": {"type": "integer", "format": "int64"}, "officialShopId": {"type": "integer", "format": "int64"}, "pmShopId": {"type": "integer", "format": "int64"}, "region": {"type": "string"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}, "teamId": {"type": "integer", "format": "int64"}, "videoCnt": {"type": "integer", "format": "int32"}, "videoLikes": {"type": "integer", "format": "int32"}}}, "GhInteractionDetailVo": {"title": "GhInteractionDetailVo", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "extraInfo": {"type": "object"}, "flowId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "interactTime": {"type": "integer", "format": "int64"}, "interactType": {"type": "string", "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}, "interactionId": {"type": "string"}, "operator": {"$ref": "#/components/schemas/UserDto"}, "operatorId": {"type": "integer", "format": "int64"}, "rpaFlow": {"$ref": "#/components/schemas/RpaFlowDto"}, "rpaTask": {"$ref": "#/components/schemas/RpaTaskDto"}, "rpaType": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "shop": {"$ref": "#/components/schemas/ShopDto"}, "shopId": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}, "taskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "GhInteractionDto": {"title": "GhInteractionDto", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "flowId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "interactTime": {"type": "integer", "format": "int64"}, "interactType": {"type": "string", "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}, "interactionId": {"type": "string"}, "operatorId": {"type": "integer", "format": "int64"}, "rpaType": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "shopId": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}, "taskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "GhInteractionVo": {"title": "GhInteractionVo", "type": "object", "properties": {"creatorId": {"type": "string", "description": "达人ID"}, "description": {"type": "string", "description": "交互的描述"}, "extraInfo": {"type": "object", "description": "额外信息，转化成JSON总长度不要超过8000"}, "flowId": {"type": "integer", "description": "操作流程", "format": "int64"}, "handle": {"type": "string", "description": "达人账号"}, "interactTimestamp": {"type": "integer", "description": "交互时间戳", "format": "int64"}, "interactType": {"type": "string", "description": "交互类型", "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}, "interactionId": {"type": "string", "description": "交互D"}, "rpaType": {"type": "string", "description": "RpaType", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "shopId": {"type": "integer", "description": "店铺ID", "format": "int64"}, "success": {"type": "boolean", "description": "是否成功", "example": false}, "taskId": {"type": "integer", "description": "流程任务ID", "format": "int64"}}}, "GhJobDetailVo": {"title": "GhJobDetailVo", "type": "object", "properties": {"allowedStartTime": {"type": "string", "description": "允许开始执行的时间", "format": "date-time"}, "bizScene": {"type": "string", "description": "什么类型的达人", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "clientId": {"type": "string"}, "createTime": {"type": "string", "description": "该条任务创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者id", "format": "int64"}, "creatorName": {"type": "string", "description": "创建者昵称"}, "creators": {"type": "array", "items": {"$ref": "#/components/schemas/SimpleGhCreatorVo"}}, "description": {"type": "string"}, "device": {"description": "执行设备", "$ref": "#/components/schemas/LoginDeviceDto"}, "deviceName": {"type": "string", "description": "执行设备的名称"}, "executeEndTime": {"type": "string", "description": "执行结束时间", "format": "date-time"}, "executeTime": {"type": "string", "description": "开始执行时间", "format": "date-time"}, "ghCreatorId": {"type": "integer", "description": "主播id", "format": "int64"}, "ghCreatorName": {"type": "string", "description": "主播昵称"}, "ghPlanId": {"type": "integer", "format": "int64"}, "ghPlatformType": {"type": "string", "description": "公会平台类型，tk、抖音、小红书", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "ghScheduleType": {"type": "string", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "id": {"type": "integer", "format": "int64"}, "jobType": {"type": "string", "enum": ["Acco<PERSON><PERSON><PERSON><PERSON>", "AccountHealthCheck", "AccountMaintenance", "AccountMessageCheck", "KOL_LiveRewards", "<PERSON><PERSON><PERSON><PERSON>", "SendInvite", "SendPm", "SendUserCard", "ShareVideo", "<PERSON><PERSON><PERSON>", "SyncContactToPhone", "SyncContacts", "SyncCreator", "SyncLiveStream", "SyncPm", "TS_AddContacts", "TS_AddFriends", "TS_DemandPayment", "TS_IMChatByFilter", "TS_IMChatByHandle", "TS_LoginCheck", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendEmail", "TS_SendFacebook", "TS_SendLine", "TS_SendWhatsApp", "TS_SendZalo", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncProducts", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_TargetPlanClear", "TS_TargetPlanModify", "TS_VideoADCode"]}, "params": {"type": "string", "description": "任务参数，json格式"}, "planRunId": {"type": "string"}, "rpaTaskId": {"type": "integer", "format": "int64"}, "rpaType": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "shopId": {"type": "integer", "description": "用来执行该job的分身", "format": "int64"}, "shopName": {"type": "string", "description": "用来执行该job的分身名"}, "shops": {"type": "array", "items": {"$ref": "#/components/schemas/SimpleShopVo"}}, "status": {"type": "string", "description": "job状态", "enum": ["Cancelled", "Failed", "Pending", "Running", "Scheduled", "Succeed"]}, "subCounts": {"type": "integer", "description": "子任务个数，当任务为信息更新/私信更新时，根据该字段来显示 xxx个主播 ", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "GhJobDeviceVo": {"title": "GhJobDeviceVo", "type": "object", "properties": {"appVersion": {"type": "string"}, "clientId": {"type": "string"}, "concurrent": {"type": "integer", "format": "int32"}, "cpus": {"type": "integer", "format": "int32"}, "deviceType": {"type": "string", "enum": ["App", "Browser", "Extension", "HYRuntime", "RpaExecutor"]}, "hostName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastUserId": {"type": "integer", "format": "int64"}, "mem": {"type": "integer", "format": "int64"}, "online": {"type": "boolean"}, "osName": {"type": "string"}, "rpaVoucherValid": {"type": "boolean"}, "userAgent": {"type": "string"}}}, "GhJobDto": {"title": "GhJobDto", "type": "object", "properties": {"allowedStartTime": {"type": "string", "description": "允许开始执行的时间", "format": "date-time"}, "bizScene": {"type": "string", "description": "什么类型的达人", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "clientId": {"type": "string"}, "createTime": {"type": "string", "description": "该条任务创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者id", "format": "int64"}, "creatorName": {"type": "string", "description": "创建者昵称"}, "description": {"type": "string"}, "device": {"description": "执行设备", "$ref": "#/components/schemas/LoginDeviceDto"}, "deviceName": {"type": "string", "description": "执行设备的名称"}, "executeEndTime": {"type": "string", "description": "执行结束时间", "format": "date-time"}, "executeTime": {"type": "string", "description": "开始执行时间", "format": "date-time"}, "ghCreatorId": {"type": "integer", "description": "主播id", "format": "int64"}, "ghCreatorName": {"type": "string", "description": "主播昵称"}, "ghPlanId": {"type": "integer", "format": "int64"}, "ghPlatformType": {"type": "string", "description": "公会平台类型，tk、抖音、小红书", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "ghScheduleType": {"type": "string", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "id": {"type": "integer", "format": "int64"}, "jobType": {"type": "string", "enum": ["Acco<PERSON><PERSON><PERSON><PERSON>", "AccountHealthCheck", "AccountMaintenance", "AccountMessageCheck", "KOL_LiveRewards", "<PERSON><PERSON><PERSON><PERSON>", "SendInvite", "SendPm", "SendUserCard", "ShareVideo", "<PERSON><PERSON><PERSON>", "SyncContactToPhone", "SyncContacts", "SyncCreator", "SyncLiveStream", "SyncPm", "TS_AddContacts", "TS_AddFriends", "TS_DemandPayment", "TS_IMChatByFilter", "TS_IMChatByHandle", "TS_LoginCheck", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendEmail", "TS_SendFacebook", "TS_SendLine", "TS_SendWhatsApp", "TS_SendZalo", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncProducts", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_TargetPlanClear", "TS_TargetPlanModify", "TS_VideoADCode"]}, "params": {"type": "string", "description": "任务参数，json格式"}, "planRunId": {"type": "string"}, "rpaTaskId": {"type": "integer", "format": "int64"}, "rpaType": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "shopId": {"type": "integer", "description": "用来执行该job的分身", "format": "int64"}, "shopName": {"type": "string", "description": "用来执行该job的分身名"}, "status": {"type": "string", "description": "job状态", "enum": ["Cancelled", "Failed", "Pending", "Running", "Scheduled", "Succeed"]}, "subCounts": {"type": "integer", "description": "子任务个数，当任务为信息更新/私信更新时，根据该字段来显示 xxx个主播 ", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "GhJobPlanVo": {"title": "GhJobPlanVo", "type": "object", "properties": {"bizScene": {"type": "string", "description": "场景", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "commentWordIds": {"type": "array", "description": "邀约时评论要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义", "items": {"type": "integer", "format": "int64"}}, "createTime": {"type": "string", "format": "date-time"}, "creatorFilter": {"type": "string", "description": "查询条件，由 /api/gh/creator/page 这个接口的params JSON.stringify(params) 而来"}, "cronExpression": {"type": "string", "description": "重复日期，只包含星期信息的表达式(具体参考自动流程计划)"}, "deleteMsg": {"type": "boolean"}, "deleteSession": {"type": "boolean", "description": "同步时是否删除与主播的聊天记录，默认为true。只在 ghJobType 为 SyncPm 时有意义", "example": false}, "description": {"type": "string"}, "enabled": {"type": "boolean", "description": "是否启用(目前UI无法体现这一属性)", "example": false}, "expressions": {"type": "array", "description": "如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...]", "items": {"type": "string"}}, "ghPlatformType": {"type": "string", "description": "公会平台类型，tk、抖音、小红书", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "ghScheduleType": {"type": "string", "description": "拟使用的TK账号类型，默认值 Normal。只在 ghJobType 为 SendInvite 时有意义", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "id": {"type": "integer", "format": "int64"}, "inviteAdvanceSettings": {"type": "object", "description": "私信高级设置，只在 ghJobType 为 SendInvite/AccountMaintenance 时有意义"}, "inviteMobileChoicePolicy": {"description": "发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义", "$ref": "#/components/schemas/MobileChoicePolicy"}, "inviteShopChoicePolicy": {"description": "发送邀约分身选择策略，仅在 ghScheduleType != Mobile 的时候有意义", "$ref": "#/components/schemas/ShopChoicePolicy"}, "jobType": {"type": "string", "enum": ["Acco<PERSON><PERSON><PERSON><PERSON>", "AccountHealthCheck", "AccountMaintenance", "AccountMessageCheck", "KOL_LiveRewards", "<PERSON><PERSON><PERSON><PERSON>", "SendInvite", "SendPm", "SendUserCard", "ShareVideo", "<PERSON><PERSON><PERSON>", "SyncContactToPhone", "SyncContacts", "SyncCreator", "SyncLiveStream", "SyncPm", "TS_AddContacts", "TS_AddFriends", "TS_DemandPayment", "TS_IMChatByFilter", "TS_IMChatByHandle", "TS_LoginCheck", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendEmail", "TS_SendFacebook", "TS_SendLine", "TS_SendWhatsApp", "TS_SendZalo", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncProducts", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_TargetPlanClear", "TS_TargetPlanModify", "TS_VideoADCode"]}, "maxMinutes": {"type": "integer", "description": "最多允许执行多少分钟(需求方要求是流程自己实现，所以应该只有部分流程支持这个参数)", "format": "int32"}, "mobileAccountIds": {"type": "array", "description": "相应的手机账号id，只有在 ghJobType 为 AccountMaintenance 时有意义", "items": {"type": "integer", "format": "int64"}}, "name": {"type": "string"}, "nextFireTime": {"type": "string", "description": "下次执行时间", "format": "date-time"}, "planType": {"type": "string", "description": "计划类型，归属团队还是个人", "enum": ["Team", "User"]}, "sendComment": {"type": "boolean", "description": "发送私信前关注主播并将私信内容评论到主播的第一条视频", "example": false}, "sendEmoji": {"type": "boolean", "description": "是否增加随机表情", "example": false}, "sendInviteCard": {"type": "boolean", "description": "是否发送邀约卡片，只有发送邀约建联计划才有意义(只有公会后台账号才支持此特性）", "example": false}, "skipResponsible": {"type": "boolean", "description": "已分配/已认领的达人不再发送 (为空表示 true )，只对发送邀约建联的计划有意义", "example": false}, "teamId": {"type": "integer", "format": "int64"}, "wordsIds": {"type": "array", "description": "邀约要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义", "items": {"type": "integer", "format": "int64"}}}}, "GhLiveCreatorDetailVo": {"title": "GhLiveCreatorDetailVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creator": {"$ref": "#/components/schemas/GhCreatorDto"}, "gameRank": {"type": "number", "format": "double"}, "handle": {"type": "string"}, "hourRevenue": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "likes": {"type": "integer", "format": "int32"}, "liveId": {"type": "string"}, "liveTime": {"type": "string", "format": "date-time"}, "popularRank": {"type": "number", "format": "double"}, "region": {"type": "string"}, "revenue": {"type": "number", "format": "double"}, "tags": {"type": "array", "description": "标签", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}, "viewers": {"type": "integer", "format": "int32"}, "weekRevenue": {"type": "number", "format": "double"}}}, "GhLiveCreatorDocumentBatch": {"title": "GhLiveCreatorDocumentBatch", "type": "object", "properties": {"creators": {"type": "array", "items": {"$ref": "#/components/schemas/GhLiveDocument"}}}}, "GhLiveDocument": {"title": "GhLiveDocument", "type": "object", "properties": {"createTimestamp": {"type": "integer", "format": "int64"}, "creatorId": {"type": "string"}, "gameRank": {"type": "number", "format": "double"}, "handle": {"type": "string"}, "hourRevenue": {"type": "number", "format": "double"}, "likes": {"type": "integer", "format": "int32"}, "liveId": {"type": "string"}, "popularRank": {"type": "number", "format": "double"}, "region": {"type": "string"}, "revenue": {"type": "number", "format": "double"}, "viewers": {"type": "integer", "format": "int32"}, "weekRevenue": {"type": "number", "format": "double"}}}, "GhLiveGiftDocument": {"title": "GhLiveGiftDocument", "type": "object", "properties": {"avatar": {"type": "string"}, "createTimestamp": {"type": "integer", "format": "int64"}, "gift": {"type": "integer", "format": "int32"}, "gifterAvatar": {"type": "string"}, "gifterHandle": {"type": "string"}, "gifterLevel": {"type": "integer", "description": "送礼者等级", "format": "int32"}, "gitferCreatorId": {"type": "string"}, "gitferRegion": {"type": "string"}, "handle": {"type": "string"}, "liveId": {"type": "string"}, "liveLevel": {"type": "integer", "format": "int32"}}}, "GhLiveGiftDto": {"title": "GhLiveGiftDto", "type": "object", "properties": {"avatar": {"type": "string"}, "gift": {"type": "integer", "format": "int32"}, "gifterHandle": {"type": "string"}, "gifterId": {"type": "integer", "format": "int64"}, "gitferCreatorId": {"type": "string"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "liveId": {"type": "string"}, "liveLevel": {"type": "integer", "format": "int32"}, "liveTime": {"type": "string", "format": "date-time"}, "teamId": {"type": "integer", "format": "int64"}}}, "GhMessageDto": {"title": "GhMessageDto", "type": "object", "properties": {"accountId": {"type": "integer", "format": "int64"}, "accountName": {"type": "string"}, "bizScene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "cid": {"type": "string"}, "content": {"type": "string"}, "creatorId": {"type": "integer", "format": "int64"}, "extra": {"type": "string"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "messageId": {"type": "string"}, "messageSource": {"type": "string", "enum": ["Creator", "TkUser"]}, "replyUserId": {"type": "integer", "format": "int64"}, "rpaType": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "scheduleType": {"type": "string", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "sendTime": {"type": "string", "format": "date-time"}, "status": {"type": "string", "enum": ["Fail", "Pending", "Success"]}, "syncTime": {"type": "string", "format": "date-time"}, "teamId": {"type": "integer", "format": "int64"}}}, "GhMessageVo": {"title": "GhMessageVo", "type": "object", "properties": {"content": {"type": "string", "description": "消息内容"}, "extra": {"type": "string", "description": "json格式的额外信息，保存消息是通过哪个手机的账号发出去的"}, "messageId": {"type": "string", "description": "消息ID，全局唯一（如果有）"}, "messageSource": {"type": "string", "description": "谁发送的消息", "enum": ["Creator", "TkUser"]}, "rpaType": {"type": "string", "description": "消息是手机发送的还是浏览器发送的", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "sendTimestamp": {"type": "integer", "description": "消息发送时间（如果有）", "format": "int64"}}}, "GhRevenueDocument": {"title": "GhRevenueDocument", "type": "object", "properties": {"gameRank": {"type": "number", "format": "double"}, "handle": {"type": "string"}, "hourRevenue": {"type": "number", "format": "double"}, "popularRank": {"type": "number", "format": "double"}, "region": {"type": "string"}, "revenue": {"type": "number", "format": "double"}, "weekRevenue": {"type": "number", "format": "double"}}}, "GhScheduleConfig": {"title": "GhScheduleConfig", "type": "object", "properties": {"blockTags": {"type": "object", "additionalProperties": {"type": "string"}, "description": "有哪些表示账号已经封禁的标签，目前key的取值: account_logged_out | account_banned | account_limited | pm_too_fast | need_config_privacy"}, "concurrent": {"type": "integer", "description": "发送时最高并发数，为空或者为<=0都表示不限制 (目前只在公会后台账号邀约建联时有意义)", "format": "int32"}, "inviteTimeFrame": {"description": "允许邀请的时间", "$ref": "#/components/schemas/AllowTimeConfig"}, "maxShopDayPm": {"type": "integer", "description": "单账号每日最多私信条数", "format": "int32"}, "maxShopTaskPm": {"type": "integer", "description": "单账号每批次最多私信条数", "format": "int32"}, "shopCount": {"type": "integer", "description": "打上了这个标签的分身的个数", "format": "int32"}, "shopTag": {"type": "string", "description": "发送私信的分身的标签"}, "shopTaskPmInterval": {"type": "integer", "description": "单账号每批私信间隔，单位分钟", "format": "int32"}}}, "GhSessionVo": {"title": "GhSessionVo", "type": "object", "properties": {"accountId": {"type": "integer", "format": "int64"}, "avatar": {"type": "string"}, "bizScene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "cid": {"type": "string"}, "content": {"type": "string"}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastPushTime": {"type": "string", "format": "date-time"}, "lastReplyTime": {"type": "string", "format": "date-time"}, "receiveIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "replyUserId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Error", "NeedReply", "Replied", "Replying"]}, "teamId": {"type": "integer", "format": "int64"}}}, "GhShopStatistics": {"title": "GhShopStatistics", "type": "object", "properties": {"filterShopCount": {"type": "integer", "description": "筛选主播账号个数", "format": "int32"}, "ghBackendShopCount": {"type": "integer", "description": "公会后台私信账号个数", "format": "int32"}, "ghPlatformType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "invalidPmMobileAccountCount": {"type": "integer", "description": "手机私信账号数量(暂时不可用)", "format": "int32"}, "officePmShopCount": {"type": "integer", "description": "官方账号个数", "format": "int32"}, "unionPmShopCount": {"type": "integer", "description": "联盟号数量", "format": "int32"}, "validPmMobileAccountCount": {"type": "integer", "description": "手机私信账号数量(正常)", "format": "int32"}}}, "GhSpeechDto": {"title": "GhSpeechDto", "type": "object", "properties": {"bizScene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "content": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "groupId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "sortNo": {"type": "integer", "format": "int32"}, "speechType": {"type": "string", "enum": ["BuyerImChat", "BuyerSendMsg", "Comment", "Im<PERSON><PERSON>", "Invite", "LiveComment", "RequestAdCode", "SendMsg", "TargetPlan"]}, "teamId": {"type": "integer", "format": "int64"}}}, "GhSpeechGroupDto": {"title": "GhSpeechGroupDto", "type": "object", "properties": {"bizScene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "speechType": {"type": "string", "enum": ["BuyerImChat", "BuyerSendMsg", "Comment", "Im<PERSON><PERSON>", "Invite", "LiveComment", "RequestAdCode", "SendMsg", "TargetPlan"]}, "teamId": {"type": "integer", "format": "int64"}}}, "GhSpeechGroupVo": {"title": "GhSpeechGroupVo", "type": "object", "properties": {"bizScene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "count": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "speechType": {"type": "string", "enum": ["BuyerImChat", "BuyerSendMsg", "Comment", "Im<PERSON><PERSON>", "Invite", "LiveComment", "RequestAdCode", "SendMsg", "TargetPlan"]}, "teamId": {"type": "integer", "format": "int64"}}}, "GhStatRowVo": {"title": "GhStatRowVo", "type": "object", "properties": {"day": {"type": "string", "description": "日期", "format": "date-time"}, "stats": {"type": "object", "additionalProperties": {"type": "number"}}}}, "GhSyncNotFoundRequest": {"title": "GhSyncNotFoundRequest", "type": "object", "properties": {"handles": {"type": "array", "items": {"type": "string"}}}}, "GhTagByHandleRequest": {"title": "GhTagByHandleRequest", "type": "object", "properties": {"bizScene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "handles": {"type": "array", "items": {"type": "string"}}, "tags": {"type": "array", "items": {"type": "string"}}}}, "GhTagRequest": {"title": "GhTagRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "mobileAccount": {"type": "string"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TkshopSr", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "tag": {"type": "string"}}}, "GhUserDetailVo": {"title": "GhUserDetailVo", "type": "object", "properties": {"alias": {"type": "string"}, "avatar": {"type": "string"}, "backstageShopId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "deleteRemark": {"type": "string"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "followerCnt": {"type": "integer", "format": "int32"}, "gifterLevel": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "hyperlink": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "mobileAccountId": {"type": "integer", "format": "int64"}, "officialShopId": {"type": "integer", "format": "int64"}, "pmShopId": {"type": "integer", "format": "int64"}, "region": {"type": "string"}, "responsibleUser": {"$ref": "#/components/schemas/UserDto"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}, "tags": {"type": "array", "description": "标签", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}, "videoCnt": {"type": "integer", "format": "int32"}, "videoLikes": {"type": "integer", "format": "int32"}}}, "GhUserDocument": {"title": "GhUserDocument", "type": "object", "properties": {"alias": {"type": "string"}, "avatar": {"type": "string"}, "categories": {"type": "array", "items": {"type": "string"}}, "creatorId": {"type": "string"}, "followerCnt": {"type": "integer", "format": "int32"}, "gifterLevel": {"type": "integer", "description": "送礼者等级", "format": "int32"}, "handle": {"type": "string"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "region": {"type": "string"}, "videoCnt": {"type": "integer", "format": "int32"}, "videoLikes": {"type": "integer", "format": "int32"}}}, "GhUserDocumentBatch": {"title": "GhUserDocumentBatch", "type": "object", "properties": {"creators": {"type": "array", "items": {"$ref": "#/components/schemas/GhUserDocument"}}}}, "GhUserDto": {"title": "GhUserDto", "type": "object", "properties": {"alias": {"type": "string"}, "avatar": {"type": "string"}, "backstageShopId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "deleteRemark": {"type": "string"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "followerCnt": {"type": "integer", "format": "int32"}, "gifterLevel": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "mobileAccountId": {"type": "integer", "format": "int64"}, "officialShopId": {"type": "integer", "format": "int64"}, "pmShopId": {"type": "integer", "format": "int64"}, "region": {"type": "string"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}, "teamId": {"type": "integer", "format": "int64"}, "videoCnt": {"type": "integer", "format": "int32"}, "videoLikes": {"type": "integer", "format": "int32"}}}, "GhVideoCreatorDetailVo": {"title": "GhVideoCreatorDetailVo", "type": "object", "properties": {"alias": {"type": "string"}, "avatar": {"type": "string"}, "backstageShopId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string"}, "deleteRemark": {"type": "string"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "followerCnt": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "hasNewMsg": {"type": "boolean"}, "hyperlink": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "mobileAccountId": {"type": "integer", "format": "int64"}, "officialShopId": {"type": "integer", "format": "int64"}, "pmShopId": {"type": "integer", "format": "int64"}, "region": {"type": "string"}, "responsibleUser": {"$ref": "#/components/schemas/UserDto"}, "responsibleUserId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}, "tags": {"type": "array", "description": "标签", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}, "videoCnt": {"type": "integer", "format": "int32"}, "videoLikes": {"type": "integer", "format": "int32"}}}, "GhVideoCreatorDocument": {"title": "GhVideoCreatorDocument", "type": "object", "properties": {"alias": {"type": "string"}, "avatar": {"type": "string"}, "categories": {"type": "array", "items": {"type": "string"}}, "creatorId": {"type": "string"}, "followerCnt": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}, "messageStatus": {"type": "string", "enum": ["Everyone", "Friends", "None", "Recommands", "Unset"]}, "region": {"type": "string"}, "videoCnt": {"type": "integer", "format": "int32"}, "videoLikes": {"type": "integer", "format": "int32"}}}, "GhVideoCreatorDocumentBatch": {"title": "GhVideoCreatorDocumentBatch", "type": "object", "properties": {"creators": {"type": "array", "items": {"$ref": "#/components/schemas/GhVideoCreatorDocument"}}}}, "HandleCreatorIdVo": {"title": "HandleCreatorIdVo", "type": "object", "properties": {"creatorId": {"type": "string"}, "handle": {"type": "string"}}}, "HandleRegionBatchRequest": {"title": "HandleRegionBatchRequest", "type": "object", "properties": {"creators": {"type": "array", "items": {"$ref": "#/components/schemas/HandleRegionVo"}}}}, "HandleRegionVo": {"title": "HandleRegionVo", "type": "object", "properties": {"handle": {"type": "string"}, "region": {"type": "string"}}}, "HasGhInteractionRequest": {"title": "HasGhInteractionRequest", "type": "object", "properties": {"handlers": {"type": "array", "items": {"type": "string"}}, "interactTimeFrom": {"type": "integer", "format": "int64"}, "interactTimeTo": {"type": "integer", "format": "int64"}, "interactType": {"type": "string", "enum": ["ImportCreator", "Invite", "SendMsg", "SyncMsg", "UpdateInfo"]}, "shopId": {"type": "integer", "format": "int64"}}}, "HasInteractionVo": {"title": "HasInteractionVo", "type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "handle": {"type": "string"}}}, "HealthCheckConfig": {"title": "HealthCheckConfig", "type": "object", "properties": {"advanceSettings": {"type": "object", "description": "其它的流程关心但引擎不关心的属性"}, "disabled": {"type": "boolean"}, "duration": {"type": "string", "description": "健康检查工作时间段，格式如 03:22-15:34"}, "friendType": {"type": "string", "description": "first | random | assign  为空表示random"}, "interval": {"type": "integer", "description": "健康检查频率，为空表示不检查；最小10分钟，最大1000分钟", "format": "int32"}, "scheduleId": {"type": "string", "description": "调度quartz id"}}}, "IdNameVo": {"title": "IdNameVo", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "JobCreatingInfo": {"title": "JobCreatingInfo", "type": "object", "properties": {"currentAliveCount": {"type": "integer", "description": "任务池当前已有任务", "format": "int32"}, "maxAliveJobsCount": {"type": "integer", "description": "任务池可容纳的最大任务数量", "format": "int32"}, "plannedJobsCount": {"type": "integer", "description": "本次操作拟创建任务", "format": "int32"}, "reallyJobsCount": {"type": "integer", "description": "实际创建任务", "format": "int32"}, "status": {"type": "string", "description": "当前状态 creating | done "}}}, "KolAvailableVo": {"title": "KolAvailableVo", "type": "object", "properties": {"creatorId": {"type": "string"}, "handle": {"type": "string"}, "region": {"type": "string"}}}, "KolHandleRegionVo": {"title": "KolHandleRegionVo", "type": "object", "properties": {"handle": {"type": "string"}, "region": {"type": "string"}}}, "KolLiveDto": {"title": "KolLiveDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "gameRank": {"type": "number", "format": "double"}, "handle": {"type": "string"}, "hourRevenue": {"type": "number", "format": "double"}, "id": {"type": "integer", "format": "int64"}, "likes": {"type": "integer", "format": "int32"}, "liveId": {"type": "string"}, "popularRank": {"type": "number", "format": "double"}, "region": {"type": "string"}, "revenue": {"type": "number", "format": "double"}, "viewers": {"type": "integer", "format": "int32"}, "weekRevenue": {"type": "number", "format": "double"}}}, "KolRegionMapDto": {"title": "KolRegionMapDto", "type": "object", "properties": {"id": {"type": "string"}, "region": {"type": "string"}}}, "KolSubTeamDetailVo": {"title": "KolSubTeamDetailVo", "type": "object", "properties": {"allocateTotal": {"type": "integer", "description": "每日采集主播数量", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "parentTeamId": {"type": "integer", "format": "int64"}, "subTeam": {"$ref": "#/components/schemas/TeamDto"}, "subTeamAdmin": {"$ref": "#/components/schemas/UserBriefVo"}, "subTeamId": {"type": "integer", "format": "int64"}, "userQuota": {"type": "integer", "description": "用户数量", "format": "int32"}, "valid": {"type": "boolean"}, "validTimeFrom": {"type": "string", "format": "date-time"}, "validTimeTo": {"type": "string", "format": "date-time"}}}, "KolSubTeamDto": {"title": "KolSubTeamDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "parentTeamId": {"type": "integer", "format": "int64"}, "subTeamId": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}, "validTimeFrom": {"type": "string", "format": "date-time"}, "validTimeTo": {"type": "string", "format": "date-time"}}}, "KolSubTeamVo": {"title": "KolSubTeamVo", "type": "object", "properties": {"allocateTotal": {"type": "integer", "description": "每日采集主播数量", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "parentTeamId": {"type": "integer", "format": "int64"}, "subTeamId": {"type": "integer", "format": "int64"}, "userQuota": {"type": "integer", "description": "用户数量", "format": "int32"}, "valid": {"type": "boolean"}, "validTimeFrom": {"type": "string", "format": "date-time"}, "validTimeTo": {"type": "string", "format": "date-time"}}}, "KolTagRequest": {"title": "KolTagRequest", "type": "object", "properties": {"creators": {"type": "array", "items": {"$ref": "#/components/schemas/HandleRegionVo"}}, "tag": {"type": "string"}}}, "KolUntagBySuffixRequest": {"title": "KolUntagBySuffixRequest", "type": "object", "properties": {"creators": {"type": "array", "items": {"$ref": "#/components/schemas/HandleRegionVo"}}, "tagSuffixList": {"type": "array", "items": {"type": "string"}}}}, "KolUpdateSubTeamRequest": {"title": "KolUpdateSubTeamRequest", "type": "object", "properties": {"allocateTotal": {"type": "integer", "description": "每日采集主播数量", "format": "int32"}, "userQuota": {"type": "integer", "description": "用户数量", "format": "int32"}, "validTimeFrom": {"type": "string", "format": "date-time"}, "validTimeTo": {"type": "string", "format": "date-time"}}}, "KolUserInfo": {"title": "KolUserInfo", "type": "object", "properties": {"creator": {"$ref": "#/components/schemas/GhCreatorInterface"}, "scene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}}}, "LoginDeviceDto": {"title": "LoginDeviceDto", "type": "object", "properties": {"appId": {"type": "string"}, "appVersion": {"type": "string"}, "clientIp": {"type": "string"}, "clientLocation": {"type": "integer", "format": "int64"}, "cpus": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "deviceId": {"type": "string"}, "deviceType": {"type": "string", "enum": ["App", "Browser", "Extension", "HYRuntime", "RpaExecutor"]}, "domestic": {"type": "boolean"}, "hostName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "ipDataId": {"type": "integer", "format": "int64"}, "lastActiveTime": {"type": "string", "format": "date-time"}, "lastCity": {"type": "string"}, "lastLogTime": {"type": "string", "format": "date-time"}, "lastRemoteIp": {"type": "string"}, "lastUserId": {"type": "integer", "format": "int64"}, "logUrl": {"type": "string"}, "mem": {"type": "integer", "format": "int64"}, "online": {"type": "boolean"}, "osName": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "userAgent": {"type": "string"}, "version": {"type": "string"}}}, "MemberVo": {"title": "MemberVo", "type": "object", "properties": {"account": {"type": "string", "description": "账号"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "email": {"type": "string", "description": "邮箱"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "phone": {"type": "string", "description": "手机"}, "residentCity": {"type": "string"}, "roleCode": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "teamNickname": {"type": "string"}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}, "weixin": {"type": "string"}}}, "MobileChoicePolicy": {"title": "MobileChoicePolicy", "type": "object", "properties": {"choiceType": {"type": "string", "description": "使用的手机账号：reuse 使用最近使用的 | designated 指定手机 | reset 强制重新分配"}, "extraAccountTag": {"type": "string", "description": "手机账号分配原则：除了发送私信之外的标签，为空说明只需要发送私信标签"}, "mobileAccountId": {"type": "integer", "description": "指定的手机账号，当 choiceType=designated 时，具体使用哪一个手机账号", "format": "int64"}}}, "NotifyNewTkMessageRequest": {"title": "NotifyNewTkMessageRequest", "type": "object", "properties": {"accounts": {"type": "array", "items": {"type": "string"}}, "handles": {"type": "array", "items": {"type": "string"}}, "mobileId": {"type": "integer", "format": "int64"}, "tags": {"type": "array", "items": {"type": "string"}}}}, "NotifyScheduleVo": {"title": "NotifyScheduleVo", "type": "object", "properties": {"times": {"type": "array", "items": {"type": "string"}}, "userIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "PageGhSessionRequest": {"title": "PageGhSessionRequest", "type": "object", "properties": {"handle": {"type": "string"}, "lastPushTimeBeforeDays": {"type": "integer", "format": "int32"}, "lastPushTimeFrom": {"type": "string", "format": "date-time"}, "lastPushTimeLastDays": {"type": "integer", "format": "int32"}, "lastPushTimeTo": {"type": "string", "format": "date-time"}, "lastReplyTimeBeforeDays": {"type": "integer", "format": "int32"}, "lastReplyTimeFrom": {"type": "string", "format": "date-time"}, "lastReplyTimeLastDays": {"type": "integer", "format": "int32"}, "lastReplyTimeTo": {"type": "string", "format": "date-time"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "receiveUserId": {"type": "integer", "format": "int64"}, "replyUserId": {"type": "integer", "format": "int64"}, "sortField": {"type": "string"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "statusList": {"type": "array", "items": {"type": "string", "enum": ["Error", "NeedReply", "Replied", "Replying"]}}}}, "PageResult«GhCreatorDetailVo»": {"title": "PageResult«GhCreatorDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GhCreatorDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«GhGifterDetailVo»": {"title": "PageResult«GhGifterDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GhGifterDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«GhInteractionDetailVo»": {"title": "PageResult«GhInteractionDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GhInteractionDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«GhJobDto»": {"title": "PageResult«GhJobDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GhJobDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«GhLiveCreatorDetailVo»": {"title": "PageResult«GhLiveCreatorDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GhLiveCreatorDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«GhMessageDto»": {"title": "PageResult«GhMessageDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GhMessageDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«GhSessionVo»": {"title": "PageResult«GhSessionVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GhSessionVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«GhSpeechDto»": {"title": "PageResult«GhSpeechDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GhSpeechDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«GhUserDetailVo»": {"title": "PageResult«GhUserDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GhUserDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«GhVideoCreatorDetailVo»": {"title": "PageResult«GhVideoCreatorDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/GhVideoCreatorDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«KolLiveDto»": {"title": "PageResult«KolLiveDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/KolLiveDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«KolSubTeamDetailVo»": {"title": "PageResult«KolSubTeamDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/KolSubTeamDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«string»": {"title": "PageResult«string»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"type": "string"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "ReportGhJobRequest": {"title": "ReportGhJobRequest", "type": "object", "properties": {"jobId": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "rpaTaskId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "description": "汇报的时候允许指定状态。如果未指定将根据 success 来设置为成功或失败。 Succeed | Failed | Cancelled ", "enum": ["Cancelled", "Failed", "Pending", "Running", "Scheduled", "Succeed"]}, "success": {"type": "boolean"}, "unbindCreatorShop": {"type": "boolean", "description": "是否解绑达人和分身，目前只有在公会后台邀约建联时有意义", "example": false}}}, "Resource": {"title": "Resource", "type": "object"}, "RpaFlowDto": {"title": "RpaFlowDto", "type": "object", "properties": {"attachmentSize": {"type": "integer", "format": "int64"}, "bizCode": {"type": "string"}, "configId": {"type": "string"}, "console": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createType": {"type": "string", "enum": ["FileCopy", "Manual", "Market", "MarketCopy", "Shared", "TkPack", "Tkshop"]}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "dirty": {"type": "boolean"}, "execCount": {"type": "integer", "format": "int32"}, "expireTime": {"type": "string", "format": "date-time"}, "flowShareCode": {"type": "string"}, "groupId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "lastExecTime": {"type": "string", "format": "date-time"}, "marketId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "nameBrief": {"type": "string"}, "numberVersion": {"type": "integer", "format": "int32"}, "packId": {"type": "integer", "format": "int64"}, "publishTime": {"type": "string", "format": "date-time"}, "rpaType": {"type": "string", "enum": ["Browser", "Extension", "IOS", "Mobile"]}, "sessionInner": {"type": "boolean"}, "sharedFlowId": {"type": "integer", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "sortNo": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Draft", "Published"]}, "supportConcurrent": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "tkFlowId": {"type": "integer", "format": "int64"}, "updateTime": {"type": "string", "format": "date-time"}, "version": {"type": "string"}}}, "RpaTaskDto": {"title": "RpaTaskDto", "type": "object", "properties": {"clientId": {"type": "string"}, "clientIp": {"type": "string"}, "cloudInstanceId": {"type": "integer", "format": "int64"}, "concurrent": {"type": "integer", "format": "int32"}, "configId": {"type": "string"}, "console": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "creditDetailId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "deviceName": {"type": "string"}, "errorCode": {"type": "integer", "format": "int32"}, "errorMsg": {"type": "string"}, "executeEndTime": {"type": "string", "format": "date-time"}, "executeTime": {"type": "string", "format": "date-time"}, "extra": {"type": "string"}, "fileLocked": {"type": "boolean"}, "fileStatus": {"type": "string", "enum": ["Deleted", "Undefined", "<PERSON><PERSON>"]}, "flowId": {"type": "integer", "format": "int64"}, "flowName": {"type": "string"}, "forceRecord": {"type": "boolean"}, "heartbeatTime": {"type": "string", "format": "date-time"}, "hyRuntimeId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "manualRun": {"type": "boolean"}, "name": {"type": "string"}, "planId": {"type": "integer", "format": "int64"}, "planName": {"type": "string"}, "preview": {"type": "boolean"}, "provider": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "region": {"type": "string"}, "rpaVoucherId": {"type": "integer", "format": "int64"}, "runOnCloud": {"type": "boolean"}, "snapshot": {"type": "string", "enum": ["Node", "Not", "OnFail"]}, "status": {"type": "string", "enum": ["Cancelled", "CreateFailed", "Ended", "Ended_All_Failed", "Ended_Partial_Failed", "Ignored", "NotStart", "Running", "ScheduleCancelled", "Scheduled", "Scheduling", "UnusualEnded"]}, "teamId": {"type": "integer", "format": "int64"}}}, "SendInvitePMRequest": {"title": "SendInvitePMRequest", "type": "object", "properties": {"advanceSettings": {"type": "object", "description": "邀约高级设置"}, "allowedStartTime": {"type": "string", "description": "允许开始执行的时间", "format": "date-time"}, "bizScene": {"type": "string", "description": "是什么样类型的达人，为空表示 LiveCreator ", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "commentWordIds": {"type": "array", "description": "邀约时评论要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义", "items": {"type": "integer", "format": "int64"}}, "creatorIds": {"type": "array", "description": "拟发送的达人列表", "items": {"type": "integer", "format": "int64"}}, "deleteMsg": {"type": "boolean"}, "ghPlatformType": {"type": "string", "description": "公会平台类型", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "ghScheduleType": {"type": "string", "description": "拟使用的TK账号类型，默认值 Normal", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "mobileChoicePolicy": {"description": "发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义", "$ref": "#/components/schemas/MobileChoicePolicy"}, "sendComment": {"type": "boolean", "description": "发送私信前关注主播并将私信内容评论到主播的第一条视频", "example": false}, "sendEmoji": {"type": "boolean", "description": "是否增加随机表情", "example": false}, "sendInviteCard": {"type": "boolean", "description": "是否发送邀约卡片(只有公会后台账号才支持此特性）", "example": false}, "shopChoicePolicy": {"description": "发送邀约分身选择策略", "$ref": "#/components/schemas/ShopChoicePolicy"}, "skipResponsible": {"type": "boolean", "description": "已分配/已认领的达人不再发送 (为空表示 true )", "example": false}, "wordsIds": {"type": "array", "description": "邀约要使用的话术列表，会随机从中选择发送", "items": {"type": "integer", "format": "int64"}}}}, "SendKakaoMessageRequest": {"title": "SendKakaoMessageRequest", "type": "object", "properties": {"chatId": {"type": "integer", "description": "给指定的kakao聊天记录发消息，只有当#friendId为空时才有意义", "format": "int64"}, "content": {"type": "string"}, "friendId": {"type": "integer", "description": "给指定的kakao联系人发消息", "format": "int64"}}}, "SendReplyPMRequest": {"title": "SendReplyPMRequest", "type": "object", "properties": {"advanceSettings": {"type": "object", "description": "邀约高级设置"}, "allowedStartTime": {"type": "string", "description": "允许开始执行的时间", "format": "date-time"}, "bizScene": {"type": "string", "description": "是什么样类型的达人，为空表示 LiveCreator ", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "content": {"type": "string", "description": "自定义输入内容，如果不为空，wordIds会被忽略"}, "ghCreatorId": {"type": "integer", "format": "int64"}, "ghPlatformType": {"type": "string", "description": "公会平台类型", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "ghScheduleType": {"type": "string", "description": "拟使用的TK账号类型，默认值 Normal", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "mobileChoicePolicy": {"description": "发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义", "$ref": "#/components/schemas/MobileChoicePolicy"}, "wordsIds": {"type": "array", "description": "邀约要使用的话术列表，会随机从中选择发送", "items": {"type": "integer", "format": "int64"}}}}, "SendUserCardRequest": {"title": "SendUserCardRequest", "type": "object", "properties": {"advanceSettings": {"type": "object", "description": "发送卡片高级设置"}, "allowedStartTime": {"type": "string", "description": "允许开始执行的时间", "format": "date-time"}, "bizScene": {"type": "string", "description": "是什么样类型的达人，为空表示 User ", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "creatorIds": {"type": "array", "description": "拟发送的达人列表", "items": {"type": "integer", "format": "int64"}}, "ghPlatformType": {"type": "string", "description": "公会平台类型", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "mobileAccountId": {"type": "integer", "format": "int64"}, "receiverHandle": {"type": "string", "description": "接收者handle，会原样转给流程"}}}, "ShareVideoRequest": {"title": "ShareVideoRequest", "type": "object", "properties": {"advanceSettings": {"type": "object", "description": "邀约高级设置"}, "allowedStartTime": {"type": "string", "description": "允许开始执行的时间", "format": "date-time"}, "bizScene": {"type": "string", "description": "是什么样类型的达人，为空表示 LiveCreator ", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "creatorIds": {"type": "array", "description": "拟发送的达人列表", "items": {"type": "integer", "format": "int64"}}, "ghPlatformType": {"type": "string", "description": "公会平台类型", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "ghScheduleType": {"type": "string", "description": "拟使用的TK账号类型，默认值 Normal", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "mobileChoicePolicy": {"description": "发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义", "$ref": "#/components/schemas/MobileChoicePolicy"}, "wordsIds": {"type": "array", "description": "邀约要使用的话术列表，会随机从中选择发送", "items": {"type": "integer", "format": "int64"}}}}, "ShopChoicePolicy": {"title": "ShopChoicePolicy", "type": "object", "properties": {"shopId": {"type": "integer", "description": "如果达人没有分配过对应类型的分身，shopId不为空表示强制使用该分身，否则从打了相应tag的分身中随机选择", "format": "int64"}}}, "ShopDto": {"title": "ShopDto", "type": "object", "properties": {"account": {"type": "string", "description": "账户账号"}, "allowExtension": {"type": "boolean", "description": "是否允许用户自行安装插件", "example": false}, "allowMonitor": {"type": "boolean", "description": "会话是否允许监视", "example": false}, "allowSkip": {"type": "boolean", "description": "是否允许跳过敏感操作", "example": false}, "autoFill": {"type": "boolean", "description": "是否自动代填", "example": false}, "bookmarkBar": {"type": "string"}, "coordinateId": {"type": "string"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "description": {"type": "string", "description": "描述"}, "domainPolicy": {"type": "string", "enum": ["Blacklist", "None", "Whitelist"]}, "exclusive": {"type": "boolean", "description": "是否独占访问", "example": false}, "extension": {"type": "string", "enum": ["both", "extension", "huayang", "origin_browser"]}, "extraProp": {"type": "string"}, "fingerprintId": {"type": "integer", "description": "绑定的指纹Id", "format": "int64"}, "fingerprintTemplateId": {"type": "integer", "description": "绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板）", "format": "int64"}, "frontUrl": {"type": "string"}, "googleTranslateSpeed": {"type": "boolean"}, "homePage": {"type": "string"}, "homePageSites": {"type": "string"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "imageForbiddenSize": {"type": "integer", "format": "int64"}, "intranetEnabled": {"type": "boolean"}, "ipSwitchCheckInterval": {"type": "integer", "format": "int32"}, "ipSwitchStrategy": {"type": "string", "enum": ["Abort", "<PERSON><PERSON>", "Off"]}, "lastAccessTime": {"type": "string", "format": "date-time"}, "lastAccessUser": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "loginStatus": {"type": "string", "enum": ["Offline", "Online", "Unknown"]}, "markCode": {"type": "string", "description": "任务栏分身标记文字"}, "markCodeBg": {"type": "integer", "description": "任务栏分身标记背景颜色", "format": "int32"}, "monitorPerception": {"type": "boolean"}, "name": {"type": "string", "description": "账户名称"}, "nameBookmarkEnabled": {"type": "boolean"}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "operatingCategory": {"type": "string", "description": "经营品类", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "password": {"type": "string", "description": "密码"}, "platformId": {"type": "integer", "description": "平台ID", "format": "int64"}, "privateAddress": {"type": "string"}, "privateTitle": {"type": "string"}, "recordPerception": {"type": "boolean"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "requireIp": {"type": "boolean"}, "resourcePolicy": {"type": "integer", "format": "int32"}, "securityPolicyEnabled": {"type": "boolean"}, "securityPolicyUpdateTime": {"type": "string", "format": "date-time"}, "sharePolicyId": {"type": "string"}, "shopDataSize": {"type": "integer", "format": "int64"}, "shopNo": {"type": "string"}, "stateless": {"type": "boolean"}, "statelessChangeFp": {"type": "boolean"}, "statelessSyncPolicy": {"type": "integer", "format": "int32"}, "syncPolicy": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "trafficAlertStrategy": {"type": "string", "enum": ["Abort", "Off"]}, "trafficAlertThreshold": {"type": "integer", "format": "int32"}, "trafficSaving": {"type": "boolean"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}, "webSecurity": {"type": "boolean"}}}, "SimpleGhCreatorVo": {"title": "SimpleGhCreatorVo", "type": "object", "properties": {"alias": {"type": "string"}, "handle": {"type": "string"}, "id": {"type": "integer", "format": "int64"}}}, "SimpleShopVo": {"title": "SimpleShopVo", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "StartWatchLiveRewardsRequest": {"title": "StartWatchLiveRewardsRequest", "type": "object", "properties": {"advanceSettings": {"type": "object", "description": "其它的流程关心但引擎不关心的属性。引擎会关注 (minViewers: number = 10, maxViewers: number = 20) 两个参数"}, "ghPlatformType": {"type": "string", "description": "公会平台类型", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "ghScheduleType": {"type": "string", "description": "区分是浏览器还是手机，非Mobile一律认为是浏览器", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "rewardTag": {"type": "string", "description": "如果未分配打赏的店铺/手机账号，通过这个标签去查找"}, "rewardedPolicy": {"type": "string", "description": "如果已经打赏过的策略", "enum": ["change", "ignore", "reuse"]}, "wordsIds": {"type": "array", "description": "直播间发言话术，会随机从中选择发送", "items": {"type": "integer", "format": "int64"}}}}, "SyncBatchResult": {"title": "SyncBatchResult", "type": "object", "properties": {"created": {"type": "integer", "description": "新创建个数", "format": "int32"}, "total": {"type": "integer", "description": "总数", "format": "int32"}, "updated": {"type": "integer", "description": "更新个数", "format": "int32"}}}, "SyncCreatorAvatarRequest": {"title": "SyncCreatorAvatarRequest", "type": "object", "properties": {"avatarImageBase64": {"type": "string", "description": "头像数据base64编码"}, "avatarImageExt": {"type": "string", "description": "头像扩展名"}, "creatorId": {"type": "string"}, "handle": {"type": "string"}, "region": {"type": "string"}}}, "SyncCreatorConfig": {"title": "SyncCreatorConfig", "type": "object", "properties": {"maxShopTaskSync": {"type": "integer", "description": "单分身每次最多更新多少个主播", "format": "int32"}, "shopTag": {"type": "string", "description": "信息更新账号所用标签"}}}, "SyncGhLiveGiftRequest": {"title": "SyncGhLiveGiftRequest", "type": "object", "properties": {"liveGifts": {"type": "array", "items": {"$ref": "#/components/schemas/GhLiveGiftDocument"}}}}, "SyncKaokaoContactsToPhoneRequest": {"title": "SyncKaokaoContactsToPhoneRequest", "type": "object", "properties": {"friendIds": {"type": "array", "description": "要同步到手机的联系人列表", "items": {"type": "integer", "format": "int64"}}}}, "SyncMessageResult": {"title": "SyncMessageResult", "type": "object", "properties": {"newMessageCount": {"type": "integer", "description": "新消息数量", "format": "int32"}, "tag": {"type": "string", "description": "新打的标签"}}}, "SyncPMRequest": {"title": "SyncPMRequest", "type": "object", "properties": {"bizScene": {"type": "string", "description": "是什么样类型的达人，为空表示 LiveCreator ", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "creatorIds": {"type": "array", "description": "打算更新PM信息的达人列表", "items": {"type": "integer", "format": "int64"}}, "deleteSession": {"type": "boolean", "description": "同步时是否同步删除与主播的聊天记录，默认为true", "example": false}, "ghPlatformType": {"type": "string", "description": "公会平台类型", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "ghScheduleType": {"type": "string", "description": "拟使用的TK账号类型，默认值 Normal", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}}}, "TagCreatorRequest": {"title": "TagCreatorRequest", "type": "object", "properties": {"handles": {"type": "array", "items": {"type": "string"}}, "tags": {"type": "array", "items": {"type": "string"}}}}, "TagDto": {"title": "TagDto", "type": "object", "properties": {"bizCode": {"type": "string"}, "color": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TkshopSr", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "system": {"type": "boolean"}, "tag": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "TagTkCreatorByQueryRequest": {"title": "TagTkCreatorByQueryRequest", "type": "object", "properties": {"query": {"type": "object"}, "scene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "tag": {"type": "boolean", "description": "打标签或清空标签", "example": false}, "tagIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "TeamDto": {"title": "TeamDto", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "domesticCloudEnabled": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invalidTime": {"type": "string", "format": "date-time"}, "inviteCode": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "overseaCloudEnabled": {"type": "boolean"}, "paid": {"type": "boolean"}, "partnerId": {"type": "integer", "format": "int64"}, "payTime": {"type": "string", "format": "date-time"}, "repurchaseTime": {"type": "string", "format": "date-time"}, "repurchased": {"type": "boolean"}, "status": {"type": "string", "enum": ["Blocked", "Deleted", "Pending", "Ready"]}, "teamType": {"type": "string", "enum": ["crs", "gh", "krShop", "normal", "partner", "plugin", "tk", "tkshop"]}, "tenantId": {"type": "integer", "format": "int64"}, "testing": {"type": "boolean"}, "validateTime": {"type": "string", "format": "date-time"}, "validated": {"type": "boolean"}, "verified": {"type": "boolean"}}}, "TeamKolConfig": {"title": "TeamKolConfig", "type": "object", "properties": {"agents": {"type": "integer", "format": "int32"}, "allocateInterval": {"type": "integer", "format": "int32"}, "allocateRevenue": {"type": "string", "description": "流水过滤字段", "enum": ["max_hour_revenue", "max_revenue", "max_week_revenue", "revenue"]}, "allocateTotal": {"type": "integer", "format": "int32"}, "allowUserSetKeepDays": {"type": "boolean", "description": "允许用户设置主播缓存时长", "example": false}, "area": {"type": "string"}, "commerceUser": {"type": "boolean"}, "countries": {"type": "array", "items": {"type": "string"}}, "creatorTypes": {"type": "array", "items": {"type": "string"}}, "deleteRemarks": {"type": "array", "items": {"type": "string"}}, "filterTotal": {"type": "integer", "format": "int32"}, "invalidJobId": {"type": "string"}, "invalidateTime": {"type": "string", "format": "date-time"}, "keepDays": {"type": "integer", "format": "int32"}, "keepStatusList": {"type": "array", "items": {"type": "string", "enum": ["NotContacted", "Replied", "<PERSON><PERSON>", "SignUnsupported", "Signed"]}}, "keepTagDays": {"type": "integer", "description": "标签保留时长", "format": "int32"}, "liveHours": {"type": "integer", "format": "int32"}, "maxAliveJobsCount": {"type": "integer", "format": "int32"}, "maxFollowerCnt": {"type": "integer", "format": "int32"}, "maxRevenue": {"type": "integer", "format": "int32"}, "minFollowerCnt": {"type": "integer", "format": "int32"}, "minRevenue": {"type": "integer", "format": "int32"}, "platformModules": {"type": "array", "items": {"type": "string"}}, "platformTypes": {"type": "array", "items": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, "regionSwitchQuota": {"type": "integer", "format": "int32"}, "remarks": {"type": "string"}, "subTeamCount": {"type": "integer", "format": "int32"}, "ttSeller": {"type": "boolean"}, "validEndTime": {"type": "string", "format": "date-time"}, "xhsConfig": {"$ref": "#/components/schemas/XiaohongshuConfig"}}}, "TeamMobileVo": {"title": "TeamMobileVo", "type": "object", "properties": {"allowBackup": {"type": "boolean", "description": "当前手机是否允许使用快照功能", "example": false}, "androidVersion": {"type": "string", "description": "系统版本，由于历史原因取名叫androidVersion "}, "cloudInfo": {"description": "当手机为云手机时才有值", "$ref": "#/components/schemas/CloudMobileInfo"}, "code": {"type": "string", "description": "adb devices -l 获取到的设备标识"}, "connectType": {"type": "string", "enum": ["ARMCLOUD", "Baidu", "QCloud", "USB", "WIFI"]}, "createTime": {"type": "string", "description": "导入时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者id", "format": "int64"}, "description": {"type": "string"}, "deviceId": {"type": "string", "description": "所在设备的id，关联login_device.device_id"}, "deviceOnline": {"type": "boolean", "description": "所在设备的当前是否在线", "example": false}, "grantDepartmentList": {"type": "array", "description": "授权的部门", "items": {"$ref": "#/components/schemas/DepartmentDto"}}, "grantUserVoList": {"type": "array", "description": "授权给的用户", "items": {"$ref": "#/components/schemas/MemberVo"}}, "healthCheckConfig": {"description": "手机账号可用性检查配置", "$ref": "#/components/schemas/HealthCheckConfig"}, "id": {"type": "integer", "format": "int64"}, "intervalConfig": {"description": "手机邀约配置，如设置每手机每批次间隔", "$ref": "#/components/schemas/手机邀约配置"}, "messageCheckConfig": {"description": "手机新消息检查配置", "$ref": "#/components/schemas/手机消息检查配置"}, "metas": {"type": "object", "description": "存手机当前的geo, sim等信息"}, "mode": {"type": "string", "description": "adb shell getprop ro.product.model"}, "name": {"type": "string", "description": "手机设备名字，例如 MI 14"}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "parentMobileId": {"type": "integer", "description": "从哪台手机联营而来", "format": "int64"}, "platform": {"type": "string", "description": "android | ios", "enum": ["Android", "IOS"]}, "rpaSilent": {"type": "boolean", "description": "是否暂时禁用rpa执行", "example": false}, "screenHeight": {"type": "integer", "format": "int32"}, "screenWidth": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["EXPIRED", "OFFLINE", "ONLINE", "RESETING"]}, "tags": {"type": "array", "description": "标签，只有查询的时候声明 fetchTags 才会有", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "description": "所属团队", "format": "int64"}, "workTimeConfig": {"$ref": "#/components/schemas/WorkTimeConfig"}}}, "TkCreatorByQueryRequest": {"title": "TkCreatorByQueryRequest", "type": "object", "properties": {"query": {"type": "object"}, "scene": {"type": "string", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "TkCreatorFiledVo": {"title": "TkCreatorFiledVo", "type": "object", "properties": {"desc": {"type": "string", "description": "字段描述（可能为null）"}, "filedType": {"type": "string", "enum": ["CreatorColumn", "CreatorContact", "CreatorProp", "CreatorStat", "Other", "ProductColumn"]}, "force": {"type": "boolean", "description": "方案必须包含", "example": false}, "key": {"type": "string", "description": "字段Key"}, "label": {"type": "string", "description": "字段名称"}, "path": {"type": "string"}}}, "TkInteractionDetailVo": {"title": "TkInteractionDetailVo", "type": "object", "properties": {"creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "extraInfo": {"type": "object"}, "flowId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "interactTime": {"type": "integer", "format": "int64"}, "interactType": {"type": "string", "enum": ["Cha<PERSON>", "Email", "FetchCreator", "Im<PERSON><PERSON>", "ImportCreator", "Ranking", "SampleRequest", "SampleRequestApproved", "TargetPlan", "UpdateCreator"]}, "interactionId": {"type": "string"}, "operator": {"$ref": "#/components/schemas/UserDto"}, "operatorId": {"type": "integer", "format": "int64"}, "rpaFlow": {"$ref": "#/components/schemas/RpaFlowDto"}, "rpaTask": {"$ref": "#/components/schemas/RpaTaskDto"}, "shop": {"$ref": "#/components/schemas/ShopDto"}, "shopId": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}, "taskId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "UpdateGhCreatorInfoRequest": {"title": "UpdateGhCreatorInfoRequest", "type": "object", "properties": {"bizScene": {"type": "string", "description": "是什么样类型的达人，为空表示 LiveCreator ", "enum": ["General", "Gifter", "InsUser", "LiveCreator", "Shop<PERSON>uyer", "ShopCreator", "User", "VideoCreator"]}, "creatorIds": {"type": "array", "description": "拟更新信息的达人列表", "items": {"type": "integer", "format": "int64"}}, "ghPlatformType": {"type": "string", "description": "公会平台类型", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}}, "UpdateGhJobPlanRequest": {"title": "UpdateGhJobPlanRequest", "type": "object", "properties": {"commentWordIds": {"type": "array", "description": "邀约时评论要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义", "items": {"type": "integer", "format": "int64"}}, "creatorFilter": {"type": "string", "description": "查询条件，由 /api/gh/creator/page 这个接口的params JSON.stringify(params) 而来"}, "cronExpression": {"type": "string", "description": "重复日期，只包含星期信息的表达式(具体参考自动流程计划)"}, "deleteMsg": {"type": "boolean"}, "deleteSession": {"type": "boolean", "description": "同步时是否删除与主播的聊天记录，默认为true。只在 ghJobType 为 SyncPm 时有意义", "example": false}, "expressions": {"type": "array", "description": "如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...]", "items": {"type": "string"}}, "ghJobType": {"type": "string", "enum": ["Acco<PERSON><PERSON><PERSON><PERSON>", "AccountHealthCheck", "AccountMaintenance", "AccountMessageCheck", "KOL_LiveRewards", "<PERSON><PERSON><PERSON><PERSON>", "SendInvite", "SendPm", "SendUserCard", "ShareVideo", "<PERSON><PERSON><PERSON>", "SyncContactToPhone", "SyncContacts", "SyncCreator", "SyncLiveStream", "SyncPm", "TS_AddContacts", "TS_AddFriends", "TS_DemandPayment", "TS_IMChatByFilter", "TS_IMChatByHandle", "TS_LoginCheck", "TS_SampleApprove", "TS_SendBuyerIMChat", "TS_SendEmail", "TS_SendFacebook", "TS_SendLine", "TS_SendWhatsApp", "TS_SendZalo", "TS_SyncBuyerInfo", "TS_SyncCreator", "TS_SyncOrders", "TS_SyncProducts", "TS_SyncSampleCreator", "TS_SyncShopInfo", "TS_SyncVideos", "TS_TargetPlanByFilter", "TS_TargetPlanByHandle", "TS_TargetPlanClear", "TS_TargetPlanModify", "TS_VideoADCode"]}, "ghScheduleType": {"type": "string", "description": "拟使用的TK账号类型，默认值 Normal。只在 ghJobType 为 SendInvite 时有意义", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "id": {"type": "integer", "format": "int64"}, "inviteAdvanceSettings": {"type": "object", "description": "私信高级设置，只在 ghJobType 为 SendInvite/AccountMaintenance 时有意义"}, "inviteMobileChoicePolicy": {"description": "发送邀约手机账号选择策略，仅在 ghScheduleType == Mobile 的时候有意义", "$ref": "#/components/schemas/MobileChoicePolicy"}, "inviteShopChoicePolicy": {"description": "发送邀约分身选择策略，仅在 ghScheduleType != Mobile 的时候有意义", "$ref": "#/components/schemas/ShopChoicePolicy"}, "maxMinutes": {"type": "integer", "description": "最多允许执行多少分钟(需求方要求是流程自己实现，所以应该只有部分流程支持这个参数)", "format": "int32"}, "mobileAccountIds": {"type": "array", "description": "相应的手机账号id，只有在 ghJobType 为 AccountMaintenance 时有意义", "items": {"type": "integer", "format": "int64"}}, "name": {"type": "string"}, "sendComment": {"type": "boolean", "description": "发送私信前关注主播并将私信内容评论到主播的第一条视频", "example": false}, "sendEmoji": {"type": "boolean", "description": "是否增加随机表情", "example": false}, "sendInviteCard": {"type": "boolean", "description": "是否发送邀约卡片，只有发送邀约建联计划才有意义(只有公会后台账号才支持此特性）", "example": false}, "skipResponsible": {"type": "boolean", "description": "已分配/已认领的达人不再发送 (为空表示 true )，只对发送邀约建联的计划有意义", "example": false}, "wordsIds": {"type": "array", "description": "邀约要使用的话术列表，会随机从中选择发送。只在 ghJobType 为 SendInvite 时有意义", "items": {"type": "integer", "format": "int64"}}}}, "UpdateGhSessionStatusRequest": {"title": "UpdateGhSessionStatusRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "status": {"type": "string", "enum": ["Error", "NeedReply", "Replied", "Replying"]}}}, "UpdateScheduleConfigRequest": {"title": "UpdateScheduleConfigRequest", "type": "object", "properties": {"blockTags": {"type": "object", "additionalProperties": {"type": "string"}, "description": "有哪些表示账号已经封禁的标签，目前key的取值: account_logged_out | account_banned | account_limited | pm_too_fast | need_config_privacy"}, "concurrent": {"type": "integer", "description": "发送时最高并发数，为空或者为<=0都表示不限制 (目前只在公会后台账号邀约建联时有意义)", "format": "int32"}, "ghPlatformType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "ghScheduleType": {"type": "string", "enum": ["GhBackstage", "Mobile", "Normal", "Official", "Union", "Unknown"]}, "inviteTimeFrame": {"description": "允许邀请的时间", "$ref": "#/components/schemas/AllowTimeConfig"}, "maxShopDayPm": {"type": "integer", "description": "单账号每日最多私信条数", "format": "int32"}, "maxShopTaskPm": {"type": "integer", "description": "单账号每批次最多私信条数", "format": "int32"}, "shopCount": {"type": "integer", "description": "打上了这个标签的分身的个数", "format": "int32"}, "shopTag": {"type": "string", "description": "发送私信的分身的标签"}, "shopTaskPmInterval": {"type": "integer", "description": "单账号每批私信间隔，单位分钟", "format": "int32"}}}, "UpdateSyncCreatorConfigRequest": {"title": "UpdateSyncCreatorConfigRequest", "type": "object", "properties": {"ghPlatformType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}, "maxShopTaskSync": {"type": "integer", "description": "单分身每次最多更新多少个主播", "format": "int32"}, "shopTag": {"type": "string", "description": "信息更新账号所用标签"}}}, "UserBriefVo": {"title": "UserBriefVo", "type": "object", "properties": {"avatar": {"type": "string"}, "nickname": {"type": "string"}}}, "UserDto": {"title": "UserDto", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "residentCity": {"type": "string"}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«FilterStatInfo»": {"title": "WebResult«FilterStatInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/FilterStatInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhCreatorDetailVo»": {"title": "WebResult«GhCreatorDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhCreatorDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhCreatorExpiredAutoTags»": {"title": "WebResult«GhCreatorExpiredAutoTags»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhCreatorExpiredAutoTags"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhGeneralSettingsVo»": {"title": "WebResult«GhGeneralSettingsVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhGeneralSettingsVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhGifterDetailVo»": {"title": "WebResult«GhGifterDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhGifterDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhInteractionDetailVo»": {"title": "WebResult«GhInteractionDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhInteractionDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhInteractionDto»": {"title": "WebResult«GhInteractionDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhInteractionDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhJobDetailVo»": {"title": "WebResult«GhJobDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhJobDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhJobDeviceVo»": {"title": "WebResult«GhJobDeviceVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhJobDeviceVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhJobDto»": {"title": "WebResult«GhJobDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhJobDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhJobPlanVo»": {"title": "WebResult«GhJobPlanVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhJobPlanVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhShopStatistics»": {"title": "WebResult«GhShopStatistics»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhShopStatistics"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhSpeechDto»": {"title": "WebResult«GhSpeechDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhSpeechDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhSpeechGroupDto»": {"title": "WebResult«GhSpeechGroupDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhSpeechGroupDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhUserDetailVo»": {"title": "WebResult«GhUserDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhUserDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GhVideoCreatorDetailVo»": {"title": "WebResult«GhVideoCreatorDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GhVideoCreatorDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«JobCreatingInfo»": {"title": "WebResult«JobCreatingInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/JobCreatingInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«KolLiveDto»": {"title": "WebResult«KolLiveDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/KolLiveDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«KolSubTeamDto»": {"title": "WebResult«KolSubTeamDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/KolSubTeamDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«KolUserInfo»": {"title": "WebResult«KolUserInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/KolUserInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«CreatorBriefVo»»": {"title": "WebResult«List«CreatorBriefVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CreatorBriefVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GhAvailableVo»»": {"title": "WebResult«List«GhAvailableVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GhAvailableVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GhCreatorDto»»": {"title": "WebResult«List«GhCreatorDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GhCreatorDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GhCreatorPerformanceStatVo»»": {"title": "WebResult«List«GhCreatorPerformanceStatVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GhCreatorPerformanceStatVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GhCreatorStatusVo»»": {"title": "WebResult«List«GhCreatorStatusVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GhCreatorStatusVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GhGifterDto»»": {"title": "WebResult«List«GhGifterDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GhGifterDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GhJobDeviceVo»»": {"title": "WebResult«List«GhJobDeviceVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GhJobDeviceVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GhJobPlanVo»»": {"title": "WebResult«List«GhJobPlanVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GhJobPlanVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GhLiveGiftDto»»": {"title": "WebResult«List«GhLiveGiftDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GhLiveGiftDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GhSpeechGroupVo»»": {"title": "WebResult«List«GhSpeechGroupVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GhSpeechGroupVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GhStatRowVo»»": {"title": "WebResult«List«GhStatRowVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GhStatRowVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GhUserDto»»": {"title": "WebResult«List«GhUserDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GhUserDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«HandleCreatorIdVo»»": {"title": "WebResult«List«HandleCreatorIdVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/HandleCreatorIdVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«HasInteractionVo»»": {"title": "WebResult«List«HasInteractionVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/HasInteractionVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«IdNameVo»»": {"title": "WebResult«List«IdNameVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/IdNameVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«KolAvailableVo»»": {"title": "WebResult«List«KolAvailableVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/KolAvailableVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«KolRegionMapDto»»": {"title": "WebResult«List«KolRegionMapDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/KolRegionMapDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TagDto»»": {"title": "WebResult«List«TagDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TagDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkCreatorFiledVo»»": {"title": "WebResult«List«TkCreatorFiledVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkCreatorFiledVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«long»»": {"title": "WebResult«List«long»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«string»»": {"title": "WebResult«List«string»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "string"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«string,string»»": {"title": "WebResult«Map«string,string»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "string"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map»": {"title": "WebResult«Map»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«NotifyScheduleVo»": {"title": "WebResult«NotifyScheduleVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/NotifyScheduleVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«GhCreatorDetailVo»»": {"title": "WebResult«PageResult«GhCreatorDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«GhCreatorDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«GhGifterDetailVo»»": {"title": "WebResult«PageResult«GhGifterDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«GhGifterDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«GhInteractionDetailVo»»": {"title": "WebResult«PageResult«GhInteractionDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«GhInteractionDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«GhJobDto»»": {"title": "WebResult«PageResult«GhJobDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«GhJobDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«GhLiveCreatorDetailVo»»": {"title": "WebResult«PageResult«GhLiveCreatorDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«GhLiveCreatorDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«GhMessageDto»»": {"title": "WebResult«PageResult«GhMessageDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«GhMessageDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«GhSessionVo»»": {"title": "WebResult«PageResult«GhSessionVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«GhSessionVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«GhSpeechDto»»": {"title": "WebResult«PageResult«GhSpeechDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«GhSpeechDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«GhUserDetailVo»»": {"title": "WebResult«PageResult«GhUserDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«GhUserDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«GhVideoCreatorDetailVo»»": {"title": "WebResult«PageResult«GhVideoCreatorDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«GhVideoCreatorDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«KolLiveDto»»": {"title": "WebResult«PageResult«KolLiveDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«KolLiveDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«KolSubTeamDetailVo»»": {"title": "WebResult«PageResult«KolSubTeamDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«KolSubTeamDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«string»»": {"title": "WebResult«PageResult«string»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«string»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«SyncBatchResult»": {"title": "WebResult«SyncBatchResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/SyncBatchResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«SyncMessageResult»": {"title": "WebResult«SyncMessageResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/SyncMessageResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamKolConfig»": {"title": "WebResult«TeamKolConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamKolConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamMobileVo»": {"title": "WebResult«TeamMobileVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamMobileVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TkInteractionDetailVo»": {"title": "WebResult«TkInteractionDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TkInteractionDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«boolean»": {"title": "WebResult«boolean»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "boolean"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«int»": {"title": "WebResult«int»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«long»": {"title": "WebResult«long»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WorkTimeConfig": {"title": "WorkTimeConfig", "type": "object", "properties": {"FRI": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "MON": {"type": "array", "description": "每天的数组长度一定是24，分别表示每小时属于什么时间 0: 空闲， 1: 工作时间， 2: 养号时间", "items": {"type": "integer", "format": "int32"}}, "SAT": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "SUN": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "THU": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "TUE": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "WED": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "XiaohongshuConfig": {"title": "XiaohongshuConfig", "type": "object"}, "手机消息检查配置": {"title": "手机消息检查配置", "type": "object", "properties": {"disabled": {"type": "boolean"}, "duration": {"type": "string", "description": "消息检查工作时间段，格式如 03:22-15:34"}, "interval": {"type": "integer", "description": "消息同步频率，单位分钟，为空表示不同步；最小10分钟，最大1000分钟", "format": "int32"}, "receiveIds": {"type": "string", "description": "以逗号分隔开的用户id列表，表示如果有新消息要通知哪些用户"}, "scheduleId": {"type": "string", "description": "调度quartz id"}}}, "手机邀约配置": {"title": "手机邀约配置", "type": "object", "properties": {"taskCardInterval": {"type": "integer", "description": "单账号每批发卡片间隔，单位分钟", "format": "int32"}, "taskPmInterval": {"type": "integer", "description": "单账号每批私信间隔，单位分钟", "format": "int32"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-device-token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-device-token", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}